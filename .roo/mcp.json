{"mcpServers": {"github.com/supabase-community/supabase-mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_organizations", "list_projects", "list_edge_functions", "list_tables", "get_project_url", "get_anon_key", "apply_migration", "execute_sql"], "alwaysAllow": ["execute_sql"]}}}