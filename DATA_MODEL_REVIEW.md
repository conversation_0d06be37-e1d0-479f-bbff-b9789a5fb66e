# Data Model Review Summary (Client-Side)

This document summarizes the review and finalization of the client-side data model for the application, as defined in `src/lib/types.ts`. Key decisions and the rationale behind changes are outlined for each major entity.

## Core Types & Interfaces

### `UserClassification` and `WorkspaceUserRole`

These types were introduced to differentiate a user's global classification from their role within a specific workspace.

```typescript
export type UserClassification = 'standard' | 'ai_agent' | 'guest' | 'bot';

export type WorkspaceUserRole = 'admin' | 'member';
```

*   **`UserClassification`**: Defines the global nature of a user account (e.g., a standard human user, an AI agent, a guest, or a bot).
*   **`WorkspaceUserRole`**: Defines a user's permissions or standing within a particular workspace (e.g., admin, member).

### `User`

The global `User` interface was updated to include the `classification` and remove the ambiguous workspace-specific `role`.

```typescript
export interface User {
  id: string;
  name: string;
  avatar: string;
  status?: 'online' | 'away' | 'offline';
  title?: string;
  classification?: UserClassification; // Global type of user
  about?: string; // Added about field for profile information
}
```
*   **Key Change**: `role` was removed; `classification` was added.

### `WorkspaceDisplayUser`

This new interface extends `User` to include the `workspaceRole`, representing a user specifically within the context of a workspace.

```typescript
export interface WorkspaceDisplayUser extends User {
  workspaceRole: WorkspaceUserRole;
}
```

### `Workspace`

The `Workspace` interface was updated to use `WorkspaceDisplayUser` for its `users` array and had its `Project`-related fields renamed to `Section`. Client-side state fields for current selections are maintained.

```typescript
export interface Workspace {
  id: string;
  name: string;
  icon?: string;
  users: WorkspaceDisplayUser[]; // Changed from User[]
  sections: Section[];           // Renamed from projects
  directMessages: DirectMessage[];
  currentUserId: string;
  currentSectionId: string | null; // Renamed from currentProjectId
  currentChannelId: string | null; // Renamed from currentTopicId
  currentDirectMessageId: string | null;
  activeThreadId: string | null;
  createdAt?: string;
  settings?: WorkspaceSettings;
}
```
*   **Key Changes**: `users` type updated, `projects` renamed to `sections`, `currentProjectId` to `currentSectionId`, and `currentTopicId` to `currentChannelId`.
*   Denormalized arrays (`users`, `sections`, `directMessages`) are kept for client-side convenience.

### `WorkspaceSettings`

Settings specific to a workspace.

```typescript
export interface WorkspaceSettings {
  defaultSectionId?: string; // Renamed from defaultProjectId
  allowGuestInvites?: boolean;
  retentionDays?: number;
  theme?: string;
}
```
*   **Key Change**: `defaultProjectId` renamed to `defaultSectionId`.

### `Section` (formerly `Project`)

This entity is used to group channels within a workspace. It was renamed from `Project` to `Section` for better generality and alignment with common UI patterns.

```typescript
export interface Section {
  id: string;
  name: string;
  channels: Channel[]; // Groups channels
}
```
*   **Key Change**: Renamed from `Project`. Structure remains the same.

### `Channel`

Represents a communication channel within a `Section`.

```typescript
export interface Channel {
  id: string;
  name: string;
  description?: string;
  isPrivate: boolean;
  members: string[]; // User IDs
  messages: Message[];
  threads: Record<string, Thread>;
  createdAt: string;
  channelTopics?: ChannelTopic[];
  files?: File[];
  pinnedFiles?: string[]; // File IDs
  channelNote?: string;
  activeChannelTopicId?: string; // ID of the active ChannelTopic
}
```
*   **Key Changes**:
    *   The previous singular `channelTopic: string` (for overall purpose) was removed.
    *   `activeChannelTopicId` was added to point to a specific `ChannelTopic` that is currently active/focused in the UI.
*   `members` array (User IDs) and denormalized content arrays (`messages`, `threads`, `files`, `channelTopics`) are suitable for client-side rendering.

### `ChannelTopic`

Defines a structured sub-topic within a `Channel` or `DirectMessage`.

```typescript
export interface ChannelTopic {
  id: string;
  title: string;
  summary: string;
  messageIds: string[]; // References to messages in this topic
  createdAt: string;
}
```
*   `messageIds` is a client-side optimization for quickly finding messages related to a topic. The source of truth remains `Message.topicId`.

### `Message`

Represents a single message within a channel, DM, or thread.

```typescript
export interface Message {
  id: string;
  content: string;
  timestamp: string;
  userId: string;
  reactions?: Reaction[];
  edited?: boolean;
  threadId?: string;
  files?: File[];
  channelId?: string;
  topicId?: string;
  alsoSendToChannel?: boolean; // If true, a thread reply also appears in the main channel
}
```
*   `alsoSendToChannel` clarifies display logic for thread replies.
*   The general structure is comprehensive for client-side needs.

### `Thread`

Represents a thread of replies to a parent message.

```typescript
export interface Thread {
  id: string;
  parentMessageId: string;
  messages: Message[]; // Replies within the thread
}
```
*   `messages` array contains replies. The parent message is identified by `parentMessageId` and typically fetched separately for display in the thread UI.

### `File`

Represents a file.

```typescript
export interface File {
  id: string;
  name: string;
  type: string;
  url: string;
  size: number;
  uploadedBy: string;
  timestamp: string;
  isPinned?: boolean;
  isStarred?: boolean; // For channel-wide starring
}
```
*   **Key Change**: `isStarred?: boolean` added for channel-wide starring status.
*   Files can be attached to messages (`Message.files`) and aggregated at the channel level (`Channel.files`). Application logic handles this aggregation.

### `Reaction`

Represents an emoji reaction to a message.

```typescript
export interface Reaction {
  emoji: string;
  count: number;
  users: string[]; // User IDs
}
```
*   The `emoji: string` field is flexible enough to support any Unicode emoji. UI components will handle the selection of various emojis.

### `DirectMessage`

Represents a direct message conversation, which can be 1-on-1 or a group DM.

```typescript
export interface DirectMessage {
  id: string;
  name?: string; // Optional custom name, primarily for group DMs
  participants: string[];
  messages: Message[];
  threads: Record<string, Thread>;
  topics?: ChannelTopic[];
  activeDmTopicId?: string; // Optional: ID of the active topic within this DM
  createdAt?: string;         // When the DM conversation was initiated
}
```
*   **Key Changes**:
    *   `name?: string` added for optional custom titles, especially for group DMs.
    *   `createdAt?: string` added.
    *   `activeDmTopicId?: string` added for consistency if topics are used in DMs.
*   Supports group DMs via the `participants` array.
*   Kept distinct from `Channel` by not including a DM-level file gallery or notes, focusing on core communication with optional topic organization.

## Conclusion

The client-side data model has been reviewed and refined to improve clarity, accuracy, and support for intended features. It is now considered finalized for current client-side requirements.