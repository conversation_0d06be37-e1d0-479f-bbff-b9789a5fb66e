# Supabase Integration: High-Level Implementation Plan

This document provides high-level guidance for migrating the application's backend functionality to Supabase. It outlines the core architectural components, data structures, and key strategies.

## I. Core Objective

Transition from the current mock data provider (`src/lib/mock-data.ts`) to a fully functional backend powered by Supabase, supporting all existing client-side features including user management, workspaces, channels, messaging, real-time updates, and user preferences.

## II. Supabase Project Foundation

*   **Project Initialization:** A new Supabase project will be created.
*   **Authentication:** Supabase Auth will be the primary mechanism for user authentication (sign-up, login, session management). Email/Password will be the initial method.
*   **API Access:** The Supabase JavaScript client library will be used for all client-server interactions.

## III. High-Level Database Schema Overview

The database will be structured to mirror and expand upon the entities defined in `src/lib/types.ts`. UUIDs will generally be used for primary identifiers.

1.  **`Profiles` (Users)**
    *   **Links to:** Supabase `auth.users` (one-to-one).
    *   **Key Attributes:** `id` (matches `auth.users.id`), `name`, `avatar_url`, `status`, `status_message`, `title`, `classification`, `about`, `settings` (JSONB for `UserSettings`).
    *   **Purpose:** Stores application-specific user information beyond basic auth.

2.  **`Workspaces`**
    *   **Key Attributes:** `id`, `name`, `icon_url`, `settings` (JSONB for `WorkspaceSettings`), `owner_id` (references a Profile).
    *   **Purpose:** Represents distinct collaborative environments.

3.  **`Workspace_Users` (Membership Junction Table)**
    *   **Links:** `Workspaces` and `Profiles` (many-to-many).
    *   **Key Attributes:** `workspace_id`, `user_id`, `role` (`admin`, `member`).
    *   **Purpose:** Manages user membership and roles within workspaces.

4.  **`Sections`**
    *   **Links to:** `Workspaces` (one-to-many).
    *   **Key Attributes:** `id`, `workspace_id`, `name`, `order` (for display sequence).
    *   **Purpose:** Organizes channels within a workspace.

5.  **`Channels`**
    *   **Links to:** `Workspaces`, `Sections`.
    *   **Key Attributes:** `id`, `workspace_id`, `section_id` (optional), `name`, `description`, `is_private`, `created_at`, `channel_note`, `settings` (JSONB for channel-specific views/defaults), `last_message_timestamp`.
    *   **Purpose:** Core communication spaces within workspaces.

6.  **`Channel_Members` (Membership Junction Table)**
    *   **Links:** `Channels` and `Profiles` (many-to-many).
    *   **Key Attributes:** `channel_id`, `user_id`.
    *   **Purpose:** Manages user membership in channels.

7.  **`Channel_Topics`**
    *   **Links to:** `Channels` (one-to-many).
    *   **Key Attributes:** `id`, `channel_id`, `title`, `summary`, `created_at`, `creator_id`.
    *   **Purpose:** Organizes discussions within a channel around specific topics.

8.  **`Messages`**
    *   **Links to:** `Channels` (for channel messages), `Direct_Message_Sessions` (for DM messages), `Profiles` (for sender), `Messages` (for threads, self-referential), `Channel_Topics`.
    *   **Key Attributes:** `id`, `channel_id` (nullable), `dm_id` (nullable), `user_id` (sender), `content`, `timestamp`, `edited_at`, `parent_message_id` (for threads, nullable), `topic_id` (nullable, for channel topics), `also_send_to_channel` (for thread replies).
    *   **Constraint:** Must belong to either a channel or a DM.
    *   **Purpose:** Stores all communication records.

9.  **`Reactions`**
    *   **Links to:** `Messages`, `Profiles`.
    *   **Key Attributes:** `id`, `message_id`, `user_id`, `emoji`.
    *   **Purpose:** Manages user reactions to messages.

10. **`Direct_Message_Sessions` (DM Conversations)**
    *   **Key Attributes:** `id`, `name` (for group DMs), `created_at`, `last_message_timestamp`.
    *   **Purpose:** Represents a direct messaging conversation.

11. **`Direct_Message_Participants` (Membership Junction Table)**
    *   **Links:** `Direct_Message_Sessions` and `Profiles` (many-to-many).
    *   **Key Attributes:** `dm_id`, `user_id`.
    *   **Purpose:** Manages participants in a DM session.

12. **`Files` (Metadata - Storage Implementation Deferred)**
    *   **Links to:** `Profiles` (uploader), `Channels` (if uploaded to a channel), `Messages` (if attached to a message).
    *   **Key Attributes:** `id`, `name`, `type` (MIME), `url` (placeholder), `size_bytes`, `uploaded_by_user_id`, `created_at`, `channel_id` (nullable), `message_id` (nullable), `is_pinned_in_channel_id` (nullable).
    *   **Purpose:** Stores metadata about uploaded files. Actual file storage (e.g., Supabase Storage) integration is deferred.

13. **`User_App_State` (Optional Persistent Client State)**
    *   **Links to:** `Profiles`, `Workspaces`, `Channels`, `Direct_Message_Sessions`, `Messages` (for active thread).
    *   **Key Attributes:** `user_id`, `current_workspace_id`, `current_channel_id`, `current_dm_id`, `active_thread_id`.
    *   **Purpose:** Persists UI state like current selections across sessions/devices if deemed necessary. Often managed client-side.

## IV. Authentication and Authorization Strategy

*   **Authentication:**
    *   Client-side will use `supabase.auth.signUp()`, `signInWithPassword()`, `signOut()`.
    *   The Supabase JS SDK will manage user sessions and JWTs.
    *   A new entry in the `Profiles` table will be automatically created (e.g., via a database trigger) when a new user signs up in `auth.users`.
*   **Authorization (Row Level Security - RLS):**
    *   RLS policies will be the primary mechanism for controlling data access.
    *   **General Principle:** Users can only access/modify data they own or are explicitly granted permission to through their memberships (workspaces, channels, DMs).
    *   Examples:
        *   A user can only see workspaces they are a member of.
        *   A user can only see channels within their accessible workspaces (public channels or private channels they are a member of).
        *   A user can only send messages to channels/DMs they are part of.
        *   Users can only edit/delete their own messages (potentially with time limits).

## V. Real-Time Functionality Strategy

*   **Supabase Realtime:** Leveraged for live updates.
*   **Key Real-time Features:**
    1.  **New Messages:** Clients subscribe to new message insertions in their currently active channel or DM. The subscription payload should ideally contain the full new message data to avoid an extra fetch.
    2.  **Message Edits/Deletions:** Subscriptions for updates/deletions on messages.
    3.  **Reactions:** Live updates for new/removed reactions.
    4.  **User Presence:**
        *   Clients update their status in their `Profiles` record.
        *   Other clients subscribe to status changes on `Profiles` records of relevant users (e.g., members of the current workspace/channel). Supabase's built-in presence capabilities for Realtime channels can also be utilized.
    5.  **Typing Indicators:** Use Supabase Realtime's ephemeral message capabilities on specific channels (e.g., `typing:channel_id`).
*   **Client-Side Handling:** The client will listen to these real-time events and update its local state and UI accordingly.

## VI. Data Synchronization and API Logic (Client-Side)

*   **Data Fetching (Initial & Catch-up):**
    *   On app load/login, fetch essential initial data: user profile, workspaces, DMs.
    *   When navigating to a channel/DM:
        *   **Delta Sync:** Maintain a `last_fetched_message_timestamp` locally per channel/DM. Fetch messages newer than this timestamp.
        *   **Initial Load/Backfill:** If no local timestamp or for older history, fetch the latest `N` messages, implementing pagination/infinite scroll for older messages.
*   **Data Mutation (Create, Update, Delete):**
    *   Use standard Supabase JS client methods (`.insert()`, `.update()`, `.delete()`) for all data modifications.
    *   These operations will be subject to RLS policies.
*   **PostgreSQL Functions (for complex/atomic operations):**
    *   If needed, database functions can be created and called via `supabase.rpc()` for operations requiring atomicity or complex logic best handled server-side (e.g., creating a DM session and adding multiple participants in one go).

## VII. Data Seeding (from `mock-data.ts`)

*   **Purpose:** Populate the development database with initial data for testing and development.
*   **Strategy:**
    1.  Develop a script (e.g., Node.js) that uses the Supabase Admin SDK (with a service role key).
    2.  The script will:
        *   Create mock users in `auth.users` via `supabase.auth.admin.createUser()`.
        *   Transform data from `mock-data.ts` to match the new database schemas, mapping mock user IDs to the newly created Supabase user IDs.
        *   Insert the transformed data into the respective Supabase tables in the correct order to respect foreign key constraints.

## VIII. Client-Side Data Provider Replacement

*   **New Data Layer:** Create a dedicated module (e.g., `src/lib/supabase-data-provider.ts` or similar).
*   **Functions:** This module will encapsulate all Supabase client calls for fetching and mutating data.
*   **Gradual Refactoring:** Systematically replace all usages of `src/lib/mock-data.ts` in UI components and hooks with calls to the new Supabase data provider functions.
*   **State Management:** Ensure client-side state management (e.g., Zustand, Redux, Context) correctly handles loading states, errors, and reflects data fetched from Supabase, including real-time updates.

## IX. E2E CLI Testing Strategy (Conceptual)

*   **Tooling:** A separate command-line interface (CLI) tool, likely built with Node.js and the Supabase JS client.
*   **Purpose:** To automate testing of core user flows and data integrity from an end-to-end perspective.
*   **Key Scenarios to Test:**
    1.  User sign-up and profile creation.
    2.  Workspace creation and user joining.
    3.  Channel creation and user joining (public/private).
    4.  Sending/receiving messages in channels (including threads).
    5.  Sending/receiving direct messages.
    6.  User status updates and visibility to other users.
    7.  Basic RLS enforcement (e.g., user cannot see data from a workspace they are not part of).
*   **Methodology:** The CLI will simulate actions of different test users and verify expected outcomes by querying the database (respecting user context for RLS checks) or observing real-time events.

## X. Future Considerations (Post-Initial Full Migration)

*   **Advanced Search:** Evaluate the need for server-side full-text search capabilities if client-side search becomes insufficient.
*   **Edge Functions:** Implement Supabase Edge Functions for complex backend logic, notifications, or third-party API integrations that are not suitable for client-side or simple database functions.
*   **File Storage:** Fully integrate Supabase Storage for handling file uploads, downloads, and associating them with messages/channels as per the `Files` table schema.
*   **Scalability & Performance Tuning:** As the application grows, monitor performance and implement optimizations such as advanced indexing, query optimization, or exploring Supabase's scaling options.
*   **Enhanced Offline Support:** If deeper offline capabilities are required, investigate more sophisticated client-side caching and synchronization strategies.

This high-level plan provides the roadmap for the Supabase integration. The next phase involves the detailed design and implementation of each component.

## XI. Env

We have added the following to .env already you can leverage for implementation and testing:

VITE_SUPABASE_URL="https://ryqzoxfsvdqokegjmbwj.supabase.co"
VITE_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ5cXpveGZzdmRxb2tlZ2ptYndqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4NDUwMDUsImV4cCI6MjA2MzQyMTAwNX0.HIUYOgdJG0rztzKuZ3_uiH8dlRC87VLNULQJWkbu_Hk"

VITE_TEST_USER_EMAIL="<EMAIL>"
VITE_TEST_USER_PASSWORD="password"

## XII. Implementation Progress & Next Steps (as of 2025-05-21)

This section documents the progress made during the initial Supabase integration session and outlines the immediate path forward.

### A. Supabase Project & Client Setup:
*   **Project Confirmed**: The Supabase project (`id: ryqzoxfsvdqokegjmbwj`, URL: `https://ryqzoxfsvdqokegjmbwj.supabase.co`) is active. API URL and Anon Key are configured via `.env` variables (`VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`).
*   **JS Client Initialized**: The Supabase JavaScript client (`@supabase/supabase-js`) has been installed and is initialized in `src/lib/supabaseClient.ts`.
*   **Database Schema**: Initial exploration confirms that tables like `profiles`, `workspaces`, `channels`, `messages`, etc., exist, generally aligning with the schema outlined in Section III. Primary keys are UUIDs.
*   **Row Level Security (RLS)**: Extensive RLS policies are already active on the database tables.
*   **Edge Functions**: No custom edge functions were observed initially.

### B. Authentication & Authorization:
*   **Client-Side Auth UI**: `src/pages/Auth.tsx` created to handle email/password sign-up and login using the Supabase JS SDK.
*   **Routing**: `/auth` route added to `src/App.tsx`. Unauthenticated users accessing the root path (`/`) are redirected to `/auth`.
*   **Auth Context**: `src/lib/auth-context.tsx` created. `AuthProvider` manages user sessions, authentication state, and fetches the user's profile from `public.profiles`. It uses `supabase.auth.onAuthStateChange` for live updates.
*   **Profile Creation Trigger**: A PostgreSQL function (`public.handle_new_user`) and trigger (`on_auth_user_created`) were added to the Supabase database. This automatically creates a new entry in `public.profiles` when a new user signs up in `auth.users`, populating `name` and `avatar_url` from `raw_user_meta_data`.
*   **Test User Credentials**: `AuthPage.tsx` now pre-fills login details from `VITE_TEST_USER_EMAIL` and `VITE_TEST_USER_PASSWORD` if available.
*   **Auto-Login for Testing (Enhanced 2025-05-22)**: `AuthPage.tsx` was enhanced to automatically submit the login form. In development mode (`import.meta.env.DEV`), it prioritizes `test_email` and `test_password` URL query parameters (e.g., `/auth?test_email=<EMAIL>&test_password=pw`). If URL parameters are not provided, it falls back to using `VITE_TEST_USER_EMAIL` and `VITE_TEST_USER_PASSWORD` from the `.env` file. This allows flexible testing with different users by modifying the URL, bypassing manual form interaction. (Note: When constructing URLs with multiple query parameters for browser automation tools, ensure parameters are separated by a literal `&` and not an HTML entity like `&`, as the latter can cause parsing issues for URL parameter readers like `URLSearchParams`.)

### C. Data Seeding:
*   **Strategy**:
    *   Initial seeding focused on one existing test user (`id: a8fd5e82-c354-4e3b-b765-f2f1f4591dad`, "John Smith") using `execute_sql` MCP tool for basic data.
    *   A Node.js script (`seed_test_users.js`) was developed to programmatically create additional test users (Jane Doe, Alex Johnson, Sam Wilson) using the Supabase Admin SDK (via `service_role_key`). This script handles auth user creation, profile updates, workspace/channel memberships, and DM session creation, referencing `src/lib/mock-data.ts` for user details.
*   **Data Seeded for John Smith (via `execute_sql` MCP tool)**:
    *   John Smith's profile in `public.profiles` updated with mock data details.
    *   "Acme Inc." workspace created (UUID: `10000000-0000-0000-0000-000000000001`), with John Smith as admin.
    *   Sections ("Team Workspace", "Project Alpha (Dev)", "Project Beta (Design)") created within Acme Inc. using predefined UUIDs.
    *   Channels ("general", "random", "dev-team", "design-ux") created within respective sections using predefined UUIDs.
    *   John Smith added as a member to these four channels.
    *   Channel topics created by John Smith in these channels.
    *   Messages sent by John Smith added to these channels/topics.
    *   Metadata for two files uploaded by John Smith added and associated with the "general" channel.
    *   `active_channel_topic_id` updated for "general" and "random" channels.

### D. Client-Side Data Handling:
*   **`supabase-data-provider.ts`**:
    *   Created in `src/lib/`.
    *   `getUserProfile(userId)`: Implemented to fetch a user's profile.
    *   `getInitialWorkspaceDataForUser(userId)`: Partially implemented. It fetches the user's first workspace, its basic details, members (profiles), sections, and channels (with member IDs, but empty message/topic/file arrays for now).
    *   `getMessagesForChannel(channelId)`: Implemented to fetch top-level messages for a channel, including basic sender profile info.
*   **`app-context.tsx` (`AppProvider`)**:
    *   Integrates `AuthContext` to get the authenticated user.
    *   On login, calls `getInitialWorkspaceDataForUser` to load data. If this returns `null` (e.g., new user, error), it falls back to a modified version of `mockWorkspaces['w1a2b3c4d']` where `currentUserId` is the actual logged-in user.
    *   A `useEffect` hook has been added to call `getMessagesForChannel` when the `currentChannelId` (from `workspace.currentChannelId`) changes, updating the channel's messages in the local state.
    *   The `sendMessage` function now:
        *   Constructs a message object for Supabase insertion.
        *   Performs an optimistic update to the local `workspace` state.
        *   Calls `supabase.from('messages').insert()` to save the message to the database.
*   **Type Definitions (`src/lib/types.ts`)**:
    *   `Workspace` interface updated to include `owner_id?: string;`.

### E. RLS Troubleshooting and Resolution (Session of 2025-05-21):

*   **Initial Problem**: Persistent PostgreSQL errors (`"missing FROM-clause entry for table 'is_workspace_member'"` and later `"infinite recursion detected in policy for relation 'workspace_users'"`) prevented `getInitialWorkspaceDataForUser` and `getMessagesForChannel` from loading data from Supabase, causing the app to fall back to mock data.
*   **Root Cause Analysis**:
    *   The `"missing FROM-clause..."` error was traced to RLS policies (particularly on `sections` and `messages` when involved in complex queries with joins) that directly used the SQL function `public.is_workspace_member(column_name)`. It appears PostgreSQL's query planner struggled with this function in RLS policy contexts for these queries.
    *   The `"infinite recursion..."` error was traced to the RLS policy on `workspace_users` when it attempted to define "user can see all members of their workspaces" using an `EXISTS` subquery that referenced `workspace_users` itself. This, possibly in combination with other RLS policies that also queried `workspace_users` (e.g., the `workspaces` policy), led to a recursive loop.
*   **Solution Implemented**:
    1.  **Inlining RLS Logic for SELECT Policies**: For tables like `workspaces`, `sections`, `channels` (SELECT policy), `channel_members`, and `messages` (SELECT policy), RLS policies that previously might have used helper SQL functions (like `public.is_workspace_member()`) were modified to inline the equivalent logic. This typically involves an `EXISTS (SELECT 1 FROM public.workspace_users wu WHERE wu.workspace_id = relevant_table.workspace_id_column AND wu.user_id = auth.uid())` subquery. This resolved the "missing FROM-clause" errors during data fetching.
    2.  **Simplifying `workspace_users` SELECT RLS**: The SELECT RLS policy on `public.workspace_users` ("Users can view workspace members") was simplified to `USING (user_id = auth.uid())`. This resolved the "infinite recursion" error.
    3.  **Inlining RLS Logic for INSERT Policies**:
        *   The INSERT RLS policy on `public.channels` ("Workspace members can create channels"), which likely used `public.is_workspace_member()` in its `WITH CHECK` clause, was modified to use an inlined `EXISTS(...)` subquery checking workspace membership. This resolved the "missing FROM-clause" error during channel creation.
        *   The INSERT RLS policy on `public.messages` ("Users can send messages to channels and DMs they have access to"), which likely used a helper function like `is_channel_member()` (that itself might have called `is_workspace_member()`) in its `WITH CHECK` clause, was modified. The problematic function call was replaced with inlined `EXISTS(...)` subqueries for checking both workspace membership (via the channel) and direct channel membership. This resolved the "missing FROM-clause" error during message sending.
    4.  **`profiles` SELECT RLS**: The SELECT RLS policy on `public.profiles` ("Users can view all profiles") was set to `USING (auth.role() = 'authenticated')`.
    5.  **`direct_message_sessions` SELECT RLS**: The SELECT RLS policy ("Users can view DM sessions they participate in"), which used `is_dm_participant()`, was updated to use inlined `EXISTS(...)` logic checking `direct_message_participants`. This resolved visibility issues for DMs at the database level.
    6.  **`handle_new_user` Trigger**: Corrected the trigger to use `NEW.raw_user_meta_data->>'name'` (instead of `->>'full_name'`) to ensure correct profile name seeding. Existing test user profiles were manually updated.
    7.  **DM Data Fetching (Client)**: `getInitialWorkspaceDataForUser` in `supabase-data-provider.ts` now correctly fetches DM sessions and consolidates all DM participant profiles into the main `workspace.users` list. `app-context.tsx` correctly stores this data. `Sidebar.tsx` now lists DMs with correct participant names.
    8.  **DM Message Fetching (Client)**: `getMessagesForDirectMessage` function added to `supabase-data-provider.ts`, and `app-context.tsx` now calls it when a DM is selected to load messages for that DM.
*   **Outcome**: RLS policies for SELECT operations on core tables are stable. Test users are seeded correctly with proper names. DM sessions are listed correctly in the UI. Channel message sending and creation work.
*   **Key Learning**:
    *   Direct use of SQL helper functions within RLS policies is highly problematic. Inlining logic is more robust.
    *   `SECURITY DEFINER` functions with `SET search_path = ''` and fully qualified names are necessary if helper functions are used in RLS or complex backend logic.
    *   Persistent errors like "missing FROM-clause entry for table new" when defining `INSERT` RLS policies can indicate deep issues, potentially requiring workarounds like RPC calls for inserts.
    *   **Current Blocker**: Sending messages in Direct Messages (DMs) fails with a 403 Forbidden error. This is due to the `INSERT` RLS policy on `public.messages` not correctly authorizing inserts for DMs. Attempts to fix this policy (even with helper functions or minimal definitions using `new.column_name`) have consistently failed with a "missing FROM-clause entry for table new" error when applied via the MCP tool. Even when the user applied a corrected policy directly in the Supabase SQL editor, the 403 error on DM insert persists in the app.
*   **Resolution for Message Sending (DM and Channel)**:
    *   The persistent 403 errors and "missing FROM-clause" issues with the `INSERT` RLS policy on `public.messages` (especially for DMs) necessitated a shift in strategy.
    *   **Solution**: A `SECURITY DEFINER` PostgreSQL database function (RPC) named `public.send_message(p_content TEXT, p_workspace_id UUID, p_channel_id UUID, p_dm_id UUID, p_parent_message_id UUID, p_topic_id UUID)` was implemented.
        *   This function is a **database stored function**, not a Supabase Edge Function.
        *   It internally performs permission checks by calling the existing helper functions (`public.is_allowed_to_send_to_channel` and `public.is_allowed_to_send_to_dm`).
        *   If authorized, it performs the `INSERT` into `public.messages`. The `workspace_id` is not directly inserted into `messages` as it's derived via channel/DM relationships; the `p_workspace_id` parameter is for potential use in helper functions.
    *   **Client-Side Update**: The `sendMessage` function in `src/lib/app-context.tsx` was refactored to call `supabase.rpc('send_message', ...)` instead of `supabase.from('messages').insert(...)`.
    *   **RLS Simplification**: The `INSERT` RLS policy on `public.messages` was simplified to `WITH CHECK (user_id = auth.uid())`, as the primary authorization logic is now handled within the RPC.
    *   **Outcome**: This approach successfully resolved the message sending issues for both DMs and channels.
    *   **Key Takeaway**: For complex write authorizations (like message sending involving checks on related tables), using a `SECURITY DEFINER` database RPC function is a more robust and reliable pattern than attempting to implement all logic within RLS `INSERT` or `UPDATE` policies, especially when encountering persistent RLS evaluation issues.

### F. Current Status & Next Steps (as of 2025-05-21, with updates from 2025-05-22 planning):

Core data fetching, RLS policies for SELECT, and message sending via RPC are stable. Test users are seeded. DMs are listed and messages load. UI behavior on tab focus has been improved.

The immediate next steps focus on enhancing the chat experience, particularly around unread message handling and real-time updates.

**1. Implement Unread Badge Persistence (High Priority for UX)**
    *   **Problem**: Unread message badges are lost on app reload because their state is not persisted.
    *   **Phase 1: Client-Side Persistence (Immediate Goal)**
        *   **Strategy**: Use `localStorage` to store unread status per user, per conversation.
        *   **Details**:
            *   A new state variable `persistentUnreadInfo` in `app-context.tsx` will hold `Record<conversationId, { unreadCount: number; lastReadMessageTimestamp?: string; }>`.
            *   This state will be loaded from `localStorage` (e.g., `unreadStatus_USER_ID`) on app start (when `authUser` is available) and saved back whenever it changes.
            *   When `loadWorkspaceData` in `app-context.tsx` initializes the `workspace` state:
                *   For each channel/DM, its `unreadCount` will be initialized based on `persistentUnreadInfo` and by comparing the channel/DM's `lastMessageTimestamp` (from DB) with the `lastReadMessageTimestamp` stored in `persistentUnreadInfo`.
                *   If `db.lastMessageTimestamp > localStorage.lastReadMessageTimestamp`, it indicates new messages arrived while the app was closed. The `unreadCount` will be set to `(localStorage.unreadCount || 0) + 1` (or a similar heuristic) to ensure the badge shows.
            *   The Realtime message handler in `app-context.tsx` will update both the `workspace` state's `unreadCount` and the `persistentUnreadInfo` for incoming messages in non-active conversations.
            *   Functions like `markConversationRead`, `setCurrentChannel`, and `setCurrentDirectMessage` in `app-context.tsx` will be updated to:
                *   Set `unreadCount: 0` in both `workspace` state and `persistentUnreadInfo`.
                *   Update `lastReadMessageTimestamp` in `persistentUnreadInfo` with the timestamp of the latest message in the conversation. This requires ensuring the latest message's timestamp is available when these functions are called (messages for the active conversation should be loaded).
        *   **Current Message Fetching Context**:
            *   `getInitialWorkspaceDataForUser` fetches channel/DM metadata including `last_message_timestamp` (global for the conversation, from DB) but does not fetch messages.
            *   `getMessagesForChannel`/`getMessagesForDirectMessage` fetch all top-level messages for a conversation when it's selected. This is when the exact `lastReadMessageTimestamp` can be determined.
            *   The `last_message_timestamp` on `Channels` and `Direct_Message_Sessions` tables is global, not per-user. The client-persisted `lastReadMessageTimestamp` is per-user (and per-browser for Phase 1).
    *   **Phase 2: Server-Side Persistence (Future Enhancement)**
        *   **Strategy**: Store read states in a dedicated database table (e.g., `user_conversation_read_states`) to enable cross-device sync.
        *   **Details**: Involves schema changes, RPC functions for marking read, and updating data fetching logic to calculate unread counts based on server-stored read states.

**2. Implement Real-time Message Updates (High Priority for Chat Experience - In Progress)**:
    *   The `useEffect` hook for Supabase Realtime on the `messages` table in `app-context.tsx` is partially implemented.
    *   **Next Steps**:
        *   Ensure robust handling of new messages: adding to local state, updating `unreadCount` (for both `workspace` state and `persistentUnreadInfo` as per item 1), and correctly identifying target channel/DM/thread.
        *   Handle potential duplicates if optimistic updates are also in place (current logic seems to attempt this).
        *   Thoroughly test multi-user scenarios.

**3. Enhance Message Fetching Strategy (Medium Priority, Post Unread/Realtime Basics)**
    *   **Problem**: Currently, `getMessagesForChannel` and `getMessagesForDirectMessage` fetch all top-level messages, which is inefficient for long histories.
    *   **Proposed Solution (as per original plan document - Section VI)**:
        *   Implement **Delta Sync**: Maintain a `last_fetched_message_timestamp` locally (e.g., in `localStorage` or `IndexedDB`) per channel/DM. Modify fetching functions to retrieve messages newer than this timestamp.
        *   Implement **Initial Load/Backfill**: For conversations with no local timestamp or for fetching older history, fetch the latest `N` messages and implement pagination/infinite scroll.
    *   **Relation to Unread Logic**: This is complementary. The `lastReadMessageTimestamp` for unread status and a `lastFetchedMessageTimestamp` for sync would work together.

**4. Verify Downstream Functionality (Ongoing)**:
    *   Thoroughly test DM message sending, optimistic updates, and display in `MessageList.tsx`.
    *   Re-verify channel message sending and display.
    *   Test multi-user interactions extensively.

**5. Address React Hooks Warning (Resolved 2025-05-22)**:
    *   **Issue**: Console warning `Warning: React has detected a change in the order of Hooks called by MainContent.`
    *   **Resolution**: The warning was caused by calling `useApp()` (which uses `useContext`) inside `.filter()` and `.map()` methods within the `MainContent.tsx` component when rendering direct message participant names. This violated the Rules of Hooks.
    *   **Fix**: Modified `MainContent.tsx` to use the `workspace` object already destructured from a top-level `useApp()` call, instead of calling `useApp()` again within the array methods. This ensures hooks are called in a consistent order. The warning is now resolved.

**6. Address `workspace_users` RLS Policy Limitation (Potential Future Action)**:
    *   The current `workspace_users` SELECT policy (`USING (user_id = auth.uid())`) is restrictive.
    *   **Action**: If broader visibility of workspace members is needed, investigate and implement a more advanced RLS solution without reintroducing recursion.

**7. Complete Data Fetching in `getInitialWorkspaceDataForUser` (Ongoing)**:
    *   Ensure `directMessages` fetching includes all necessary participant profile details and `last_message_timestamp`.
    *   Fetch and populate `channelTopics` for each channel.
    *   Consider fetching initial messages for the default/last active channel (once message fetching strategy in item 3 is improved).

**8. Implement Thread Fetching/Display**:
    *   Modify `getMessagesForChannel`/`DM` to also fetch replies (messages with `parent_message_id`), ideally with pagination.
    *   Update UI components to display threads.

**9. Refactor Client-Side Data Usage**:
    *   Systematically replace all remaining direct usages of `mock-data.ts` in components with data from `useApp()` context.

**10. Implement Core CRUD for Other Entities**:
    *   Workspace Creation, Channel Creation, Topic Creation, etc.

**11. Review and Refine Assumed RLS Policies**:
    *   Review existing RLS policies against application requirements.

**12. File Storage**: Integrate Supabase Storage for actual file uploads.

This updated plan should provide a clear overview of what's been accomplished and the path forward.

### G. UI Behavior Enhancements (Session of 2025-05-22) - Completed:

*   **Problem Addressed**: The application exhibited an aggressive "reload" behavior whenever the browser tab regained focus. This was perceived as a full app refresh, even for brief tab switches.
*   **Root Cause Analysis**:
    1.  **Explicit Workspace Data Refresh**: `src/lib/app-context.tsx` contained a `useEffect` hook (triggered by `useVisibilityChange`) that re-fetched all initial workspace data (`getInitialWorkspaceDataForUser`) every time the tab became visible.
    2.  **Timed Loading Overlay**: The same `app-context.tsx` also displayed a timed loading overlay (`AppLoadingScreen variant="minimal"`) for 1 second on every tab refocus, again triggered by `useVisibilityChange`.
    3.  **Auth Profile Re-fetch**: `src/lib/auth-context.tsx` was re-fetching the user's profile on tab focus due to Supabase's default session refresh behavior.
*   **Solution Implemented**:
    1.  **In `src/lib/app-context.tsx`**:
        *   The `useEffect` hook responsible for the full workspace data re-fetch on tab focus (previously lines 234-285) was commented out.
        *   The conditional rendering block that showed the timed minimal loading screen (previously lines 1170-1182) was removed.
    2.  **In `src/lib/auth-context.tsx`**:
        *   Throttling logic was added to the user profile fetching mechanism. The profile is now only re-fetched if the user changes, no profile is currently loaded, or if more than 15 minutes have passed since the last successful fetch for the current user.
*   **Outcome**: These changes significantly reduce the "app reload" sensation on tab focus. The application now relies more on existing real-time subscriptions for data updates and avoids unnecessary full data re-fetches and disruptive loading indicators during frequent tab switches, leading to a smoother user experience.

### H. Refactoring `app-context.tsx` (Session of 2025-05-22):
*   **Objective**: To improve maintainability and readability of the large `src/lib/app-context.tsx` file.
*   **Strategy**: Extract pure state update logic into utility functions within a new file, `src/lib/app-context-utils.ts`.
*   **Actions Taken**:
    1.  Created `src/lib/app-context-utils.ts`.
    2.  Moved the following logic from `app-context.tsx` into utility functions in `app-context-utils.ts`:
        *   `deduplicateDirectMessages`: For cleaning up DM lists.
        *   `findSectionById`, `findChannelById`: For locating entities in workspace data.
        *   `transformSupabaseMessage`: For converting raw Supabase message objects (from Realtime or RPC) to the client-side `Message` type.
        *   `findOrCreateThreadFromContext`: For finding or initializing a `Thread` object.
        *   `updateOptimisticMessageInArray`: For updating a temporary optimistic message with its final version from the database.
        *   `mergeWorkspaceData`: For merging newly loaded workspace data with existing state while preserving dynamic content.
        *   `applyRealtimeMessageToWorkspace`: For processing incoming Realtime messages and updating the workspace state (adding messages, updating unread counts).
        *   `applyOptimisticMessageUpdate`, `revertOptimisticMessageUpdate`: For applying and reverting optimistic message additions in the UI.
        *   `applyReactionUpdateToWorkspace`: For updating message reactions in the state.
        *   `applyAddChannelUpdateToWorkspace`, `applyAddSectionUpdateToWorkspace`, `applyAddDirectMessageUpdateToWorkspace`: For state updates related to adding new entities.
        *   `applyUpdateChannelToWorkspace`: For updating an existing channel's data.
        *   `applyMarkConversationReadToWorkspace`: For marking channels or DMs as read (clearing unread counts).
        *   `applyUpdateUserSettingToWorkspace`, `applyUpdateWorkspaceSettingsToWorkspace`, `applyUpdateUserStatusToWorkspace`: For updating user and workspace settings/status.
        *   `applyNavigateToChannelTopicToWorkspace`, `applyClearActiveChannelTopicToWorkspace`: For managing active topics within channels.
        *   `applySetCurrentSectionToWorkspace`, `applySetCurrentChannelToWorkspace`, `applySetCurrentDirectMessageToWorkspace`, `applySetActiveThreadToWorkspace`: For handling navigation and active item selections.
    3.  Updated `src/lib/app-context.tsx` to import and use these utility functions, significantly reducing the inline complexity of its state update logic.
    4.  Corrected type definitions in `src/lib/types.ts` (e.g., `Message.edited` to `Message.editedTimestamp`) and ensured necessary types were imported into `app-context-utils.ts` to resolve TypeScript errors.
*   **Outcome**: The `setWorkspace` calls within `AppProvider` are now much simpler, delegating complex state transformations to the well-defined utility functions in `app-context-utils.ts`. This improves the modularity, readability, and testability of the application's core context logic.
*   **Next Steps for Refactoring**: Further refactoring could involve creating custom React Hooks (e.g., `useNavigation`, `useRealtime`, `useSearch`) to encapsulate related blocks of state, effects, and actions currently still in `app-context.tsx`.

## XIII. Revised Implementation Roadmap & Future Enhancements (as of 2025-05-22)

This section outlines the revised and more comprehensive roadmap based on a thorough review conducted on 2025-05-22. It addresses key weaknesses in message fetching/synchronization, unread message tracking, persistence of user/workspace settings, feature gaps compared to mock data, offline support, and incorporates a strategy for E2E testing.

### A. Overall Goals for Revised Roadmap:
*   Achieve efficient and scalable message fetching and synchronization.
*   Implement robust and persistent unread message tracking (client-side initially, then server-side).
*   Ensure user data, preferences, and workspace settings are persisted on the backend.
*   Bridge feature gaps identified by comparing with `mock-data.ts`.
*   Improve offline capabilities and graceful error handling for network issues.
*   Establish a comprehensive E2E testing strategy for ongoing development and stability.

### B. Phased Implementation Plan:

#### Phase 0: Critical Backend Fixes (Pre-requisites)

**Goal:** Address foundational backend issues necessary for reliable client-side functionality.

*   **Task 0.1: Implement DB Trigger for `last_message_timestamp`**
    *   **Description:** Create a PostgreSQL trigger on the `messages` table. After an `INSERT` into `messages`, this trigger should update the `last_message_timestamp` column on the corresponding `channels` or `direct_message_sessions` table to the `timestamp` of the new message.
    *   **Rationale:** Provides a reliable server-side indicator of the latest activity in a conversation, crucial for delta sync and initial unread calculation.
    *   **Dev Tasks (Server - SQL):**
        1.  Write the SQL for the trigger function.
        2.  Write the SQL to create the trigger on the `messages` table.
        3.  Test thoroughly.
        4.  Add these to `supabase/triggers.sql` (or a new `supabase/migrations/xxxx_update_last_message_timestamp_trigger.sql` file and update `migrations_list.json`).
    *   **E2E Test:** Verify `last_message_timestamp` updates correctly after sending messages to channels and DMs.

#### Phase 1: Core Messaging & Unread Stability (Client Focus)

**Goal:** Make message loading efficient, unread status persistent across sessions (on the same browser), and improve basic network error resilience.

*   **Task 1.1: Client-Side Persistence for Unread Status (localStorage)**
    *   **Description:** Implement the `persistentUnreadInfo` strategy using `localStorage` as previously outlined (Plan XII.F.1). Store `Record<conversationId, { unreadCount: number; lastReadMessageTimestamp: string; }>`.
    *   **Rationale:** Provides session persistence for unread badges on the same browser, significantly improving UX.
    *   **Dev Tasks (Client - `app-context.tsx` / `app-context-utils.ts`):**
        1.  Add `persistentUnreadInfo` state to `AppContext`.
        2.  Load `persistentUnreadInfo` from `localStorage` (e.g., key `unreadStatus_USER_ID`) on app start (when `authUser` is available).
        3.  Save `persistentUnreadInfo` to `localStorage` whenever it changes.
        4.  **Initial Unread Calculation:** In `loadWorkspaceData` (after `getInitialWorkspaceDataForUser` and Task 0.1 is done), for each channel/DM:
            *   Retrieve its `lastReadMessageTimestamp` and `unreadCount` from the loaded `persistentUnreadInfo`.
            *   Compare the DB's `last_message_timestamp` (now reliably updated by Task 0.1) with the stored `lastReadMessageTimestamp`.
            *   If `db.lastMessageTimestamp > localStorage.lastReadMessageTimestamp`, it means new messages arrived offline. Calculate initial `unreadCount` for the `workspace` state (e.g., `(localStorage.unreadCount || 0) + 1` or a more sophisticated count if possible).
        5.  Update `applyRealtimeMessageToWorkspace` to also update the corresponding entry in `persistentUnreadInfo` when `unreadCount` in `workspace` state is incremented.
        6.  Update `markConversationRead` / `applyMarkConversationReadToWorkspace`: When a conversation is marked read, set `unreadCount: 0` in `persistentUnreadInfo` and update `lastReadMessageTimestamp` in `persistentUnreadInfo` to the timestamp of the latest message in that conversation.
        7.  Ensure `setCurrentChannel` / `setCurrentDirectMessage` call `markConversationRead` appropriately.
    *   **E2E Test:** Verify unread badges persist after reload; verify initial unread calculation if messages arrived while the client was "offline" (simulated by manipulating localStorage or message timestamps).

*   **Task 1.2: Basic Message Pagination/Limit (Client & Server)**
    *   **Description:** Modify `getMessagesForChannel` and `getMessagesForDirectMessage` to fetch only the latest `N` messages (e.g., N=50) initially. Implement a "Load More" button or basic scroll-to-top trigger to fetch the next `N` older messages.
    *   **Rationale:** Prevents fetching huge message histories at once, improving initial load time for conversations.
    *   **Dev Tasks (Client - `supabase-data-provider.ts`):**
        1.  Modify fetching functions to accept `limit` and `cursorTimestamp` (or `offset`) parameters.
        2.  Update Supabase queries to use `.limit(N)` and `.order('timestamp', { ascending: false })` for initial fetch (latest N), and then use `.lt('timestamp', oldestFetchedTimestamp).order('timestamp', { ascending: false })` for subsequent "load older" fetches.
    *   **Dev Tasks (Client - `app-context.tsx` & UI components):**
        1.  Manage state for `oldestFetchedTimestamp` per conversation.
        2.  Add UI for "Load More" or implement infinite scroll.
        3.  Update `setWorkspace` logic to prepend older messages to the existing message list for a conversation.
    *   **E2E Test:** Verify only N messages load initially; verify "load more" fetches and prepends older messages correctly.

*   **Task 1.3: Basic Network Error Handling in Data Fetching**
    *   **Description:** Wrap critical Supabase calls in `supabase-data-provider.ts` (especially `getInitialWorkspaceDataForUser`, `getMessagesForChannel`/`DM`) in try/catch blocks to handle network errors more gracefully.
    *   **Rationale:** Prevents unhandled promise rejections and allows the app to inform the user or use fallback data more cleanly, addressing issues seen in console logs (e.g., `TypeError: Failed to fetch`).
    *   **Dev Tasks (Client - `supabase-data-provider.ts`, `app-context.tsx`):**
        1.  Implement `try/catch` around Supabase client calls.
        2.  If a fetch fails, return a specific error object or `null` consistently.
        3.  `app-context.tsx` should check for these failures:
            *   On initial load failure, continue falling back to mock data (as it currently does) but display a non-intrusive toast: "Failed to load live data. Displaying cached/placeholder content. Please check your connection."
            *   For message fetch failures (when opening a channel/DM), show a toast: "Failed to load messages. Please check your connection." and potentially display an empty state or previously cached messages (once caching from Phase 2 is in).
            *   Handle Realtime subscription errors by attempting reconnection with backoff and notifying the user if persistently failing.
    *   **E2E Test (Manual/Simulated):** Simulate offline mode (e.g., browser dev tools) and observe app behavior during initial load and when opening a channel. Verify toasts and fallback mechanisms.

#### Phase 2: Enhanced Sync, Server-Side Unread Foundation & Offline Foundation

**Goal:** Implement true delta sync for messages, lay the groundwork for server-side unread tracking, and establish basic offline viewing capabilities using a client-side cache.

*   **Task 2.1: Implement Delta Sync for Messages**
    *   **Description:** Enhance message fetching to only get messages newer than the `last_fetched_message_timestamp` stored locally per conversation (from `localStorage` initially, then potentially IndexedDB).
    *   **Rationale:** Minimizes data transfer and ensures client only fetches what's new since its last sync for that conversation.
    *   **Dev Tasks (Client - `supabase-data-provider.ts`):**
        1.  Modify `getMessagesForChannel`/`DM` to accept a `sinceTimestamp` parameter.
        2.  Update Supabase queries: `select(...).gt('timestamp', sinceTimestamp).order('timestamp', { ascending: true })`.
    *   **Dev Tasks (Client - `app-context.tsx` / `app-context-utils.ts`):**
        1.  Store `last_fetched_message_timestamp` per conversation (e.g., in `localStorage` alongside `persistentUnreadInfo`, or in IndexedDB later).
        2.  When a conversation is opened, call `getMessagesForChannel`/`DM` with the stored `last_fetched_message_timestamp`.
        3.  Append new messages and update the `last_fetched_message_timestamp` to the timestamp of the newest message received.
        4.  Combine with pagination: fetch new messages via delta sync, then allow paginating older messages.
    *   **E2E Test:** Verify only new messages are fetched when reopening a channel after some new messages have arrived.

*   **Task 2.2: Introduce IndexedDB for Client-Side Caching**
    *   **Description:** Implement a basic IndexedDB cache (e.g., using `dexie.js`) for fetched messages (per conversation), user profiles, and essential workspace/channel/DM structure.
    *   **Rationale:** Enables viewing of previously loaded data when offline and can lead to faster perceived loads even when online.
    *   **Dev Tasks (Client):**
        1.  Select and integrate an IndexedDB library.
        2.  Define IndexedDB schemas for messages, profiles, workspace/channel/DM metadata.
        3.  Modify `supabase-data-provider.ts` and `app-context.tsx`:
            *   On successful Supabase fetch, write/update data in IndexedDB.
            *   When data is requested (e.g., initial load, opening a conversation):
                *   Attempt to load from IndexedDB first and display immediately (optimistic rendering from cache).
                *   Concurrently, attempt to fetch fresh data from Supabase.
                *   If Supabase fetch succeeds, update IndexedDB and merge fresh data into the UI state.
                *   If Supabase fetch fails (e.g., offline), the UI continues to show cached data. Display a clear "Offline Mode - Showing cached data" indicator.
    *   **E2E Test:** Load data, go offline, reload app – verify previously loaded channels/messages are viewable. Go back online, verify data syncs and updates.

*   **Task 2.3: Design and Implement `user_conversation_read_states` Table (Server)**
    *   **Description:** Create a new table in Supabase: `user_conversation_read_states` with columns: `user_id (fk to profiles)`, `conversation_id (uuid, can be channel_id or dm_id)`, `conversation_type (text, 'channel' | 'dm')`, `last_read_message_timestamp (timestamptz)`. Primary Key: `(user_id, conversation_id)`.
    *   **Rationale:** This is the foundation for server-side, cross-device unread message synchronization.
    *   **Dev Tasks (Server - SQL):**
        1.  Define table schema.
        2.  Implement RLS: Users can only R/W their own read states.
        3.  Add to `supabase/migrations/` and update `migrations_list.json`.

*   **Task 2.4: Implement RPC to Update `user_conversation_read_states` (Server)**
    *   **Description:** Create an RPC function `mark_conversation_as_read(p_conversation_id uuid, p_conversation_type text, p_last_read_message_timestamp timestamptz)`. This function will `UPSERT` into `user_conversation_read_states` for the `auth.uid()` user.
    *   **Rationale:** Allows clients to inform the server about their read progress for cross-device sync.
    *   **Dev Tasks (Server - SQL):**
        1.  Write the `SECURITY DEFINER` RPC function.
        2.  Add to `supabase/rpc_functions.sql`.

*   **Task 2.5: Integrate Server-Side Read State Tracking (Client)**
    *   **Description:**
        *   Modify client to call the `mark_conversation_as_read` RPC when a conversation is read (in addition to client-side updates for immediate UX).
        *   On initial load (`getInitialWorkspaceDataForUser`), fetch the user's read states from `user_conversation_read_states`.
        *   Use this server-fetched read state to calculate initial unread counts by comparing with `channels.last_message_timestamp` / `direct_message_sessions.last_message_timestamp`. This can replace or augment the `localStorage` based initial unread calculation from Task 1.1.
        *   The `localStorage` `persistentUnreadInfo` can still act as a more immediate cache or be gradually phased out for the `lastReadMessageTimestamp` part if RPC calls are performant.
    *   **Rationale:** Enables cross-device unread sync.
    *   **Dev Tasks (Client - `supabase-data-provider.ts`, `app-context.tsx`, `app-context-utils.ts`):**
        1.  Add function to `supabase-data-provider.ts` to fetch all `user_conversation_read_states` for the current user.
        2.  Call this in `getInitialWorkspaceDataForUser` (or a separate effect).
        3.  Modify `markConversationRead` in `app-context.tsx` to call the new RPC.
        4.  Adjust unread calculation logic in `mergeWorkspaceData` or initial load sequence to use server-fetched read states.
    *   **E2E Test:** Unread status syncs correctly across two simulated clients/browsers after one marks a conversation read.

#### Phase 3: Feature Completeness & Backend Persistence

**Goal:** Implement backend persistence for remaining entities and features identified as gaps (vs. `mock-data.ts` or app vision).

*   **Task 3.1: Persist User Profile Details & Settings**
    *   **Description:** Update `updateUserStatus` and `updateUserSetting` in `app-context.tsx` to call Supabase client methods (`.update()`) to modify the `profiles` table (specific columns for status/title/about, and the `settings` JSONB column for `UserSettings`).
    *   **Rationale:** Persist user customizations beyond basic auth info.
    *   **Dev Tasks (Client - `app-context.tsx`, `supabase-data-provider.ts` potentially):** Add Supabase `.update()` calls. Ensure RLS on `profiles` allows users to update their own record (`id = auth.uid()`).
    *   **E2E Test:** User A updates their profile status/title/a specific setting. Reload app/login as user A, verify changes persist. Verify user B cannot update user A's profile.

*   **Task 3.2: Persist Workspace Settings**
    *   **Description:** Update `updateWorkspaceSettings` in `app-context.tsx` to call Supabase client (`.update()`) to modify the `workspaces.settings` JSONB column.
    *   **Rationale:** Persist workspace customizations.
    *   **Dev Tasks (Client):** Add Supabase `.update()` call. Ensure RLS on `workspaces` allows workspace admins to update their workspace's settings.
    *   **E2E Test:** Workspace admin updates a workspace setting. Other members see the effect (if applicable, e.g., default theme). Setting persists on reload for all members. Non-admin cannot update.

*   **Task 3.3: Implement Backend for Section Creation & Management**
    *   **Description:** Modify `addSection` to insert into the `sections` table via Supabase client. Add functionality for updating (name, order) and deleting sections.
    *   **Rationale:** Persist sections and allow management.
    *   **Dev Tasks (Client & Server):** Add Supabase `.insert()`, `.update()`, `.delete()` calls. Ensure RLS on `sections` allows creation/update/delete (e.g., by workspace admins). Handle `display_order`.
    *   **E2E Test:** Create, rename, reorder, delete section. Verify persistence and RLS.

*   **Task 3.4: Implement Backend for DM Creation**
    *   **Description:** Modify `addDirectMessage`. If a DM session for the participant pair doesn't exist, call an RPC `create_direct_message_session(target_user_id uuid)` which creates a new `direct_message_sessions` record and adds both current user and target user to `direct_message_participants`. The RPC should return the new DM session details.
    *   **Rationale:** Persist DM sessions properly.
    *   **Dev Tasks (Server - SQL):** Create `create_direct_message_session` RPC. It should check for existing sessions first.
    *   **Dev Tasks (Client):** Call RPC, update state with DB-returned DM.
    *   **E2E Test:** User A creates DM with User B. Both users see it. Persists on reload. Attempting to create again navigates to existing DM.

*   **Task 3.5: Implement Backend for Reactions**
    *   **Description:**
        *   Modify `addReaction` to `UPSERT` into `reactions` table (or use separate add/remove RPCs for atomicity if needed, e.g., `toggle_reaction(p_message_id uuid, p_emoji text)`).
        *   Modify message fetching (initial, paginated, delta sync) to also fetch aggregated reaction data for messages (e.g., using a database view or join: `message_id, emoji, count, user_ids_array`).
    *   **Rationale:** Persist reactions, show existing reactions from all users.
    *   **Dev Tasks (Server - SQL):** Potentially a view `message_reactions_summary (message_id, emoji, count, user_ids_array)`. RLS on `reactions` table (users can add/delete their own).
    *   **Dev Tasks (Client):** Update `addReaction` with Supabase call. Update message fetching to include reaction data. Update UI to display fetched reactions.
    *   **E2E Test:** User A adds a reaction. Reload, reaction persists. User B sees User A's reaction (in real-time if RT for reactions is added, or on next message fetch/refresh). User A removes reaction, it's gone for all.

*   **Task 3.6: Implement Backend for Channel Topics & Notes**
    *   **Description:**
        *   **Channel Topics:** Add functions to fetch `channel_topics` for a channel. Implement RPCs or direct table operations (with RLS) for creating, updating, deleting `channel_topics`.
        *   **Channel Notes:** Add function to update `channels.channel_note` via Supabase client.
    *   **Rationale:** Feature completeness based on `mock-data.ts` and app design.
    *   **Dev Tasks (Client & Server):**
        *   Topics: CRUD functions in `supabase-data-provider.ts`, corresponding Supabase calls (direct or RPCs), RLS on `channel_topics` (e.g., channel members can create, creator/admin can edit/delete).
        *   Notes: Update function in `supabase-data-provider.ts`, Supabase `.update()` call, RLS on `channels` (e.g., channel members or admins can edit note).
    *   **E2E Test:** Create/edit/delete a channel topic. Edit a channel note. Verify persistence and visibility according to RLS.

*   **Task 3.7: Implement Backend for Workspace Creation**
    *   **Description:** Add UI and backend logic for a user to create a new workspace. This involves inserting into `workspaces` and `workspace_users` (making the creator an admin).
    *   **Rationale:** Core application functionality.
    *   **Dev Tasks (Client & Server):** UI flow. RPC `create_workspace(p_name text, p_icon_url text default null)` that handles inserts into `workspaces` and `workspace_users`. RLS on `workspaces` (user can create), `workspace_users` (RPC handles initial admin).
    *   **E2E Test:** User creates a new workspace. It appears in their list. They are an admin.

#### Phase 4: Advanced Features & Refinements

**Goal:** Implement remaining major features and perform optimizations.

*   **Task 4.1: Full File Storage Integration (Supabase Storage)**
    *   **Description:** Integrate Supabase Storage for file uploads associated with messages/channels. Update `files` table with actual storage object URLs. Implement file fetching/display/deletion.
    *   **Rationale:** Core feature from original plan (Section X).
    *   **Dev Tasks (Client & Server):**
        *   Client: UI for upload/download. Use Supabase Storage JS client.
        *   Server: RLS for Storage buckets (e.g., users can upload to paths related to channels/DMs they are part of).
        *   Update `files` table CRUD to link to storage objects.
    *   **E2E Test:** Upload file to a channel message. Another member downloads/views it. Delete file.

*   **Task 4.2: Real-time for Other Entities (as needed)**
    *   **Description:** Add Supabase Realtime subscriptions for changes to user presence/status, workspace settings, channel metadata (name, description), reactions, etc.
    *   **Rationale:** Richer real-time experience beyond just new messages.
    *   **Dev Tasks (Client & Server):** Identify entities needing RT. Set up subscriptions. Handle payloads in `app-context.tsx`.
    *   **E2E Test:** User A changes status; User B sees it update in real-time. Admin changes workspace theme; members see it update.

*   **Task 4.3: Advanced Search (Server-Side if needed)**
    *   **Description:** Evaluate if current client-side search (which iterates over locally held messages) is sufficient. If not, implement server-side search using Postgres Full-Text Search on the `messages.content` (and other relevant fields) or an Edge Function.
    *   **Rationale:** Scalable and performant search across all messages, not just locally loaded ones.
    *   **Dev Tasks (Server & Client):** If server-side, create DB functions/indexes or Edge Function. Update client to call this search endpoint.
    *   **E2E Test:** Search for terms across entire message history; verify results are accurate and performant.

*   **Task 4.4: Use `user_app_state` Table (Optional but Recommended)**
    *   **Description:** If cross-device UI state persistence (current channel, active theme, sidebar state, etc.) is desired, integrate reading/writing to the `user_app_state` table.
    *   **Rationale:** Consistent UX across devices/sessions.
    *   **Dev Tasks (Client & Server):**
        *   Client: On state changes (e.g., `currentChannelId`, theme), call RPC to update `user_app_state`. On load, fetch from `user_app_state` to initialize UI.
        *   Server: RLS on `user_app_state` (user R/W own state).
    *   **E2E Test:** User A sets a theme and navigates to a channel. Logs out. Logs in on a different browser/device. Verify theme and last channel are restored.

### C. E2E Testing Strategy - General Principles:

*   **Tooling:** Utilize a Node.js-based test runner (e.g., Jest) combined with the Supabase JS client. For UI-specific interactions that are hard to test programmatically via API/DB, Playwright can be considered for full browser automation.
*   **Test User Management:** Employ a system for creating and managing dedicated test users with different roles and permissions. The existing `seed_test_users.js` can be expanded for this.
*   **Data Isolation:** Ensure tests clean up after themselves or run against a dedicated test database/schema to maintain a consistent state. Supabase branching could be useful here.
*   **Scope & Types of Tests:**
    1.  **API/RPC Level Tests:** Directly call `supabase-data-provider.ts` functions or RPCs and assert database state changes or returned data. Excellent for testing CRUD and RLS for specific data operations.
        *   *Example (User Settings Update):*
            1.  Setup: Create test user A.
            2.  Action: As user A, call client function to update theme to 'dark'.
            3.  Assertion: Query `profiles` table (as admin/service_role) to verify `settings->>'theme'` is 'dark' for user A.
            4.  RLS Check: As test user B, attempt to update user A's theme; expect RLS error.
    2.  **RLS Policy Validation:** Design specific scenarios to directly test RLS policies.
        *   *Example (Channel Visibility):*
            1.  Setup: Create Workspace W, Channel C in W. User A is member of W, User B is not.
            2.  Action: As User A, attempt to select Channel C; expect success.
            3.  Action: As User B, attempt to select Channel C; expect RLS to prevent access (empty result or error).
    3.  **Core User Flow Simulation (CLI/Programmatic):** Simulate sequences of actions.
        *   *Example (Basic Message Send):*
            1.  Setup: User A and User B in Channel C.
            2.  Action: As User A, send message M to Channel C.
            3.  Assertion (User A): Message M appears in User A's client state (optimistic). Query DB, message M exists.
            4.  Assertion (User B): Message M is received via Realtime (if testing RT) or fetched on next poll/channel open.
    4.  **Offline Scenario Simulation (Challenging for CLI, better with UI E2E):**
        *   Programmatically, one could simulate by having the client load data, then prevent further network calls while asserting against cached (IndexedDB) data.
*   **Test Structure:** Organize tests by feature module (e.g., `auth.e2e.test.js`, `messaging.e2e.test.js`, `userSettings.e2e.test.js`).
*   **CI/CD Integration:** Automate test execution in the CI/CD pipeline to catch regressions early.
*   **Coverage:** Aim for high coverage of critical paths, RLS policies, and data modification points.
