# RLS Policy Update Implementation Plan

**Objective:**
1.  Update the SELECT RLS policy on `public.workspace_users` to allow workspace members to see each other.
2.  Review other RLS policy files and change `TO public` to `TO authenticated` for policies where access should be restricted to logged-in users.

**Phase 1: Update `public.workspace_users` SELECT Policy**
*   [x] **Step 1.1:** Read [`supabase/rls_workspace_users.sql`](supabase/rls_workspace_users.sql) to understand its current content and identify the existing SELECT policy for `public.workspace_users`.
*   [x] **Step 1.2:** Construct new content for [`supabase/rls_workspace_users.sql`](supabase/rls_workspace_users.sql).
    *   Keep existing INSERT, UPDATE, DELETE policies if they are appropriate.
    *   Replace the existing SELECT policy with the new one.
*   [x] **Step 1.3:** Write updated content to [`supabase/rls_workspace_users.sql`](supabase/rls_workspace_users.sql).

**Phase 2: Review and Update `TO public` to `TO authenticated` in other RLS files**
For each of the following files:
    *   [`supabase/rls_channels.sql`](supabase/rls_channels.sql) - Updated
    *   [`supabase/rls_messages.sql`](supabase/rls_messages.sql) - Updated
    *   [`supabase/rls_sections.sql`](supabase/rls_sections.sql) - Updated
    *   [`supabase/rls_profiles.sql`](supabase/rls_profiles.sql) - Updated
    *   [`supabase/rls_channel_members.sql`](supabase/rls_channel_members.sql) - Updated
    *   [`supabase/rls_direct_message_participants.sql`](supabase/rls_direct_message_participants.sql) - Updated
    *   [`supabase/rls_direct_message_sessions.sql`](supabase/rls_direct_message_sessions.sql) - Updated
    *   [`supabase/rls_files.sql`](supabase/rls_files.sql) - Updated
    *   [`supabase/rls_reactions.sql`](supabase/rls_reactions.sql) - Updated
    *   [`supabase/rls_user_app_state.sql`](supabase/rls_user_app_state.sql) - Updated
    *   [`supabase/rls_workspaces.sql`](supabase/rls_workspaces.sql) - Updated
    *   [`supabase/rls_channel_topics.sql`](supabase/rls_channel_topics.sql) (Added from file list) - Updated

*   [x] **Step 2.1:** For each file:
    *   [ ] Read the file.
    *   [ ] Identify policies with `TO public`.
    *   [ ] If a policy has `TO public` and should be restricted, change it to `TO authenticated` using `search_and_replace`.
    *   [ ] Record the file as modified if changes were made.

**Phase 3: Reporting**
*   [ ] **Step 3.1:** Report paths of all modified files.
*   [ ] **Step 3.2:** Confirm `workspace_users` SELECT policy update.
*   [ ] **Step 3.3:** List other RLS files where `TO public` was changed.

**Modified Files Log:**
* [`supabase/rls_workspace_users.sql`](supabase/rls_workspace_users.sql)
* [`supabase/rls_channels.sql`](supabase/rls_channels.sql)
* [`supabase/rls_messages.sql`](supabase/rls_messages.sql)
* [`supabase/rls_sections.sql`](supabase/rls_sections.sql)
* [`supabase/rls_profiles.sql`](supabase/rls_profiles.sql)
* [`supabase/rls_channel_members.sql`](supabase/rls_channel_members.sql)
* [`supabase/rls_direct_message_participants.sql`](supabase/rls_direct_message_participants.sql)
* [`supabase/rls_direct_message_sessions.sql`](supabase/rls_direct_message_sessions.sql)
* [`supabase/rls_files.sql`](supabase/rls_files.sql)
* [`supabase/rls_reactions.sql`](supabase/rls_reactions.sql)
* [`supabase/rls_user_app_state.sql`](supabase/rls_user_app_state.sql)
* [`supabase/rls_workspaces.sql`](supabase/rls_workspaces.sql)
* [`supabase/rls_channel_topics.sql`](supabase/rls_channel_topics.sql)