// seed_test_users.js
import { createClient } from '@supabase/supabase-js';
import { v4 as uuidv4 } from 'uuid';

// Load environment variables from .env file
import dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const serviceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !serviceRoleKey) {
  console.error(
    'Error: VITE_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY is not defined in your .env file.'
  );
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY are set.');
  process.exit(1);
}

const supabaseAdmin = createClient(supabaseUrl, serviceRoleKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
});

console.log('Supabase Admin client initialized.');

// --- Data from mock-data.ts (simplified for seeding) ---
const mockUsersRaw = [
  {
    id: 'u1d9e0f7a', // <PERSON>
    name: '<PERSON>e',
    avatar: 'https://ui-avatars.com/api/?name=Jane+Doe&background=random',
    status: 'online',
    title: 'Product Manager',
    emailPrefix: 'jane.doe',
  },
  {
    id: 'u5c2b8d4e', // <PERSON> <PERSON>
    name: 'Alex Johnson',
    avatar: 'https://ui-avatars.com/api/?name=Alex+Johnson&background=random',
    status: 'away',
    title: 'UX Designer',
    emailPrefix: 'alex.johnson',
  },
  {
    id: 'ue0f1c2d3', // Sam Wilson
    name: 'Sam Wilson',
    avatar: 'https://ui-avatars.com/api/?name=Sam+Wilson&background=random',
    status: 'offline',
    title: 'Backend Developer',
    emailPrefix: 'sam.wilson',
  }
];

const acmeWorkspaceUserRoles = { // mockId: role
  'u1d9e0f7a': 'member', 
  'u5c2b8d4e': 'member', 
  'ue0f1c2d3': 'member', 
  'u8fbc62ae': 'admin',  
};

// --- IDs for existing seeded data (Updated with correct UUIDs from DB) ---
const acmeWorkspaceId = '10000000-0000-0000-0000-000000000001';
const generalChannelId = '*************-0000-0000-000000000001'; 
const devTeamChannelId = '*************-0000-0000-000000000003'; 

const johnSmithUserId = 'a8fd5e82-c354-4e3b-b765-f2f1f4591dad'; 

const testUsersToSeed = mockUsersRaw.map(mockUser => ({
  email: `${mockUser.emailPrefix}@example.com`,
  password: 'password123', 
  profileData: {
    name: mockUser.name,
    avatar_url: mockUser.avatar,
    status: mockUser.status,
    title: mockUser.title,
    status_message: `Hello, I'm ${mockUser.name}!`, 
  },
  workspaceRole: acmeWorkspaceUserRoles[mockUser.id] || 'member',
  mockId: mockUser.id, 
}));


async function main() {
  console.log('Starting test user seeding script based on mock-data.ts...');
  const createdUserMockIdToSupabaseId = {}; 

  for (const userData of testUsersToSeed) {
    console.log(`\nProcessing user: ${userData.email} (mock ID: ${userData.mockId})`);
    let userId;

    let { data: authUserResponse, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email: userData.email,
      password: userData.password,
      email_confirm: true,
      user_metadata: {
        name: userData.profileData.name,
        avatar_url: userData.profileData.avatar_url,
      }
    });

    if (authError) {
      if (authError.message === 'A user with this email address has already been registered') {
        console.warn(`User ${userData.email} already exists in auth.users. Fetching ID using listUsers().`);
        // Fetch user by email using listUsers (admin method)
        const { data: usersList, error: listError } = await supabaseAdmin.auth.admin.listUsers({ query: userData.email, limit: 1 });
        
        if (listError || !usersList || usersList.users.length === 0) {
            console.error(`Failed to fetch existing user ${userData.email} via listUsers(). Error:`, listError?.message);
            console.log('Full listUsers response:', usersList); // Log full response for debugging
            continue; 
        }
        // Ensure the found user's email matches exactly, as listUsers with query can be broad
        const foundUser = usersList.users.find(u => u.email === userData.email);
        if (!foundUser) {
            console.error(`User ${userData.email} not found with exact email match via listUsers().`);
            continue;
        }
        userId = foundUser.id;
        console.log(`Found existing user ${userData.email} with ID: ${userId}`);
      } else { 
        console.error(`Error creating auth user ${userData.email}:`, authError.message);
        continue;
      }
    } else {
      userId = authUserResponse.user.id;
      console.log(`Auth user ${userData.email} created successfully with ID: ${userId}`);
    }
    createdUserMockIdToSupabaseId[userData.mockId] = userId;

    if (!userId) {
        console.error(`Could not obtain userId for ${userData.email}. Skipping further processing for this user.`);
        continue;
    }

    const { error: profileUpdateError } = await supabaseAdmin
      .from('profiles')
      .update({
        status: userData.profileData.status,
        status_message: userData.profileData.status_message,
        title: userData.profileData.title,
      })
      .eq('id', userId);

    if (profileUpdateError) {
      console.error(`Error updating profile for ${userData.email} (ID: ${userId}):`, profileUpdateError.message);
    } else {
      console.log(`Profile for ${userData.email} updated.`);
    }

    const { error: workspaceMemberError } = await supabaseAdmin
      .from('workspace_users')
      .insert({
        workspace_id: acmeWorkspaceId,
        user_id: userId,
        role: userData.workspaceRole,
      });

    if (workspaceMemberError) {
      if (workspaceMemberError.message.includes('duplicate key value violates unique constraint')) {
        console.warn(`User ${userData.email} is already a member of workspace ${acmeWorkspaceId}.`);
      } else {
        console.error(`Error adding ${userData.email} to workspace ${acmeWorkspaceId}:`, workspaceMemberError.message);
      }
    } else {
      console.log(`User ${userData.email} added to workspace ${acmeWorkspaceId} as ${userData.workspaceRole}.`);
    }
    
    let channelsToJoinForThisUser = [];
    if (['u1d9e0f7a', 'u5c2b8d4e', 'ue0f1c2d3'].includes(userData.mockId)) { 
        channelsToJoinForThisUser.push(generalChannelId);
    }
    if (['ue0f1c2d3'].includes(userData.mockId)) { 
        channelsToJoinForThisUser.push(devTeamChannelId);
    }

    for (const channelId of channelsToJoinForThisUser) {
      if (!channelId) {
        console.warn(`Skipping channel join for ${userData.email} due to undefined channelId.`);
        continue;
      }
      console.log(`Attempting to add user ${userId} to channel ${channelId}`);
      const { error: channelMemberError } = await supabaseAdmin
        .from('channel_members')
        .insert({ channel_id: channelId, user_id: userId });
      if (channelMemberError) {
        if (channelMemberError.message.includes('duplicate key value violates unique constraint')) {
          console.warn(`User ${userData.email} is already a member of channel ${channelId}.`);
        } else {
          console.error(`Error adding ${userData.email} to channel ${channelId}: ${channelMemberError.message}. Details: ${JSON.stringify(channelMemberError)}`);
        }
      } else {
        console.log(`User ${userData.email} added to channel ${channelId}.`);
      }
    }
  } 

  const janeSupabaseId = createdUserMockIdToSupabaseId['u1d9e0f7a'];
  const alexSupabaseId = createdUserMockIdToSupabaseId['u5c2b8d4e'];

  if (johnSmithUserId && janeSupabaseId) {
    const dmJaneJohnId = uuidv4(); 
    console.log(`\nAttempting to create DM session ${dmJaneJohnId} between Jane (${janeSupabaseId}) and John Smith (${johnSmithUserId})`);
    
    const {error: dmError} = await supabaseAdmin.from('direct_message_sessions').insert({id: dmJaneJohnId, name: `DM Jane & John`});
    if(dmError) {
        if (dmError.message.includes('duplicate key value violates unique constraint')) {
            console.warn(`DM session for Jane & John might already exist (tried ID ${dmJaneJohnId}).`);
        } else {
            console.error("Error inserting DM session Jane & John:", dmError.message);
        }
    } else {
      const participants = [
        { dm_id: dmJaneJohnId, user_id: janeSupabaseId },
        { dm_id: dmJaneJohnId, user_id: johnSmithUserId }
      ];
      for (const p of participants) {
        const { error: pError } = await supabaseAdmin.from('direct_message_participants').upsert(p, { onConflict: 'dm_id,user_id' });
        if (pError) console.error(`Error adding participant ${p.user_id} to DM ${dmJaneJohnId}:`, pError.message);
      }
      console.log(`DM session ${dmJaneJohnId} between Jane and John Smith ensured.`);
    }
  }

  if (johnSmithUserId && alexSupabaseId) {
    const dmAlexJohnId = uuidv4(); 
    console.log(`\nAttempting to create DM session ${dmAlexJohnId} between Alex (${alexSupabaseId}) and John Smith (${johnSmithUserId})`);
    const {error: dmError} = await supabaseAdmin.from('direct_message_sessions').insert({id: dmAlexJohnId, name: `DM Alex & John`});
    if(dmError) {
        if (dmError.message.includes('duplicate key value violates unique constraint')) {
            console.warn(`DM session for Alex & John might already exist (tried ID ${dmAlexJohnId}).`);
        } else {
            console.error("Error inserting DM session Alex & John:", dmError.message);
        }
    } else {
      const participants = [
        { dm_id: dmAlexJohnId, user_id: alexSupabaseId },
        { dm_id: dmAlexJohnId, user_id: johnSmithUserId }
      ];
      for (const p of participants) {
        const { error: pError } = await supabaseAdmin.from('direct_message_participants').upsert(p, { onConflict: 'dm_id,user_id' });
        if (pError) console.error(`Error adding participant ${p.user_id} to DM ${dmAlexJohnId}:`, pError.message);
      }
      console.log(`DM session ${dmAlexJohnId} between Alex and John Smith ensured.`);
    }
  }

  console.log('\nTest user seeding script completed.');
}

main().catch((e) => {
  console.error('Unhandled error in main script execution:', e);
});
