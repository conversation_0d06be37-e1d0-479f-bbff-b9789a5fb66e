import React, { useEffect, useState } from 'react';
import { Sidebar } from './Sidebar';
import { MainContent } from './MainContent';
import { ThreadPanel } from './ThreadPanel';
import TopBar from './TopBar'; // Added TopBar import
import { SearchView } from './SearchView'; // Added SearchView import
import { useApp } from '@/lib/app-context';
import { PanelLeft, Menu } from 'lucide-react'; // Removed Plus as NewMessageDialog is separate, Removed Info
import { CommandDialog } from './CommandDialog';
import { Button } from './ui/button';
// import { ChannelDetailsDialog } from './ChannelDetailsDialog'; // Removed
import { NewMessageDialog } from './NewMessageDialog';
import { KeyboardShortcutsProvider } from '@/hooks/use-keyboard-shortcuts';
import { KeyboardShortcutsDialog2 } from './KeyboardShortcutsDialog2';
import { UserPreferencesDialog } from './UserPreferencesDialog';
import { WorkspaceSettingsDialog } from './WorkspaceSettingsDialog';

export const AppLayout = () => {
  // State for dialogs
  const [isUserPreferencesOpen, setIsUserPreferencesOpen] = useState(false);
  const [isWorkspaceSettingsDialogOpen, setIsWorkspaceSettingsDialogOpen] = useState(false);

  // Expose the component to window for keyboard shortcuts
  useEffect(() => {
    (window as any).__APP_COMPONENT__ = {
      setIsUserPreferencesOpen: (open: boolean) => setIsUserPreferencesOpen(open),
      setIsWorkspaceSettingsDialogOpen: (open: boolean) => setIsWorkspaceSettingsDialogOpen(open)
    };
    return () => {
      delete (window as any).__APP_COMPONENT__;
    };
  }, []);
  const {
    currentThread,
    toggleSidebar,
    isSidebarOpen,
    setActiveThread,
    currentChannel, // Changed from currentTopic
    // getCurrentUser, // We'll get user from workspace
    workspace, // Get the whole workspace object
    workspaceSettings, // Retain for workspace-level theme default, now includes showTopBar
    isSearchViewActive, // Added for controlling search view
    setIsSearchViewActive, // Added for toggling search view
  } = useApp();
  const showTopBar = workspaceSettings?.showTopBar ?? true; // Default to true if not set
  const [showCommandDialog, setShowCommandDialog] = useState(false);
  // const [showChannelDetails, setShowChannelDetails] = useState(false); // Removed
  const [showNewMessageDialog, setShowNewMessageDialog] = useState(false);
  const [showKeyboardShortcutsDialog, setShowKeyboardShortcutsDialog] = useState(false);

  const currentUser = workspace.users.find(u => u.id === workspace.currentUserId);
  let themeToApply = currentUser?.settings?.themeOverride || workspace.settings?.theme || 'default';

  // Map 'light' and 'system' to 'default' as they are not explicitly defined themes with CSS
  if (themeToApply === 'light' || themeToApply === 'system') {
    themeToApply = 'default';
  }

  const activeTheme = themeToApply;

  // Apply theme to body
  useEffect(() => {
    // More robustly remove any existing theme-prefixed classes
    const bodyClasses = document.body.className.split(' ').filter(c => !c.startsWith('theme-'));
    document.body.className = bodyClasses.join(' ');
    document.body.classList.remove('dark'); // Always remove dark initially

    // Always add a theme class, including theme-default
    document.body.classList.add(`theme-${activeTheme}`);

    // Special handling for dark themes to also add generic .dark class for shadcn/ui compatibility
    if (
      activeTheme === 'midnight-blue' ||
      activeTheme === 'graphite' ||
      activeTheme === 'gradient-big-business' ||
      activeTheme === 'dark' // Handle the initial 'dark' case
    ) {
      document.body.classList.add('dark');
    }
  }, [activeTheme, workspace]);

  // Add keyboard listener for global shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't trigger shortcuts when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Cmd/Ctrl + K to open command dialog
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault();
        setShowCommandDialog(true);
      }

      // Cmd/Ctrl + B to toggle sidebar
      if ((e.metaKey || e.ctrlKey) && e.key === 'b') {
        e.preventDefault();
        toggleSidebar();
      }

      // ESC to close thread
      if (e.key === 'Escape' && currentThread) {
        e.preventDefault();
        setActiveThread(null);
      }

      // Cmd/Ctrl + W for workspace switcher
      if ((e.metaKey || e.ctrlKey) && e.key === 'w') {
        e.preventDefault();
        const workspaceTrigger = document.getElementById('workspace-trigger');
        if (workspaceTrigger) {
          workspaceTrigger.click();
        }
      }

      // Keyboard shortcuts are now handled by the useKeyboardShortcuts hook
      // We only need to handle shortcuts that need to be handled at the AppLayout level

      // Shift+? or Ctrl+/ for keyboard shortcuts help
      if ((e.key === '?' && e.shiftKey) ||
          (e.key === '/' && (e.ctrlKey || e.metaKey))) {
        e.preventDefault();
        setShowKeyboardShortcutsDialog(true);
      }
    };

    // Use capture phase to ensure we get events before other handlers
    window.addEventListener('keydown', handleKeyDown, true);
    return () => window.removeEventListener('keydown', handleKeyDown, true);
  }, [toggleSidebar, currentThread, setActiveThread, currentChannel, isSearchViewActive]);

  return (
    <KeyboardShortcutsProvider>
      <div className="flex h-screen overflow-hidden">
        <div className={`transition-transform duration-300 ease-in-out ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full'}`}>
          {isSidebarOpen && <Sidebar />}
        </div>
        <div className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ease-in-out ${isSidebarOpen ? '' : 'pl-0'}`}>
          {showTopBar && <TopBar />}
          {/* The div that previously acted as a placeholder for main-header when TopBar was off is removed.
              When showTopBar is false, no header is rendered here.
              The sidebar toggle button is handled by MainContent.tsx.
          */}
          <div className="flex-1 flex overflow-hidden">
            {isSearchViewActive ? (
              <SearchView />
            ) : (
              <>
                <MainContent />
                {currentThread && (
                  <div className="animate-slide-in">
                    <ThreadPanel />
                  </div>
                )}
              </>
            )}
          </div>
        </div>

        {/* Command Dialog (Cmd/Ctrl + K) */}
        <CommandDialog open={showCommandDialog} onOpenChange={setShowCommandDialog} />

        {/* Theme Settings dialog is now part of UserPreferencesDialog, accessed via Sidebar's profile menu */}

        {/* Channel Details Dialog - REMOVED FROM HERE (handled by MainContent) */}
        {/* {currentChannel && (
          <ChannelDetailsDialog
            open={showChannelDetails}
            onOpenChange={setShowChannelDetails}
            topic={currentChannel}
          />
        )} */}

        {/* New Message Dialog */}
        <NewMessageDialog
          open={showNewMessageDialog}
          onOpenChange={setShowNewMessageDialog}
        />

        {/* Keyboard Shortcuts Dialog */}
        <KeyboardShortcutsDialog2
          open={showKeyboardShortcutsDialog}
          onOpenChange={setShowKeyboardShortcutsDialog}
        />

        {/* User Preferences Dialog */}
        <UserPreferencesDialog
          open={isUserPreferencesOpen}
          onOpenChange={setIsUserPreferencesOpen}
        />

        {/* Workspace Settings Dialog */}
        <WorkspaceSettingsDialog
          open={isWorkspaceSettingsDialogOpen}
          onOpenChange={setIsWorkspaceSettingsDialogOpen}
        />
      </div>
    </KeyboardShortcutsProvider>
  );
};
