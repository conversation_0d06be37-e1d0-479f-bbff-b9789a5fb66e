import React from 'react';
import { Skeleton } from './ui/skeleton';
import { Progress } from './ui/progress';
import { Spinner } from './ui/spinner';

interface AppLoadingScreenProps {
  message?: string;
  progress?: number;
  variant?: 'minimal' | 'skeleton' | 'progress';
  className?: string;
}

export const AppLoadingScreen: React.FC<AppLoadingScreenProps> = ({
  message = 'Loading application data...',
  progress,
  variant = 'skeleton',
  className = '',
}) => {
  // For minimal variant - just a subtle spinner overlay
  if (variant === 'minimal') {
    return (
      <div className={`fixed inset-0 z-50 bg-background/30 backdrop-blur-[1px] flex items-center justify-center ${className}`}>
        <div className="bg-background/80 rounded-full p-3 shadow-md">
          <Spinner size="md" color="muted" />
        </div>
      </div>
    );
  }

  // For progress variant - shows a subtle progress indicator
  if (variant === 'progress') {
    return (
      <div className={`fixed inset-0 z-50 bg-background/30 backdrop-blur-[1px] flex items-center justify-center ${className}`}>
        <div className="bg-background/80 rounded-lg p-4 shadow-md w-64">
          <Progress value={progress ?? 66} className="h-1.5 mx-auto" />
        </div>
      </div>
    );
  }

  // For skeleton variant - shows a skeleton UI of the app layout
  return (
    <div className={`flex h-screen overflow-hidden bg-background ${className}`}>
      {/* Sidebar skeleton */}
      <div className="w-64 h-full border-r border-border bg-sidebar">
        <div className="p-4">
          <Skeleton className="h-8 w-40 mb-6" />

          {/* Workspace selector */}
          <div className="flex items-center mb-6">
            <Skeleton className="h-8 w-8 rounded-full mr-2" />
            <Skeleton className="h-5 w-32" />
          </div>

          {/* Sections */}
          <div className="space-y-6">
            {Array.from({ length: 3 }).map((_, sectionIndex) => (
              <div key={`section-${sectionIndex}`} className="space-y-2">
                <Skeleton className="h-5 w-24 mb-2" />
                {Array.from({ length: 3 }).map((_, channelIndex) => (
                  <Skeleton
                    key={`channel-${sectionIndex}-${channelIndex}`}
                    className="h-6 w-full"
                  />
                ))}
              </div>
            ))}
          </div>

          {/* Direct messages */}
          <div className="mt-6 space-y-2">
            <Skeleton className="h-5 w-32 mb-2" />
            {Array.from({ length: 4 }).map((_, index) => (
              <div key={`dm-${index}`} className="flex items-center">
                <Skeleton className="h-6 w-6 rounded-full mr-2" />
                <Skeleton className="h-6 flex-1" />
              </div>
            ))}
          </div>
        </div>

        {/* User section */}
        <div className="absolute bottom-0 w-64 p-4 border-t border-border">
          <div className="flex items-center">
            <Skeleton className="h-8 w-8 rounded-full mr-2" />
            <Skeleton className="h-5 w-32" />
          </div>
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top bar */}
        <div className="h-14 border-b border-border p-4 flex items-center justify-between">
          <div className="flex items-center">
            <Skeleton className="h-6 w-6 mr-2" />
            <Skeleton className="h-6 w-32" />
          </div>
          <div className="flex items-center space-x-2">
            <Skeleton className="h-6 w-6" />
            <Skeleton className="h-6 w-6" />
            <Skeleton className="h-6 w-6" />
          </div>
        </div>

        {/* Message area */}
        <div className="flex-1 p-4 overflow-hidden">
          <div className="space-y-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <div key={`message-${index}`} className="flex">
                <Skeleton className="h-8 w-8 rounded-full mr-2 flex-shrink-0" />
                <div className="space-y-2 flex-1">
                  <div className="flex items-center">
                    <Skeleton className="h-4 w-24 mr-2" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};
