import React, { KeyboardEvent, useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from './ui/dialog';
import { Separator } from './ui/separator';
import { Button } from './ui/button';
import { useApp } from '@/lib/app-context';
import { findUserById } from '@/lib/mock-data';
import { Hash, Users, PenLine, Plus, LogOut, Settings2 } from 'lucide-react'; // Added Settings2
import { Channel, User, ChannelViewKey, UserSettings } from '@/lib/types'; // Added ChannelViewKey, UserSettings
import { Input } from './ui/input';
import { Label } from './ui/label'; // Added Label import
import { Textarea } from './ui/textarea';
import { Checkbox } from './ui/checkbox'; // Added Checkbox
import { 
  Tabs, 
  Ta<PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from './ui/tabs';
import { useToast } from '@/hooks/use-toast';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from './ui/sheet';
import { Form, FormControl, FormField, FormItem } from './ui/form';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';

interface ChannelDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  topic: Channel; 
}

const ALL_CHANNEL_VIEWS: ChannelViewKey[] = ['Messages', 'Topics', 'Files', 'Members', 'Note'];

export const ChannelDetailsDialog = ({ open, onOpenChange, topic: channel }: ChannelDetailsDialogProps) => {
  const { workspace, updateChannel, getCurrentUser, updateUserSetting } = useApp(); // Corrected to updateUserSetting
  const currentUser = getCurrentUser();
  const { toast } = useToast();
  
  const [nameEditMode, setNameEditMode] = useState<boolean>(false);
  const [descriptionEditMode, setDescriptionEditMode] = useState<boolean>(false);
  const [channelTopicEditMode, setChannelTopicEditMode] = useState<boolean>(false);
  
  const [userChannelViews, setUserChannelViews] = useState<ChannelViewKey[]>([]);
  const [name, setName] = useState<string>(channel.name);
  const [description, setDescription] = useState<string>(channel.description || '');

  let initialTopicSummary = '';
  let effectiveActiveTopicIdForState = channel.activeChannelTopicId;
  if (!effectiveActiveTopicIdForState && channel.channelTopics && channel.channelTopics.length > 0) {
    effectiveActiveTopicIdForState = channel.channelTopics[0].id;
  }
  if (effectiveActiveTopicIdForState) {
    const topicToDisplay = channel.channelTopics?.find(t => t.id === effectiveActiveTopicIdForState);
    initialTopicSummary = topicToDisplay?.summary || topicToDisplay?.title || '';
  }
  const [channelTopicText, setChannelTopicText] = useState<string>(initialTopicSummary);
  const [activeTab, setActiveTab] = useState<string>('about');
  const [showAddMemberSheet, setShowAddMemberSheet] = useState<boolean>(false);
  
  const nameInputRef = useRef<HTMLInputElement>(null);
  const descriptionTextareaRef = useRef<HTMLTextAreaElement>(null);
  const topicTextareaRef = useRef<HTMLTextAreaElement>(null);
  
  useEffect(() => {
    if (nameEditMode && nameInputRef.current) nameInputRef.current.focus();
  }, [nameEditMode]);
  
  useEffect(() => {
    if (descriptionEditMode && descriptionTextareaRef.current) descriptionTextareaRef.current.focus();
  }, [descriptionEditMode]);
  
  useEffect(() => {
    if (channelTopicEditMode && topicTextareaRef.current) topicTextareaRef.current.focus();
  }, [channelTopicEditMode]);

  useEffect(() => {
    setName(channel.name);
    setDescription(channel.description || '');
    let currentEffectiveActiveTopicId = channel.activeChannelTopicId;
    if (!currentEffectiveActiveTopicId && channel.channelTopics && channel.channelTopics.length > 0) {
        currentEffectiveActiveTopicId = channel.channelTopics[0].id;
    }
    if (currentEffectiveActiveTopicId) {
        const currentActiveTopic = channel.channelTopics?.find(t => t.id === currentEffectiveActiveTopicId);
        setChannelTopicText(currentActiveTopic?.summary || currentActiveTopic?.title || '');
    } else {
        setChannelTopicText(''); 
    }

    // Determine initial channel views for the user
    const userOverrides = currentUser.settings?.channelViewOverrides?.[channel.id];
    const channelDefault = channel.channelSpecificDefaultViews;
    const workspaceDefault = workspace.settings?.defaultChannelViews;
    
    if (userOverrides && userOverrides.length > 0) {
      setUserChannelViews(userOverrides);
    } else if (channelDefault && channelDefault.length > 0) {
      setUserChannelViews(channelDefault);
    } else if (workspaceDefault && workspaceDefault.length > 0) {
      setUserChannelViews(workspaceDefault);
    } else {
      setUserChannelViews(ALL_CHANNEL_VIEWS); // Fallback to all views
    }

  }, [channel, channel.activeChannelTopicId, channel.channelTopics, currentUser.settings, workspace.settings]);
  
  const memberDetails = channel.members.map(memberId => findUserById(memberId)).filter(Boolean);
  const availableUsers = workspace.users.filter(user => !channel.members.includes(user.id));
  const createdDate = channel.createdAt ? new Date(channel.createdAt) : new Date();
  const formattedDate = createdDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long', day: 'numeric' });

  const handleSaveName = () => {
    if (!name.trim()) {
      toast({ title: "Error", description: "Channel name cannot be empty.", variant: "destructive" });
      return;
    }
    if (updateChannel) updateChannel({ ...channel, name: name.trim() });
    toast({ title: "Channel name updated", description: `Channel name changed to #${name.trim()}` });
    setNameEditMode(false);
  };
  
  const handleSaveDescription = () => {
    if (updateChannel) updateChannel({ ...channel, description: description.trim() });
    toast({ title: "Description updated", description: "Your changes have been saved successfully." });
    setDescriptionEditMode(false);
  };

  const handleSaveTopic = () => {
    let topicIdToUpdate = channel.activeChannelTopicId;
    if (!topicIdToUpdate && channel.channelTopics && channel.channelTopics.length > 0) {
      topicIdToUpdate = channel.channelTopics[0].id;
    }
    if (!topicIdToUpdate) {
      toast({ title: "Error", description: "No topic available to update.", variant: "destructive" });
      setChannelTopicEditMode(false);
      return;
    }
    if (updateChannel) {
      const updatedChannelTopics = channel.channelTopics?.map(topic =>
        topic.id === topicIdToUpdate ? { ...topic, summary: channelTopicText.trim() } : topic
      );
      const finalChannelTopics = updatedChannelTopics || (topicIdToUpdate && !channel.channelTopics ? [{id: topicIdToUpdate, title: "Topic 1", summary: channelTopicText.trim(), messageIds: [], createdAt: new Date().toISOString()}] : []);
      updateChannel({ ...channel, channelTopics: finalChannelTopics, activeChannelTopicId: topicIdToUpdate });
      toast({ title: "Topic summary updated", description: "The topic's summary has been updated." });
    }
    setChannelTopicEditMode(false);
  };
  
  const handleNameKeyDown = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') handleSaveName();
    else if (e.key === 'Escape') { setName(channel.name); setNameEditMode(false); }
  };
  
  const handleDescriptionKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) handleSaveDescription();
    else if (e.key === 'Escape') { setDescription(channel.description || ''); setDescriptionEditMode(false); }
  };
  
  const handleTopicKeyDown = (e: KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey)) handleSaveTopic();
    else if (e.key === 'Escape') {
      let currentEffectiveActiveTopicId = channel.activeChannelTopicId;
      if (!currentEffectiveActiveTopicId && channel.channelTopics && channel.channelTopics.length > 0) {
          currentEffectiveActiveTopicId = channel.channelTopics[0].id;
      }
      if (currentEffectiveActiveTopicId) {
          const ct = channel.channelTopics?.find(t => t.id === currentEffectiveActiveTopicId);
          setChannelTopicText(ct?.summary || ct?.title || '');
      } else { setChannelTopicText(''); }
      setChannelTopicEditMode(false);
    }
  };
  
  const handleAddMember = (user: User) => {
    if (updateChannel) updateChannel({ ...channel, members: [...channel.members, user.id] });
    toast({ title: "Member added", description: `${user.name} has been added to the channel.` });
    setShowAddMemberSheet(false);
  };
  
  const handleRemoveMember = (user: User) => {
    if (updateChannel) updateChannel({ ...channel, members: channel.members.filter(id => id !== user.id) });
    toast({ title: "Member left channel", description: `${user.name} has left the channel.` });
  };

  const handleSaveChannelViewPreferences = () => {
    if (updateUserSetting) { // Corrected to updateUserSetting
      const newChannelViewOverrides = {
        ...(currentUser.settings?.channelViewOverrides || {}),
        [channel.id]: userChannelViews,
      };
      const newSettings: UserSettings = { // Ensure it's UserSettings, not Partial
        ...currentUser.settings,
        channelViewOverrides: newChannelViewOverrides,
      };
      updateUserSetting(currentUser.id, newSettings); // Corrected to updateUserSetting
      toast({ title: "View preferences saved", description: "Your channel view preferences have been updated." });
    } else {
      toast({ title: "Error", description: "Could not save view preferences.", variant: "destructive" });
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg min-h-[600px] flex flex-col p-0"> {/* Increased min-height */}
        <DialogHeader className="p-6 pb-4">
            <DialogTitle>Channel details and settings</DialogTitle>
        </DialogHeader>
        
        <Tabs value={activeTab} onValueChange={setActiveTab} className="px-6 flex-grow flex flex-col overflow-hidden">
          <TabsList className="grid grid-cols-3 mb-4"> {/* Changed to 3 cols for Settings tab */}
            <TabsTrigger value="about">About</TabsTrigger>
            <TabsTrigger value="members">Members ({memberDetails.length})</TabsTrigger>
            <TabsTrigger value="settings">View Settings</TabsTrigger> {/* New Settings Tab */}
          </TabsList>
          
          <TabsContent value="about" className="space-y-4 flex-grow overflow-y-auto p-1">
            {/* Channel Name Section */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-sm font-medium">Name</h3>
                {!nameEditMode && (
                  <Button variant="ghost" size="icon" onClick={() => setNameEditMode(true)} className="h-6 w-6">
                    <PenLine size={14} />
                  </Button>
                )}
              </div>
              {nameEditMode ? (
                <>
                  <Input 
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    onKeyDown={handleNameKeyDown}
                    className="h-8 px-2" 
                    placeholder="Channel name"
                    ref={nameInputRef}
                  />
                  <div className="flex justify-end space-x-2 mt-2">
                    <Button variant="outline" size="sm" onClick={() => { setName(channel.name); setNameEditMode(false); }}>
                      Cancel
                    </Button>
                    <Button size="sm" onClick={handleSaveName}>Save</Button>
                  </div>
                </>
              ) : (
                <p className="text-sm text-gray-500">#{channel.name}</p>
              )}
            </div>
            <Separator />

            {/* Topic Section */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-sm font-medium">Topic</h3>
                {!channelTopicEditMode && (
                  <Button variant="ghost" size="icon" onClick={() => setChannelTopicEditMode(true)} className="h-6 w-6">
                    <PenLine size={14} />
                  </Button>
                )}
              </div>
              {channelTopicEditMode ? (
                <>
                  <Textarea 
                    value={channelTopicText} 
                    onChange={(e) => setChannelTopicText(e.target.value)}
                    onKeyDown={handleTopicKeyDown}
                    className="min-h-12"
                    placeholder="Add a topic..."
                    ref={topicTextareaRef}
                  />
                  <div className="flex justify-end space-x-2 mt-2">
                    <Button variant="outline" size="sm" onClick={() => {
                      let cEffectiveId = channel.activeChannelTopicId;
                      if (!cEffectiveId && channel.channelTopics && channel.channelTopics.length > 0) cEffectiveId = channel.channelTopics[0].id;
                      if (cEffectiveId) {
                          const cat = channel.channelTopics?.find(t => t.id === cEffectiveId);
                          setChannelTopicText(cat?.summary || cat?.title || '');
                      } else { setChannelTopicText(''); }
                      setChannelTopicEditMode(false);
                    }}>Cancel</Button>
                    <Button size="sm" onClick={handleSaveTopic}>Save</Button>
                  </div>
                </>
              ) : (
                <p className="text-sm text-gray-500 whitespace-pre-wrap">
                  {channelTopicText || "No active topic summary set. Click edit to add one."}
                </p>
              )}
            </div>
            <Separator />

            {/* Description Section */}
            <div>
              <div className="flex justify-between items-center mb-1">
                <h3 className="text-sm font-medium">Description</h3>
                {!descriptionEditMode && (
                  <Button variant="ghost" size="icon" onClick={() => setDescriptionEditMode(true)} className="h-6 w-6">
                    <PenLine size={14} />
                  </Button>
                )}
              </div>
              {descriptionEditMode ? (
                <>
                  <Textarea 
                    value={description} 
                    onChange={(e) => setDescription(e.target.value)}
                    onKeyDown={handleDescriptionKeyDown}
                    className="min-h-32"
                    placeholder="Add a description..."
                    ref={descriptionTextareaRef}
                  />
                  <div className="flex justify-end space-x-2 mt-2">
                    <Button variant="outline" size="sm" onClick={() => { setDescription(channel.description || ''); setDescriptionEditMode(false); }}>
                      Cancel
                    </Button>
                    <Button size="sm" onClick={handleSaveDescription}>Save</Button>
                  </div>
                </>
              ) : (
                <p className="text-sm text-gray-500">
                  {channel.description || "No description provided"}
                </p>
              )}
            </div>
            <Separator />
            <div>
              <h3 className="text-sm font-medium mb-1">Created</h3>
              <p className="text-sm text-gray-500">{formattedDate}</p>
            </div>
            {channel.isPrivate && (
              <>
                <Separator />
                <div>
                  <h3 className="text-sm font-medium mb-1">Privacy</h3>
                  <p className="text-sm text-gray-500">This is a private channel. Only members can view this channel.</p>
                </div>
              </>
            )}
          </TabsContent>

          <TabsContent value="settings" className="space-y-4 flex-grow overflow-y-auto p-1">
            {/* Channel View Preferences Section */}
            <div>
              <h3 className="text-sm font-medium mb-2">Channel View Preferences</h3>
              <p className="text-xs text-muted-foreground mb-3">
                Customize which views are visible for you in this channel. At least one view must be selected.
              </p>
              <div className="space-y-2">
                {ALL_CHANNEL_VIEWS.map((viewKey) => (
                  <div key={viewKey} className="flex items-center space-x-2">
                    <Checkbox
                      id={`user-channel-view-${viewKey}`}
                      checked={userChannelViews.includes(viewKey)}
                      onCheckedChange={(checked) => {
                        setUserChannelViews((prev) => {
                          const newViews = checked
                            ? [...prev, viewKey]
                            : prev.filter((v) => v !== viewKey);
                          // Ensure at least one view is always selected
                          return newViews.length > 0 ? newViews : prev;
                        });
                      }}
                    />
                    <Label htmlFor={`user-channel-view-${viewKey}`} className="font-normal">
                      {viewKey}
                    </Label>
                  </div>
                ))}
              </div>
              <div className="flex justify-end mt-4">
                <Button size="sm" onClick={handleSaveChannelViewPreferences}>Save View Preferences</Button>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="members" className="flex-grow overflow-y-auto p-1">
            <div className="flex justify-between items-center mb-3">
              <h3 className="text-sm font-medium">Members</h3>
              <Sheet open={showAddMemberSheet} onOpenChange={setShowAddMemberSheet}>
                <SheetTrigger asChild>
                  <Button size="sm"><Plus size={14} className="mr-1" />Add Members</Button>
                </SheetTrigger>
                <SheetContent side="right">
                  <SheetHeader><SheetTitle>Add members to #{channel.name}</SheetTitle></SheetHeader>
                  <div className="mt-6">
                    <h3 className="text-sm font-medium mb-2">Available Users</h3>
                    <div className="max-h-[500px] overflow-y-auto">
                      {availableUsers.length > 0 ? (
                        availableUsers.map(user => (
                          <div key={user.id} className="flex items-center justify-between gap-2 py-2 border-b border-gray-100 last:border-0">
                            <div className="flex items-center gap-2">
                              <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                              <div>
                                <p className="text-sm font-medium">{user.name}</p>
                                <p className="text-xs text-gray-500">{user.title}</p>
                              </div>
                            </div>
                            <Button size="sm" variant="outline" onClick={() => handleAddMember(user)}>Add</Button>
                          </div>
                        ))
                      ) : ( <p className="text-sm text-gray-500">No available users to add</p> )}
                    </div>
                  </div>
                </SheetContent>
              </Sheet>
            </div>
            <div className="max-h-48 overflow-y-auto"> {/* Consider making this taller or flex-grow if needed */}
              {memberDetails.map(user => (
                <div key={user.id} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-0">
                  <div className="flex items-center gap-2">
                    <img src={user.avatar} alt={user.name} className="w-8 h-8 rounded-full" />
                    <div>
                      <p className="text-sm font-medium">{user.name}</p>
                      <p className="text-xs text-gray-500">{user.title}</p>
                    </div>
                  </div>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button variant="ghost" size="icon" className="h-8 w-8"><LogOut size={16} className="text-red-500" /></Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-2">
                      <div className="flex flex-col gap-2">
                        <p className="text-sm">Remove {user.name}?</p>
                        <div className="flex justify-end gap-2">
                          <Button size="sm" variant="outline" onClick={() => { /* Close popover */ }}>Cancel</Button>
                          <Button size="sm" variant="destructive" onClick={() => handleRemoveMember(user)}>Remove</Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              ))}
              {memberDetails.length === 0 && (<p className="text-sm text-gray-500 text-center py-4">No members in this channel</p>)}
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter className="p-6 pt-4 border-t"> {/* Added border-t */}
          <Button variant="outline" onClick={() => onOpenChange(false)}>Close</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
