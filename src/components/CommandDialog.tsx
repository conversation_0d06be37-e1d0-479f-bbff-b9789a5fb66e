
import React, { useState, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { Hash, User, Search } from 'lucide-react';
import { Dialog, DialogContent } from './ui/dialog';
import { Command, CommandGroup, CommandInput, CommandItem, CommandList } from './ui/command';
import { Channel, User as UserType } from '@/lib/types';

interface CommandDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

interface CommandOption {
  type: 'channel' | 'user'; // Changed 'topic' to 'channel'
  id: string;
  name: string;
  icon: React.ReactNode;
  isPrivate?: boolean;
}

export const CommandDialog = ({ open, onOpenChange }: CommandDialogProps) => {
  const { workspace, setCurrentChannel, setCurrentDirectMessage } = useApp();
  const [searchQuery, setSearchQuery] = useState('');
  const [options, setOptions] = useState<CommandOption[]>([]);

  // Reset search query when dialog opens
  useEffect(() => {
    if (open) {
      setSearchQuery('');
    }
  }, [open]);

  // Build options from channels and users
  useEffect(() => {
    const channelOptions: CommandOption[] = []; // Renamed from topicOptions
    workspace.sections.forEach(section => {
      section.channels.forEach(channel => { // Changed project.topics to project.channels
        channelOptions.push({
          type: 'channel', // Changed 'topic' to 'channel'
          id: channel.id,
          name: channel.name,
          icon: channel.isPrivate ? <Lock size={16} /> : <Hash size={16} />,
          isPrivate: channel.isPrivate
        });
      });
    });
    
    // Filter out current user
    const userOptions: CommandOption[] = workspace.users
      .filter(user => user.id !== workspace.currentUserId)
      .map(user => ({
        type: 'user',
        id: user.id,
        name: user.name,
        icon: <User size={16} />
      }));
    
    setOptions([...channelOptions, ...userOptions]); // Used channelOptions
  }, [workspace]);

  // Filter options based on search query
  const filteredOptions = searchQuery ? 
    options.filter(option => 
      option.name.toLowerCase().includes(searchQuery.toLowerCase())
    ) : 
    options;
  
  const handleSelect = (option: CommandOption) => {
    if (option.type === 'channel') { // Changed 'topic' to 'channel'
      setCurrentChannel(option.id); 
    } else if (option.type === 'user') {
      // Find or create direct message for this user
      const existingDm = workspace.directMessages.find(dm => 
        dm.participants.length === 2 && 
        dm.participants.includes(option.id) && 
        dm.participants.includes(workspace.currentUserId)
      );
      
      if (existingDm) {
        setCurrentDirectMessage(existingDm.id);
      } else {
        // In a real app we would create a new DM here
        console.log("Would create new DM with", option.name);
      }
    }
    
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="p-0 max-w-xl">
        <Command className="rounded-lg border-0 shadow-md" shouldFilter={false}>
          <CommandInput 
            placeholder="Search channels and people..." 
            value={searchQuery}
            onValueChange={setSearchQuery}
            autoFocus
          />
          <CommandList>
            {filteredOptions.length === 0 && (
              <div className="py-6 text-center text-gray-500">
                <Search className="mx-auto h-6 w-6" />
                <p className="mt-2">No results found</p>
              </div>
            )}
            {filteredOptions.length > 0 && (
              <>
                <CommandGroup heading="Channels"> 
                  {filteredOptions.filter(o => o.type === 'channel').map(option => ( // Changed 'topic' to 'channel'
                    <CommandItem
                      key={option.id}
                      value={`${option.type}-${option.id}`}
                      onSelect={() => handleSelect(option)}
                    >
                      <div className="mr-2">{option.icon}</div>
                      <span>{option.name}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
                <CommandGroup heading="People">
                  {filteredOptions.filter(o => o.type === 'user').map(option => (
                    <CommandItem
                      key={option.id}
                      value={`${option.type}-${option.id}`}
                      onSelect={() => handleSelect(option)}
                    >
                      <div className="mr-2">{option.icon}</div>
                      <span>{option.name}</span>
                    </CommandItem>
                  ))}
                </CommandGroup>
              </>
            )}
          </CommandList>
        </Command>
      </DialogContent>
    </Dialog>
  );
};

// Import the Lock icon
import { Lock } from 'lucide-react';
