import React from 'react';
import { Keyboard } from 'lucide-react';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { Button } from './ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from './ui/tooltip';

export const KeyboardShortcutIndicator = () => {
  const { setIsHelpDialogOpen } = useKeyboardShortcuts();

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className="h-8 w-8 rounded-full opacity-70 hover:opacity-100"
            onClick={() => setIsHelpDialogOpen(true)}
            aria-label="Keyboard shortcuts"
          >
            <Keyboard size={16} />
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <div className="flex flex-col items-center">
            <p className="text-sm">Keyboard shortcuts</p>
            <div className="flex items-center gap-1 mt-1">
              <kbd className="px-1.5 py-0.5 text-xs">Shift</kbd>
              <span>+</span>
              <kbd className="px-1.5 py-0.5 text-xs">?</kbd>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
};
