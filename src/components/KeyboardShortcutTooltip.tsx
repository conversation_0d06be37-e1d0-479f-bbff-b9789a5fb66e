import React from 'react';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';

interface KeyboardShortcutTooltipProps {
  shortcut: string;
  description: string;
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
}

export const KeyboardShortcutTooltip = ({
  shortcut,
  description,
  children,
  side = 'bottom',
}: KeyboardShortcutTooltipProps) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <div className="inline-block" data-shortcut={shortcut}>
          {children}
        </div>
      </TooltipTrigger>
      <TooltipContent side={side} className="flex items-center gap-2">
        <span>{description}</span>
        <kbd className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">
          {shortcut}
        </kbd>
      </TooltipContent>
    </Tooltip>
  );
};
