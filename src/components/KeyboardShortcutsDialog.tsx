import { useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle, DialogDescription } from './ui/dialog';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from './ui/tabs';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const KeyboardShortcutsDialog = ({ open, onOpenChange }: KeyboardShortcutsDialogProps) => {
  const { shortcuts } = useKeyboardShortcuts();

  // Group shortcuts by category
  const navigationShortcuts = shortcuts.filter(s => s.category === 'navigation');
  const messagingShortcuts = shortcuts.filter(s => s.category === 'messaging');
  const viewsShortcuts = shortcuts.filter(s => s.category === 'views');
  const threadsShortcuts = shortcuts.filter(s => s.category === 'threads');
  const generalShortcuts = shortcuts.filter(s => s.category === 'general');

  // Focus the dialog when it opens
  useEffect(() => {
    if (open) {
      // Focus the dialog after it opens
      setTimeout(() => {
        const dialogElement = document.querySelector('[role="dialog"]');
        if (dialogElement) {
          (dialogElement as HTMLElement).focus();
        }
      }, 100);
    }
  }, [open]);

  const renderShortcutList = (categoryShortcuts: typeof shortcuts) => (
    <div className="space-y-2">
      {categoryShortcuts.map(shortcut => (
        <div key={shortcut.id} className="flex justify-between items-center py-1">
          <span className="text-sm">{shortcut.description}</span>
          <div className="flex space-x-2">
            {shortcut.keys.map((key, index) => (
              <kbd
                key={index}
                className="px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600"
              >
                {key}
              </kbd>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle className="text-xl">Keyboard Shortcuts</DialogTitle>
          <DialogDescription>
            Press <kbd className="px-2 py-1 text-xs font-semibold bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">Shift+?</kbd> or <kbd className="px-2 py-1 text-xs font-semibold bg-gray-100 border border-gray-200 rounded-md dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600">Ctrl+/</kbd> to open this dialog anytime
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="navigation" className="mt-4">
          <TabsList className="grid grid-cols-5 mb-4">
            <TabsTrigger value="navigation">Navigation</TabsTrigger>
            <TabsTrigger value="messaging">Messaging</TabsTrigger>
            <TabsTrigger value="views">Views</TabsTrigger>
            <TabsTrigger value="threads">Threads</TabsTrigger>
            <TabsTrigger value="general">General</TabsTrigger>
          </TabsList>

          <TabsContent value="navigation" className="space-y-4 max-h-[400px] overflow-y-auto p-2">
            {renderShortcutList(navigationShortcuts)}
          </TabsContent>

          <TabsContent value="messaging" className="space-y-4 max-h-[400px] overflow-y-auto p-2">
            {renderShortcutList(messagingShortcuts)}
          </TabsContent>

          <TabsContent value="views" className="space-y-4 max-h-[400px] overflow-y-auto p-2">
            {renderShortcutList(viewsShortcuts)}
          </TabsContent>

          <TabsContent value="threads" className="space-y-4 max-h-[400px] overflow-y-auto p-2">
            {renderShortcutList(threadsShortcuts)}
          </TabsContent>

          <TabsContent value="general" className="space-y-4 max-h-[400px] overflow-y-auto p-2">
            {renderShortcutList(generalShortcuts)}
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
};
