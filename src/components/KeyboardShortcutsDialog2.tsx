import React, { useEffect, useMemo } from 'react';
import { X } from 'lucide-react';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog';

const KeyDisplay: React.FC<{ keyName: string; isMac?: boolean }> = ({ keyName, isMac = false }) => {
  // Replace Ctrl with ⌘ and vice versa based on OS
  let displayKey = keyName;

  if (isMac && keyName.includes('Ctrl+')) {
    displayKey = keyName.replace('Ctrl+', '⌘+');
  } else if (!isMac && keyName.includes('⌘+')) {
    displayKey = keyName.replace('⌘+', 'Ctrl+');
  }

  return (
    <kbd className="px-2 py-1.5 text-xs font-semibold text-[var(--app-main-text)] bg-[var(--app-hover-bg)] border border-[var(--app-border)] rounded-md">
      {displayKey}
    </kbd>
  );
};

interface KeyboardShortcutsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const KeyboardShortcutsDialog2 = ({ open, onOpenChange }: KeyboardShortcutsDialogProps) => {
  const { shortcuts } = useKeyboardShortcuts();

  // Detect OS for key display
  const isMac = useMemo(() => {
    return navigator.userAgent.indexOf('Mac') !== -1;
  }, []);

  // Group shortcuts by category
  const navigationShortcuts = shortcuts.filter(s => s.category === 'navigation');
  const messagingShortcuts = shortcuts.filter(s => s.category === 'messaging');
  const viewsShortcuts = shortcuts.filter(s => s.category === 'views');
  const topicsShortcuts = shortcuts.filter(s => s.category === 'topics');
  const generalShortcuts = shortcuts.filter(s => s.category === 'general');

  // Focus the dialog when it opens
  useEffect(() => {
    if (open) {
      // Focus the dialog after it opens
      setTimeout(() => {
        const dialogElement = document.querySelector('[role="dialog"]');
        if (dialogElement) {
          (dialogElement as HTMLElement).focus();
        }
      }, 100);
    }
  }, [open]);

  // Organize shortcuts into groups for display
  const shortcutGroups = [
    { name: 'Navigation', shortcuts: navigationShortcuts },
    { name: 'Messaging', shortcuts: messagingShortcuts },
    { name: 'Views', shortcuts: viewsShortcuts },
    { name: 'Topics', shortcuts: topicsShortcuts },
    { name: 'General', shortcuts: generalShortcuts },
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
        <DialogHeader className="flex justify-between items-center">
          <DialogTitle className="text-xl">Keyboard Shortcuts</DialogTitle>
          <button
            onClick={() => onOpenChange(false)}
            className="w-8 h-8 rounded-full flex items-center justify-center opacity-70 hover:opacity-100 hover:bg-[var(--app-hover-bg)]"
          >
            <X size={18} />
          </button>
        </DialogHeader>



        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 py-4">
          {shortcutGroups.map((group, groupIndex) => (
            <div key={groupIndex} className="space-y-3">
              <h3 className="font-medium text-lg border-b border-[var(--app-border)] pb-2">{group.name}</h3>
              <ul className="space-y-2">
                {group.shortcuts.map((shortcut, shortcutIndex) => (
                  <li key={shortcutIndex} className="flex items-center justify-between">
                    <span className="text-sm opacity-90">{shortcut.description}</span>
                    <div className="flex items-center gap-1">
                      {shortcut.keys
                        // Filter keys to show only OS-appropriate ones
                        .filter(key => {
                          // If it's a Mac, show Mac keys (⌘) or non-platform specific keys
                          if (isMac) {
                            return !key.includes('Ctrl+') || key.includes('⌘+');
                          }
                          // If it's not a Mac, show Ctrl keys or non-platform specific keys
                          return !key.includes('⌘+') || key.includes('Ctrl+');
                        })
                        .map((key, keyIndex, filteredKeys) => (
                          <span key={keyIndex} className="flex items-center">
                            <KeyDisplay keyName={key} isMac={isMac} />
                            {keyIndex < filteredKeys.length - 1 && (
                              <span className="text-xs px-1 opacity-60">or</span>
                            )}
                          </span>
                        ))
                      }
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <div className="mt-6 border-t border-[var(--app-border)] pt-4 text-center text-sm opacity-70">
          Press <span className="inline-flex items-center"><KeyDisplay keyName="Shift+?" /></span> or <span className="inline-flex items-center"><KeyDisplay keyName={isMac ? "⌘+/" : "Ctrl+/"} /></span> at any time to show or hide this dialog
        </div>
      </DialogContent>
    </Dialog>
  );
};
