
import React from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { User, AtSign, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { User as UserType } from '@/lib/types';
import { useApp } from '@/lib/app-context';

interface MemberProfileDialogProps {
  member: UserType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const MemberProfileDialog = ({ member, open, onOpenChange }: MemberProfileDialogProps) => {
  const { addDirectMessage } = useApp();
  
  if (!member) return null;
  
  const handleDirectMessage = () => {
    addDirectMessage(member.id);
    onOpenChange(false);
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={member.avatar} alt={member.name} />
              <AvatarFallback>
                <User className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <DialogTitle className="text-xl">{member.name}</DialogTitle>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  member.status === 'online' ? 'bg-green-500' : 
                  member.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
                }`}></div>
                <span className="capitalize">{member.status || 'offline'}</span>
              </div>
            </div>
          </div>
        </DialogHeader>
        
        <div className="pt-2 space-y-4">
          {member.title && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500">Title:</span>
              <span>{member.title}</span>
            </div>
          )}
          
          <Separator />
          
          <div>
            <h3 className="font-medium mb-2">About</h3>
            <p className="text-sm text-gray-600">
              {member.about || "No information available"}
            </p>
          </div>
          
          <Separator />
          
          <div className="flex justify-between">
            <Button variant="outline" size="sm" className="flex items-center gap-1">
              <AtSign size={14} />
              Email
            </Button>
            <Button onClick={handleDirectMessage} className="flex items-center gap-1">
              <MessageSquare size={14} />
              Message
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
