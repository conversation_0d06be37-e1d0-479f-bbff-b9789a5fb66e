import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { formatTimestamp } from '@/lib/mock-data';
import { MessageCircle, Reply, ThumbsUp, ThumbsDown, Info, User as User<PERSON>con<PERSON>ucide, Copy, Check } from 'lucide-react'; // Added Copy, Check
import { Message as MessageType, User as UserType, WorkspaceDisplayUser } from '@/lib/types';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { generateDisplayId } from '@/lib/utils'; // Import generateDisplayId
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

// Helper component for copy-to-clipboard button
const CopyButton = ({ textToCopy }: { textToCopy: string }) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent popover from closing or other parent clicks
    navigator.clipboard.writeText(textToCopy).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 1500); // Reset after 1.5 seconds
    }).catch(err => {
      console.error('Failed to copy text: ', err);
      // Optionally, add user feedback for error
    });
  };

  return (
    <button
      onClick={handleCopy}
      className="p-1 text-[var(--app-secondary-text)] hover:text-[var(--app-main-text)] focus:outline-none"
      aria-label={copied ? "Copied!" : "Copy ID"}
    >
      {copied ? <Check size={14} className="text-green-500" /> : <Copy size={14} />}
    </button>
  );
};

interface MessageProps {
  message: MessageType;
  showThread?: boolean;
  onClick?: () => void;
  isInThread?: boolean;
  isActive?: boolean;
  isParentMessage?: boolean;
  onAvatarClick?: (user: UserType) => void;
}

export const Message = ({
  message,
  showThread = false,
  onClick,
  isInThread = false,
  isActive = false,
  isParentMessage = false,
  onAvatarClick
}: MessageProps) => {
  const { workspace, addReaction, currentThread, setActiveThread, getCurrentUser } = useApp();
  const { selectedMessageId, setSelectedMessageId } = useKeyboardShortcuts();
  const [showReactions, setShowReactions] = useState(false);
  const [showChannelInfo, setShowChannelInfo] = useState(false);
  // const user = findUserById(message.userId); // Replaced with context lookup
  const user = workspace?.users.find(u => u.id === message.userId) as WorkspaceDisplayUser | undefined;
  const appCurrentUser = getCurrentUser();
  const messageRef = useRef<HTMLDivElement>(null);

  // Check if this message is selected
  const isSelected = selectedMessageId === message.id;

  // Determine if status indicator should be shown
  let showStatusIndicator = workspace.settings?.showStatusIndicatorInMessagesDefault ?? true;
  if (appCurrentUser?.settings?.showStatusIndicatorInMessagesOverride !== null && appCurrentUser?.settings?.showStatusIndicatorInMessagesOverride !== undefined) {
    showStatusIndicator = appCurrentUser.settings.showStatusIndicatorInMessagesOverride;
  }

  // Find the actual thread if available
  const thread = currentThread && currentThread.parentMessageId === message.id
    ? currentThread
    : undefined;

  // Count replies excluding the first message which is the parent
  const replyCount = thread ? thread.messages.length - 1 : 0;

  // Check if this message has a thread with replies
  const hasReplies = replyCount > 0;

  if (!user) {
    return null;
  }

  const handleThumbsUp = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Check if current user already reacted with thumbs down
    const thumbsDownReaction = message.reactions?.find(r => r.emoji === 'thumbs_down');
    if (thumbsDownReaction?.users.includes(workspace.currentUserId)) {
      // Remove thumbs down first
      addReaction(message.id, 'thumbs_down');
    }

    // Toggle thumbs up
    addReaction(message.id, 'thumbs_up');
  };

  const handleThumbsDown = (e: React.MouseEvent) => {
    e.stopPropagation();
    // Check if current user already reacted with thumbs up
    const thumbsUpReaction = message.reactions?.find(r => r.emoji === 'thumbs_up');
    if (thumbsUpReaction?.users.includes(workspace.currentUserId)) {
      // Remove thumbs up first
      addReaction(message.id, 'thumbs_up');
    }

    // Toggle thumbs down
    addReaction(message.id, 'thumbs_down');
  };

  const handleReplyClick = (e: React.MouseEvent) => {
    e.stopPropagation();

    // Create a new thread if one doesn't exist yet
    setActiveThread(message.id);
  };

  // Handle message click - open thread for any message when in thread mode
  const handleMessageClick = () => {
    if (currentThread && !isInThread) {
      setActiveThread(message.id);
    } else if (onClick) {
      onClick();
    }
  };

  // Render reactions if any
  const thumbsUpReaction = message.reactions?.find(r => r.emoji === 'thumbs_up');
  const thumbsDownReaction = message.reactions?.find(r => r.emoji === 'thumbs_down');

  const hasReactions = thumbsUpReaction || thumbsDownReaction;
  const userHasReacted =
    (thumbsUpReaction?.users.includes(workspace.currentUserId)) ||
    (thumbsDownReaction?.users.includes(workspace.currentUserId));

  // Handle click to select this message
  const handleSelectMessage = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedMessageId(message.id);
  };

  // Scroll into view and focus when selected
  useEffect(() => {
    if (isSelected && messageRef.current) {
      messageRef.current.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
      messageRef.current.focus();
    }
  }, [isSelected]);

  return (
    <div
      ref={messageRef}
      className={`message group flex items-start py-1 px-2 rounded ${isInThread ? 'pl-0' : ''}
        ${isActive ? 'thread-active-message' : ''}
        ${isParentMessage ? 'thread-parent-message' : ''}
        ${isSelected ? 'bg-[var(--app-hover-bg)] outline outline-2 outline-[var(--app-highlight)]' : ''}`}
      onClick={(e) => {
        handleMessageClick();
        handleSelectMessage(e);
      }}
      onMouseEnter={() => setShowReactions(true)}
      onMouseLeave={() => setShowReactions(false)}
      tabIndex={0}
      aria-selected={isSelected}
      data-message-id={message.id}
    >
      <div className="flex-shrink-0 mt-0.5 relative">
        <button
          onClick={(e) => {
            e.stopPropagation(); // Prevent message click event
            if (onAvatarClick) {
              onAvatarClick(user);
            }
          }}
          className="focus:outline-none relative" // Added relative here for positioning context
          aria-label={`View profile of ${user.name}`}
        >
          <img
            src={user.avatar}
            alt={user.name}
            className="w-9 h-9 rounded"
          />
          {showStatusIndicator && user.status && (
            <>
              {user.status === 'online' && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-green-500 rounded-full border border-[var(--app-main-bg)]"></div>
              )}
              {user.status === 'away' && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-yellow-500 rounded-full border border-[var(--app-main-bg)]"></div>
              )}
              {user.status === 'busy' && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-red-500 rounded-full border border-[var(--app-main-bg)]"></div>
              )}
              {user.status === 'dnd' && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-purple-500 rounded-full border border-[var(--app-main-bg)]"></div>
              )}
              {user.status === 'offline' && (
                <div className="absolute -bottom-0.5 -right-0.5 w-2.5 h-2.5 bg-gray-400 rounded-full border border-[var(--app-main-bg)]"></div>
              )}
            </>
          )}
        </button>
      </div>
      <div className="ml-2 flex-1">
        <div className="flex items-baseline">
          <span className="message-sender font-bold">{user.name}</span>
          <span className="message-timestamp ml-2 text-xs">{formatTimestamp(message.timestamp)}</span>

          {/* Channel info button */}
          {!isInThread && (
            <Popover open={showChannelInfo} onOpenChange={setShowChannelInfo}>
              <PopoverTrigger asChild>
                <button
                  className="ml-2 opacity-60 hover:opacity-100"
                  onClick={(e) => {
                    e.stopPropagation();
                    setShowChannelInfo(true);
                  }}
                  aria-label="Message details"
                >
                  <Info size={14} />
                </button>
              </PopoverTrigger>
              <PopoverContent className="w-96 p-4" align="start"> {/* Increased width w-80 to w-96 */}
                <div className="space-y-3">
                  <h4 className="font-medium">Message Details</h4>
                  <div className="grid grid-cols-[auto,1fr,auto] items-center gap-x-2 gap-y-1 text-sm"> {/* Adjusted grid for copy icon */}
                    
                    <div className="opacity-70">Sender</div>
                    <div className="truncate col-span-2">{user.name}</div>

                    <div className="opacity-70">Sent</div>
                    <div className="truncate col-span-2">{new Date(message.timestamp).toLocaleString()}</div>

                    <div className="opacity-70">Message ID</div>
                    <div className="truncate">{message.displayId || message.id}</div>
                    <CopyButton textToCopy={message.displayId || message.id} />

                    {message.channelId && (
                      <>
                        <div className="opacity-70">Channel ID</div>
                        <div className="truncate">{generateDisplayId(message.channelId, 'c-')}</div>
                        <CopyButton textToCopy={generateDisplayId(message.channelId, 'c-')} />
                      </>
                    )}

                    {message.topicId && (
                      <>
                        <div className="opacity-70">Topic ID</div>
                        <div className="truncate">{generateDisplayId(message.topicId, 't-')}</div>
                        <CopyButton textToCopy={generateDisplayId(message.topicId, 't-')} />
                      </>
                    )}

                    {message.threadId && (
                      <>
                        <div className="opacity-70">Thread ID</div>
                        <div className="truncate">{generateDisplayId(message.threadId, 'th-')}</div>
                        <CopyButton textToCopy={generateDisplayId(message.threadId, 'th-')} />
                        <div className="opacity-70">Thread</div>
                        <div className="truncate">Part of a thread</div>
                      </>
                    )}
                  </div>
                </div>
              </PopoverContent>
            </Popover>
          )}
        </div>
        <div className="message-content mt-1 whitespace-pre-wrap user-select-text">{message.content}</div>

        {/* Reactions - show both as buttons or indicators */}
        {hasReactions && (
          <div className="mt-1 flex flex-wrap gap-1">
            {thumbsUpReaction && (
              <button
                className={`flex items-center text-xs rounded-full px-2 py-1 opacity-90 hover:opacity-100 ${
                  thumbsUpReaction.users.includes(workspace.currentUserId)
                    ? 'bg-[var(--app-active)] text-[var(--app-active-text)]'
                    : 'bg-[var(--app-hover-bg)]'
                }`}
                onClick={handleThumbsUp}
              >
                <ThumbsUp size={12} className="mr-1" />
                <span>{thumbsUpReaction.count}</span>
              </button>
            )}
            {thumbsDownReaction && (
              <button
                className={`flex items-center text-xs rounded-full px-2 py-1 opacity-90 hover:opacity-100 ${
                  thumbsDownReaction.users.includes(workspace.currentUserId)
                    ? 'bg-[var(--app-active)] text-[var(--app-active-text)]'
                    : 'bg-[var(--app-hover-bg)]'
                }`}
                onClick={handleThumbsDown}
              >
                <ThumbsDown size={12} className="mr-1" />
                <span>{thumbsDownReaction.count}</span>
              </button>
            )}
          </div>
        )}

        {/* Message Actions - Consistent display for replies and reactions */}
        {!isInThread && (
          <div className="mt-1 flex justify-between items-center">
            {/* Thread view link or reply button */}
            <div>
              {hasReplies ? (
                <div
                  className="flex items-center text-xs text-[var(--app-highlight)] cursor-pointer hover:opacity-80"
                  onClick={onClick}
                >
                  <MessageCircle size={14} className="mr-1" />
                  <span>{replyCount} {replyCount === 1 ? 'reply' : 'replies'}</span>
                </div>
              ) : (
                <div className={`${showReactions ? 'opacity-100' : 'opacity-0'} group-hover:opacity-100`}>
                  <button
                    className="flex items-center text-xs text-[var(--app-main-text)] opacity-70 hover:opacity-100"
                    onClick={handleReplyClick}
                  >
                    <Reply size={12} className="mr-1" />
                    <span>Reply in thread</span>
                  </button>
                </div>
              )}
            </div>

            {/* Reactions - Always right-aligned and consistently displayed */}
            <div className={`${showReactions ? 'opacity-100' : 'opacity-0'} group-hover:opacity-100 flex space-x-2 ml-auto`}>
              <button
                className={`flex items-center text-xs opacity-70 hover:opacity-100 ${
                  thumbsUpReaction?.users.includes(workspace.currentUserId)
                    ? 'text-[var(--app-highlight)]'
                    : 'text-[var(--app-main-text)]'
                }`}
                onClick={handleThumbsUp}
              >
                <ThumbsUp size={12} />
              </button>
              <button
                className={`flex items-center text-xs opacity-70 hover:opacity-100 ${
                  thumbsDownReaction?.users.includes(workspace.currentUserId)
                    ? 'text-[var(--app-highlight)]'
                    : 'text-[var(--app-main-text)]'
                }`}
                onClick={handleThumbsDown}
              >
                <ThumbsDown size={12} />
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
