import React, { useState, useRef, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { Send } from 'lucide-react';

interface MessageInputProps {
  threadId?: string;
  placeholder?: string;
  autoFocus?: boolean;
  forwardedRef?: React.RefObject<HTMLTextAreaElement>;
  topicId?: string;  // Reverted from topicIds
}

export const MessageInput = ({
  threadId,
  placeholder = "Type a message...",
  autoFocus = false,
  forwardedRef,
  topicId // Reverted from topicIds
}: MessageInputProps) => {
  const [message, setMessage] = useState('');
  const { sendMessage, currentChannel: currentTopic, currentDirectMessage } = useApp();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Use forwarded ref if provided, otherwise use local ref
  const actualRef = forwardedRef || textareaRef;

  // Auto focus on input when component mounts if autoFocus is true
  useEffect(() => {
    if (autoFocus && actualRef.current) {
      actualRef.current.focus();
    }
  }, [autoFocus, actualRef, currentTopic, currentDirectMessage]);

  const handleSendMessage = () => {
    if (!message.trim()) return;

    // Reverted to use topicId (string)
    if (topicId) {
      // If topicId is provided, send message to that topic within the current channel
      // currentTopic.id is the channelId
      sendMessage(message, currentTopic?.id, undefined, threadId, topicId);
    } else {
      // Original usage: sending to a channel directly or a DM
      sendMessage(message, currentTopic?.id, currentDirectMessage?.id, threadId, undefined); // Pass undefined for topicId
    }

    setMessage('');

    // Focus the textarea after sending
    actualRef.current?.focus();
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    } else if (e.key === 'Escape') {
      // Blur the input when Escape is pressed
      e.currentTarget.blur();
      // If we're in keyboard navigation mode, this will allow keyboard navigation to resume
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    const textarea = actualRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = `${textarea.scrollHeight}px`;
    }
  }, [message, actualRef]);

  return (
    <div className="flex items-end border rounded-md bg-[var(--app-main-bg)] shadow-sm">
      <textarea
        ref={actualRef}
        className="message-input flex-1 p-3 min-h-[44px] max-h-[120px] resize-none focus:outline-none bg-transparent text-[var(--app-main-text)] placeholder:text-[var(--app-main-text)] placeholder:opacity-50"
        placeholder={placeholder}
        value={message}
        onChange={(e) => setMessage(e.target.value)}
        onKeyDown={handleKeyDown}
        rows={1}
      />
      <button
        className={`p-2 mr-2 mb-2 rounded-full transition-colors ${
          message.trim()
            ? 'text-[var(--app-highlight)] hover:text-[var(--app-active)]'
            : 'text-[var(--app-main-text)] opacity-30'
        }`}
        onClick={handleSendMessage}
        disabled={!message.trim()}
      >
        <Send size={20} />
      </button>
    </div>
  );
};
