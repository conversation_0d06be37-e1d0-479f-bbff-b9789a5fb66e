import { useRef, useEffect, useMemo, useState, useCallback } from 'react';
import { useApp } from '@/lib/app-context';
import { Message as MessageType, User, Channel, DirectMessage } from '@/lib/types'; // Renamed to avoid conflict
import { Message } from './Message';
import { Button } from '@/components/ui/button'; // Import Button
import { Spinner } from '@/components/ui/spinner'; // Import Spinner
import { MemberProfileDialog } from '@/components/MemberProfileDialog';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

interface MessageListProps {
  topicId?: string; // Renamed from subTopicId
}

export const MessageList = ({ topicId }: MessageListProps) => { // Renamed from subTopicId
  const {
    currentChannel,
    currentDirectMessage,
    setActiveThread,
    currentThread,
    conversationReadMarkers,
    markConversationRead,
    getCurrentUser,
    effectiveMarkAsReadDelaySeconds,
    loadOlderMessages,
  } = useApp();
  const { selectedMessageId, setSelectedMessageId } = useKeyboardShortcuts();
  const [isLoadingOlder, setIsLoadingOlder] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const newMessageSeparatorRef = useRef<HTMLDivElement>(null); // Added for the new message separator
  const messageListRef = useRef<HTMLDivElement>(null);
  const currentUser = getCurrentUser(); // Get current user
  const previousConversationRef = useRef<string | null>(null);
  const previousMessageCountRef = useRef<number>(0);

  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [selectedMemberForProfile, setSelectedMemberForProfile] = useState<User | null>(null);

  const handleAvatarClick = (user: User) => {
    setSelectedMemberForProfile(user);
    setIsProfileDialogOpen(true);
  };

  const baseMessages = currentChannel?.messages || currentDirectMessage?.messages || [];
  const displayedMessages = topicId
    ? baseMessages.filter(msg => msg.topicId === topicId)
    : baseMessages;

  // Log displayedMessages
  useEffect(() => {
    // console.log('[MessageList] Displayed messages:', JSON.parse(JSON.stringify(displayedMessages)));
  }, [displayedMessages]);

  const currentConversationId = useMemo(() => currentChannel?.id || currentDirectMessage?.id, [currentChannel, currentDirectMessage]);
  const lastReadTimestamp = useMemo(() => currentConversationId ? conversationReadMarkers[currentConversationId] : undefined, [currentConversationId, conversationReadMarkers]);

  const separatorMessageIdToShow = useMemo(() => {
    if (!currentConversationId || !currentUser || !lastReadTimestamp) {
      // If no current conversation, user, or last read timestamp, don't show separator.
      return null;
    }

    // Find the first message that is newer than the lastReadTimestamp
    // and was not sent by the current user.
    const firstNewMessage = displayedMessages.find(
      msg => new Date(msg.timestamp) > new Date(lastReadTimestamp) && msg.userId !== currentUser.id
    );

    return firstNewMessage ? firstNewMessage.id : null;
  }, [displayedMessages, lastReadTimestamp, currentUser, currentConversationId]);


  // Effect to mark conversation as read when user scrolls to the bottom
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          const conversationId = currentChannel?.id || currentDirectMessage?.id;
          const type = currentChannel ? 'channel' : currentDirectMessage ? 'dm' : undefined;
          // const unreadCount = currentChannel?.unreadCount || currentDirectMessage?.unreadCount || 0; // Condition removed

          if (conversationId && type && displayedMessages.length > 0) { // Removed unreadCount > 0 condition
            const latestMessageTimestamp = displayedMessages[displayedMessages.length - 1].timestamp;

            // Call markConversationRead after the configured delay
            const timer = setTimeout(() => {
              markConversationRead(conversationId, type);
            }, effectiveMarkAsReadDelaySeconds * 1000); // Use configured delay
            return () => clearTimeout(timer);
          }
        }
      },
      {
        root: null, // viewport
        threshold: 0.1, // How much of the item is visible
      }
    );

    const currentMessagesEndRef = messagesEndRef.current;
    if (currentMessagesEndRef) {
      observer.observe(currentMessagesEndRef);
    }

    return () => {
      if (currentMessagesEndRef) {
        observer.unobserve(currentMessagesEndRef);
      }
    };
  }, [currentChannel, currentDirectMessage, displayedMessages, markConversationRead, effectiveMarkAsReadDelaySeconds]);


  // Smart auto-scroll logic
  useEffect(() => {
    const currentConversationId = currentChannel?.id || currentDirectMessage?.id;
    const currentMessageCount = displayedMessages.length;
    const previousConversationId = previousConversationRef.current;
    const previousMessageCount = previousMessageCountRef.current;

    // Update refs for next comparison
    previousConversationRef.current = currentConversationId || null;
    previousMessageCountRef.current = currentMessageCount;

    if (currentMessageCount === 0) return;

    // Case 1: Conversation changed (channel/DM/topic switch) - always scroll to bottom
    const conversationChanged = currentConversationId !== previousConversationId;

    // Case 2: Loading older messages - don't auto-scroll
    if (isLoadingOlder) {
      return;
    }

    // Case 3: New messages added (message count increased) - scroll to bottom
    const newMessagesAdded = currentMessageCount > previousMessageCount && !conversationChanged;

    if (conversationChanged || newMessagesAdded) {
      messagesEndRef.current?.scrollIntoView({ behavior: conversationChanged ? 'auto' : 'smooth' });
    }
  }, [displayedMessages.length, currentChannel?.id, currentDirectMessage?.id, topicId, isLoadingOlder]);

  // Focus the input field when entering a thread or channel (if keyboard navigation mode is not enabled)
  const { isKeyboardNavigationModeEnabled } = useKeyboardShortcuts();

  useEffect(() => {
    // Only auto-focus if keyboard navigation mode is not enabled
    if (!isKeyboardNavigationModeEnabled) {
      const messageInput = document.querySelector('textarea.message-input') as HTMLTextAreaElement;
      if (messageInput && !currentThread) {
        setTimeout(() => {
          messageInput.focus();
        }, 100);
      }
    }
  }, [currentChannel, currentDirectMessage, currentThread, topicId, isKeyboardNavigationModeEnabled]);

  // Handle keyboard navigation between messages
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard navigation when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Handle various navigation keys
      const isUpNavigation = e.key === 'ArrowUp' || e.key === 'k';
      const isDownNavigation = e.key === 'ArrowDown' || e.key === 'j';
      const isCtrlPressed = e.ctrlKey || e.metaKey;
      const isShiftPressed = e.shiftKey;

      // Check if this is a navigation key combination we handle
      const isNavigationKey = isUpNavigation || isDownNavigation;

      if (!isNavigationKey) {
        return;
      }

      // Only handle keyboard navigation if we're in keyboard navigation mode
      if (!isKeyboardNavigationModeEnabled) {
        return;
      }

      e.preventDefault();

      // Get all message elements
      const messageElements = Array.from(
        document.querySelectorAll('.message[data-message-id]')
      );

      if (messageElements.length === 0) return;

      // Find the index of the currently selected message
      const currentIndex = selectedMessageId
        ? messageElements.findIndex(el => el.getAttribute('data-message-id') === selectedMessageId)
        : -1;

      let nextIndex: number;

      // Handle Ctrl+Shift combination (jump to new messages marker or start/end)
      if (isCtrlPressed && isShiftPressed) {
        if (messageElements.length === 0) return; // No messages to navigate

        const firstMessageElement = messageElements[0];
        const lastMessageElement = messageElements[messageElements.length - 1];
        const firstMessageId = firstMessageElement?.getAttribute('data-message-id');
        const lastMessageId = lastMessageElement?.getAttribute('data-message-id');

        let targetMessageId: string | null = null;
        let targetElement: Element | undefined = undefined;

        const separatorElement = separatorMessageIdToShow
            ? messageElements.find(el => el.getAttribute('data-message-id') === separatorMessageIdToShow)
            : undefined;

        // currentIndex is already defined above this block (around line 211)

        const separatorIndexInElements = separatorElement
            ? messageElements.findIndex(el => el.getAttribute('data-message-id') === separatorMessageIdToShow)
            : -1;

        if (isUpNavigation) { // Ctrl + Shift + Up
            if (separatorElement && separatorMessageIdToShow && separatorIndexInElements !== -1) {
                // If current selection is AFTER the new message separator (in terms of element order),
                // or no selection, jump to separator
                if (currentIndex === -1 || currentIndex > separatorIndexInElements) {
                    targetMessageId = separatorMessageIdToShow;
                    targetElement = separatorElement;
                } else {
                    // Otherwise, current selection is at or before separator, so jump to list start
                    targetMessageId = firstMessageId;
                    targetElement = firstMessageElement;
                }
            } else {
                // No new messages, or separator not found among visible elements, jump to list start
                targetMessageId = firstMessageId;
                targetElement = firstMessageElement;
            }
        } else { // Ctrl + Shift + Down
            if (separatorElement && separatorMessageIdToShow && separatorIndexInElements !== -1) {
                // If current selection is BEFORE the new message separator (in terms of element order),
                // or no selection, jump to separator
                if (currentIndex === -1 || currentIndex < separatorIndexInElements) {
                    targetMessageId = separatorMessageIdToShow;
                    targetElement = separatorElement;
                } else {
                    // Otherwise, current selection is at or after separator, so jump to list end
                    targetMessageId = lastMessageId;
                    targetElement = lastMessageElement;
                }
            } else {
                // No new messages, or separator not found among visible elements, jump to list end
                targetMessageId = lastMessageId;
                targetElement = lastMessageElement;
            }
        }

        if (targetMessageId && targetElement) {
            setSelectedMessageId(targetMessageId);
            targetElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
        return; // Prevent falling through to other navigation logic
      }

      // Handle Ctrl key for jumping to start/end
      if (isCtrlPressed) {
        if (isUpNavigation) {
          // Jump to first message
          nextIndex = 0;
        } else {
          // Jump to last message
          nextIndex = messageElements.length - 1;
        }
      }
      // Normal navigation (one message at a time)
      else {
        if (isUpNavigation) {
          // Move to previous message or stay at first
          nextIndex = currentIndex > 0 ? currentIndex - 1 : 0;
        } else {
          // Move to next message or stay at last
          nextIndex = currentIndex < messageElements.length - 1 ? currentIndex + 1 : messageElements.length - 1;
        }

        // If no message is selected yet, select the first or last depending on direction
        if (currentIndex === -1) {
          nextIndex = isUpNavigation ? messageElements.length - 1 : 0;
        }
      }

      // Get the message ID from the element
      const nextMessageId = messageElements[nextIndex].getAttribute('data-message-id');
      if (nextMessageId) {
        setSelectedMessageId(nextMessageId);
      }
    };

    // Use capture phase to ensure we get events before other handlers
    window.addEventListener('keydown', handleKeyDown, true);
    return () => window.removeEventListener('keydown', handleKeyDown, true);
  }, [selectedMessageId, setSelectedMessageId, isKeyboardNavigationModeEnabled]);

  if (displayedMessages.length === 0) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <div className="text-center">
          <h3 className="text-lg font-semibold text-[var(--app-main-text)] opacity-90 mb-2">
            {topicId ? "No messages in this topic yet" : "No messages yet"}
          </h3>
          <p className="text-[var(--app-main-text)] opacity-70">Start the conversation!</p>
        </div>
      </div>
    );
  }

  return (
    <div ref={messageListRef} className="flex-1 overflow-y-auto p-4 user-select-text" tabIndex={-1}>
      {currentConversationId && (currentChannel || currentDirectMessage) && (currentChannel?.hasMoreOlderMessages || currentDirectMessage?.hasMoreOlderMessages) && (
        <div className="text-center mb-4">
          <Button
            variant="outline"
            size="sm"
            onClick={async () => {
              if (!currentConversationId) return;

              // Store the first visible message to maintain scroll position
              const messageListElement = messageListRef.current;
              if (!messageListElement) return;

              const firstVisibleMessage = displayedMessages.find(msg => {
                const msgElement = document.querySelector(`[data-message-id="${msg.id}"]`);
                if (!msgElement) return false;
                const rect = msgElement.getBoundingClientRect();
                const containerRect = messageListElement.getBoundingClientRect();
                return rect.top >= containerRect.top;
              });

              setIsLoadingOlder(true);
              const type = currentChannel ? 'channel' : 'dm';
              await loadOlderMessages(currentConversationId, type);

              // After loading, try to maintain scroll position relative to the first visible message
              if (firstVisibleMessage) {
                setTimeout(() => {
                  const msgElement = document.querySelector(`[data-message-id="${firstVisibleMessage.id}"]`);
                  if (msgElement) {
                    msgElement.scrollIntoView({ behavior: 'auto', block: 'start' });
                  }
                }, 50); // Small delay to ensure DOM is updated
              }

              setIsLoadingOlder(false);
            }}
            disabled={isLoadingOlder}
          >
            {isLoadingOlder ? <Spinner size="sm" className="mr-2" /> : null}
            Load Older Messages
          </Button>
        </div>
      )}
      <div className="space-y-1">
        {displayedMessages.map((message) => { // Removed index from map as it's not directly used for separator logic now
          const isNewMessageSeparator = separatorMessageIdToShow === message.id;

          // Check if this message has a thread
          const hasThread = Boolean(
            (currentChannel && message.id in currentChannel.threads) ||
            (currentDirectMessage && message.id in currentDirectMessage.threads)
          );

          return (
            <div key={message.id}>
              {isNewMessageSeparator && (
                <div ref={newMessageSeparatorRef} className="relative py-3 my-2 new-message-separator">
                  <div className="absolute inset-0 flex items-center" aria-hidden="true">
                    <div className="w-full border-t border-[var(--app-highlight)]" />
                  </div>
                  <div className="relative flex justify-center">
                    <span className="bg-[var(--app-main-bg)] px-2 text-xs text-[var(--app-highlight)] font-semibold rounded">
                      New Messages
                    </span>
                  </div>
                </div>
              )}
              <Message
                message={message}
                onClick={() => setActiveThread(message.id)}
                showThread={hasThread}
                isActive={currentThread?.parentMessageId === message.id}
                onAvatarClick={handleAvatarClick}
              />
            </div>
          );
        })}
      </div>
      <div ref={messagesEndRef} />
      {selectedMemberForProfile && (
        <MemberProfileDialog
          member={selectedMemberForProfile}
          open={isProfileDialogOpen}
          onOpenChange={setIsProfileDialogOpen}
        />
      )}
    </div>
  );
};
