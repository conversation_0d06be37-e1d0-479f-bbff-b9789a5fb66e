import { useState, useEffect, useRef, useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AtSign, Hash, Plus, X, Search } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApp } from '@/lib/app-context';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChannelTopic, Channel, User } from '@/lib/types';
import SimpleMDE from 'react-simplemde-editor';
import type EasyMDE from 'easymde';
import { useToast } from '@/hooks/use-toast';
import 'easymde/dist/easymde.min.css';
import { Command, CommandGroup, CommandItem, CommandInput, CommandList } from './ui/command';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';

interface NewMessageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const NewMessageDialog = ({ open, onOpenChange }: NewMessageDialogProps) => {
  const { workspace, sendMessage, setCurrentChannel } = useApp();
  const { toast } = useToast();
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [messageContent, setMessageContent] = useState('');
  const [showTopicSelector, setShowTopicSelector] = useState(false);
  const [topicTitle, setTopicTitle] = useState('');
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);
  const [isKeyboardMode, setIsKeyboardMode] = useState(false);
  
  // Refs for managing focus
  const toInputRef = useRef<HTMLInputElement>(null);
  const messageEditorRef = useRef<EasyMDE | null>(null);
  const topicListRef = useRef<HTMLDivElement>(null); // For managing focus in topic list
  const addTopicButtonRef = useRef<HTMLButtonElement>(null); // For returning focus
  
  // Get all channels and users
  const allChannels = workspace.sections.flatMap(section => section.channels);
  const allUsers = workspace.users.filter(user => user.id !== workspace.currentUserId);
  
  // Reset state when dialog opens
  useEffect(() => {
    if (open) {
      setSelectedChannelId(null);
      setSelectedUserId(null);
      setSearchQuery('');
      setMessageContent('');
      setTopicTitle('');
      setSelectedTopicId(null);
      setShowTopicSelector(false);
      setShowRecipientDropdown(false);
      
      // Focus on the To: input when dialog opens
      setTimeout(() => {
        if (toInputRef.current) {
          toInputRef.current.focus();
        }
      }, 100);
    }
  }, [open]);
  
  // Close topic selector when clicking outside
  useEffect(() => {
    if (!showTopicSelector) return;
    
    const handleClickOutside = (event: MouseEvent) => {
      if (
        topicListRef.current && 
        !topicListRef.current.contains(event.target as Node) &&
        addTopicButtonRef.current && 
        !addTopicButtonRef.current.contains(event.target as Node)
      ) {
        setShowTopicSelector(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showTopicSelector]);
  
  // Get topics for selected channel
  const getChannelTopics = (): ChannelTopic[] => {
    if (!selectedChannelId) return [];
    
    const channel = allChannels.find(c => c.id === selectedChannelId);
    return channel?.channelTopics || [];
  };
  
  // Handle recipient selection
  const handleRecipientSelect = (type: 'channel' | 'user', id: string) => {
    if (type === 'channel') {
      setSelectedChannelId(id);
      setSelectedUserId(null);
    } else {
      setSelectedUserId(id);
      setSelectedChannelId(null);
    }
    setShowRecipientDropdown(false);
    setSearchQuery('');
    
    // Focus on the message editor after selecting a recipient
    setTimeout(() => {
      if (messageEditorRef.current) {
        messageEditorRef.current.codemirror.focus();
      }
    }, 100);
  };
  
  // Handle topic selection
  const handleTopicSelect = (topicId: string | 'new') => {
    if (topicId === 'new') {
      setTopicTitle('New topic');
      setSelectedTopicId(null);
    } else {
      const topic = getChannelTopics().find(t => t.id === topicId);
      if (topic) {
        setTopicTitle(topic.title);
        setSelectedTopicId(topicId);
      }
    }
    setShowTopicSelector(false);
    
    // Refocus the message editor after topic selection
    setTimeout(() => {
      if (messageEditorRef.current) {
        messageEditorRef.current.codemirror.focus();
      }
    }, 100);
  };
  
  // Filter recipients based on search query
  const getFilteredRecipients = () => {
    const query = searchQuery.toLowerCase().replace(/^[@#]/, '');
    
    if (!query || query.length < 1) {
      return { 
        channels: allChannels, 
        users: allUsers,
        topMatchId: undefined 
      };
    }
    
    // Score-based matching for better results
    const scoreMatch = (name: string, query: string): number => {
      // Exact match gets highest score
      if (name.toLowerCase() === query) return 100;
      
      // Starts with query gets high score
      if (name.toLowerCase().startsWith(query)) return 80;
      
      // Contains exact query as a "word" (e.g. "dev" in "dev-team")
      if (new RegExp(`\\b${query}\\b`).test(name.toLowerCase())) return 60;
      
      // Contains query anywhere
      if (name.toLowerCase().includes(query)) return 40;
      
      // No match
      return 0;
    };
    
    // Sort channels by score
    const scoredChannels = allChannels
      .map(channel => ({
        channel,
        score: scoreMatch(channel.name, query)
      }))
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score);
    
    // Sort users by score
    const scoredUsers = allUsers
      .map(user => ({
        user,
        score: scoreMatch(user.name, query)
      }))
      .filter(item => item.score > 0)
      .sort((a, b) => b.score - a.score);
    
    // Determine the top match ID based on search type and scores
    let topMatchId: string | undefined = undefined;
    
    if (searchQuery.startsWith('#')) {
      // For channel search, get top channel
      if (scoredChannels.length > 0) {
        topMatchId = scoredChannels[0].channel.id;
      }
      
      return { 
        channels: scoredChannels.map(item => item.channel), 
        users: [],
        topMatchId
      };
    }
    
    if (searchQuery.startsWith('@')) {
      // For user search, get top user
      if (scoredUsers.length > 0) {
        topMatchId = scoredUsers[0].user.id;
      }
      
      return { 
        channels: [], 
        users: scoredUsers.map(item => item.user),
        topMatchId
      };
    }
    
    // For general search, compare top scores
    const topChannelScore = scoredChannels.length > 0 ? scoredChannels[0].score : 0;
    const topUserScore = scoredUsers.length > 0 ? scoredUsers[0].score : 0;
    
    // Set top match based on highest score
    if (topChannelScore >= topUserScore && scoredChannels.length > 0) {
      topMatchId = scoredChannels[0].channel.id;
    } else if (topUserScore > 0 && scoredUsers.length > 0) {
      topMatchId = scoredUsers[0].user.id;
    }
    
    return { 
      channels: scoredChannels.map(item => item.channel), 
      users: scoredUsers.map(item => item.user),
      topMatchId
    };
  };
  
  const { channels: filteredChannels, users: filteredUsers, topMatchId } = getFilteredRecipients();
  
  // Get recipient details
  const getSelectedRecipient = () => {
    if (selectedChannelId) {
      const channel = allChannels.find(c => c.id === selectedChannelId);
      return { type: 'channel' as const, name: channel?.name || '', isPrivate: channel?.isPrivate };
    } 
    if (selectedUserId) {
      const user = allUsers.find(u => u.id === selectedUserId);
      return { type: 'user' as const, name: user?.name || '', avatar: user?.avatar };
    }
    return null;
  };
  
  // Refs to hold current values for the keydown handler to avoid stale closures
  const messageContentRef = useRef(messageContent);
  const getSelectedRecipientRef = useRef(getSelectedRecipient);

  useEffect(() => {
    messageContentRef.current = messageContent;
  }, [messageContent]);

  useEffect(() => {
    getSelectedRecipientRef.current = getSelectedRecipient;
  }, [selectedChannelId, selectedUserId, allChannels, allUsers]); // Dependencies of getSelectedRecipient
  
  // SimpleMDE options
  const mdeOptions = useMemo(() => ({
    autofocus: false, // Don't auto-focus the editor
    spellChecker: false,
    placeholder: "Type your message here...",
    status: false,
    toolbar: [
      'bold', 'italic', 'strikethrough', '|',
      'heading', 'heading-smaller', 'heading-bigger', '|',
      'code', 'quote', '|',
      'unordered-list', 'ordered-list', '|',
      'link', 'image', 'table', '|',
      'horizontal-rule', 'preview', 'side-by-side', 'fullscreen'
    ] as const,
    lineWrapping: true,
  }), []);
  
  // Handle sending the message
  const handleSendMessage = () => {
    // Use the ref for the most up-to-date message content
    if (!messageContentRef.current.trim()) {
      toast({
        title: "Empty message",
        description: "Please type a message before sending",
        variant: "destructive"
      });
      return;
    }
    
    // If sending to a channel
    if (selectedChannelId) {
      // Send to specific topic if selected
      if (selectedTopicId || topicTitle) {
        // Here we would create a new topic if needed
        // For now just send the message with topic context
        const topicIdToSend = selectedTopicId ? selectedTopicId : undefined;

        sendMessage(
          messageContentRef.current,
          selectedChannelId,
          undefined,
          undefined,
          topicIdToSend
        );
        
        toast({
          title: "Message sent",
          description: `Sent to channel with topic: ${topicTitle}`
        });
      } else {
        // Send to channel without topic
        sendMessage(messageContentRef.current, selectedChannelId);
        
        toast({
          title: "Message sent",
          description: "Sent to channel"
        });
      }
      
      // Navigate to the channel
      setCurrentChannel(selectedChannelId);
    }
    
    // If sending to a user (DM)
    else if (selectedUserId) {
      // Find or create DM with this user
      const existingDm = workspace.directMessages.find(dm => 
        dm.participants.length === 2 && 
        dm.participants.includes(selectedUserId) && 
        dm.participants.includes(workspace.currentUserId)
      );
      
      if (existingDm) {
        sendMessage(messageContentRef.current, undefined, existingDm.id);
      } else {
        // In a real app we would create the DM first
        console.log("Would create new DM with user", selectedUserId);
      }
      
      toast({
        title: "Message sent",
        description: "Direct message sent"
      });
    }
    
    onOpenChange(false);
  };
  
  const handleTopicListKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!showTopicSelector) return;

    const items = Array.from(
      topicListRef.current?.querySelectorAll('button, [role="button"]') || []
    ) as HTMLElement[];
    if (items.length === 0) return;

    const currentIndex = items.findIndex(item => item === document.activeElement);

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % items.length;
      items[nextIndex]?.focus();
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      const prevIndex = currentIndex === -1 ? items.length -1 : (currentIndex - 1 + items.length) % items.length;
      items[prevIndex]?.focus();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      setShowTopicSelector(false);
      if (addTopicButtonRef.current) {
        addTopicButtonRef.current.focus();
      }
    } else if (e.key === 'Enter' && currentIndex >= 0) {
      // Add Enter key handling for topic selection
      e.preventDefault();
      const selectedItem = items[currentIndex];
      selectedItem.click(); // Trigger the onClick handler of the selected button
    }
  };
  
  const recipient = getSelectedRecipient();
  
  // Handle input changes with auto-completion
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    
    // Show dropdown when typing
    if (value && !showRecipientDropdown) {
      setShowRecipientDropdown(true);
    } else if (!value) {
      setShowRecipientDropdown(false);
    }
  };
  
  // Handle keydown in the input
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    // Set keyboard mode active when using keyboard navigation
    if (e.key === 'Tab') {
      setIsKeyboardMode(true);
    }
    
    // Handle # and @ shortcuts
    if (e.key === '#' && !searchQuery.startsWith('#')) {
      setSearchQuery('#');
      setShowRecipientDropdown(true);
      e.preventDefault();
      return;
    } else if (e.key === '@' && !searchQuery.startsWith('@')) {
      setSearchQuery('@');
      setShowRecipientDropdown(true);
      e.preventDefault();
      return;
    }

    // Handle Tab key for dropdown selection - should work like Enter
    if (showRecipientDropdown && e.key === 'Tab') {
      const commandListElement = document.querySelector('[cmdk-list]');
      if (!commandListElement) return;
      
      // Check for highlighted item
      let itemToSelect: HTMLElement | null = commandListElement.querySelector('[data-selected="true"]') as HTMLElement;
      
      // If there's a single visible item, select it 
      if (!itemToSelect && searchQuery.length > 1) {
        const visibleItems = Array.from(
          commandListElement.querySelectorAll('div[cmdk-item=""]')
        ).filter(el => {
          const style = window.getComputedStyle(el);
          return style.display !== 'none';
        }) as HTMLElement[];
        
        if (visibleItems.length === 1) {
          itemToSelect = visibleItems[0];
        }
      }
      
      if (itemToSelect) {
        e.preventDefault(); // Prevent tab navigation
        const value = itemToSelect.dataset.value;
        const type = itemToSelect.dataset.type as 'channel' | 'user' | undefined;
        
        if (value && type) {
          handleRecipientSelect(type, value);
        }
      } else {
        // If no item to select, just hide the dropdown
        setShowRecipientDropdown(false);
      }
    }

    // Handle Enter key for selecting a recipient
    if (showRecipientDropdown && e.key === 'Enter') {
      e.preventDefault(); // Prevent form submission
      
      // Get the command list and all visible items
      const commandListElement = document.querySelector('[cmdk-list]');
      if (!commandListElement) return;
      
      // First check for an explicitly selected item (highlighted by arrow keys)
      let itemToSelect: HTMLElement | null = commandListElement.querySelector('[data-selected="true"]') as HTMLElement;
      
      if (!itemToSelect && searchQuery.length > 1) {
        // If nothing is explicitly selected and we have a meaningful search
        // Get all visible items (those that match the search)
        const visibleItems = Array.from(
          commandListElement.querySelectorAll('div[cmdk-item=""]')
        ).filter(el => {
          const style = window.getComputedStyle(el);
          return style.display !== 'none';
        }) as HTMLElement[];
        
        // If there's exactly one match, select it
        if (visibleItems.length === 1) {
          itemToSelect = visibleItems[0];
        }
        // If we have multiple visible items but one is an exact match by name, select that
        else if (visibleItems.length > 1) {
          const query = searchQuery.replace(/^[@#]/, '').toLowerCase();
          const exactMatch = visibleItems.find(item => {
            const itemText = item.textContent?.toLowerCase() || '';
            // Check if item text contains the query as a whole word
            return itemText.includes(query) && 
                  (itemText.includes(` ${query}`) || 
                   itemText.includes(`${query} `) || 
                   itemText === query);
          });
          
          if (exactMatch) {
            itemToSelect = exactMatch;
          }
        }
      }
      
      // If we found an item to select, process it
      if (itemToSelect) {
        const value = itemToSelect.dataset.value;
        const type = itemToSelect.dataset.type as 'channel' | 'user' | undefined;
        
        if (value && type) {
          handleRecipientSelect(type, value);
        }
      }
    }
  };
  
  // Handle mouse move to detect mouse navigation
  useEffect(() => {
    const handleMouseMove = () => {
      setIsKeyboardMode(false);
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    
    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="w-[90%] max-w-[90%] h-[90vh] flex flex-col">
        <DialogHeader>
          <DialogTitle>New message</DialogTitle>
        </DialogHeader>
        
        <div className="mt-2">
          <div className="mb-4">
            <div className="flex items-center border-b pb-2">
              <span className="text-sm text-gray-500 mr-2">To:</span>
              
              {recipient ? (
                <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md">
                  {recipient.type === 'channel' ? (
                    <>
                      {recipient.isPrivate ? <AtSign size={14} /> : <Hash size={14} />}
                      <span>{recipient.name}</span>
                    </>
                  ) : (
                    <div className="flex items-center gap-1">
                      <Avatar className="h-5 w-5">
                        <AvatarImage src={recipient.avatar} />
                        <AvatarFallback>
                          <AtSign size={12} />
                        </AvatarFallback>
                      </Avatar>
                      <span>{recipient.name}</span>
                    </div>
                  )}
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-5 w-5 ml-1"
                    onClick={() => {
                      setSelectedChannelId(null);
                      setSelectedUserId(null);
                      setSearchQuery('');
                      // Focus back on input
                      setTimeout(() => {
                        if (toInputRef.current) {
                          toInputRef.current.focus();
                        }
                      }, 10);
                    }}
                  >
                    <X size={12} />
                  </Button>
                </div>
              ) : (
                <>
                  <Input 
                    ref={toInputRef}
                    className="border-0 focus-visible:ring-0 px-0 text-sm"
                    placeholder="#channel, @somebody, or <EMAIL>" 
                    value={searchQuery}
                    onChange={handleInputChange}
                    onKeyDown={handleInputKeyDown}
                    onFocus={() => setShowRecipientDropdown(true)}
                  />
                </>
              )}
            </div>
            
            {showRecipientDropdown && !recipient && (
              <div className="relative">
                <div className="absolute z-50 top-0 left-0 w-full mt-1 bg-white border rounded-md shadow-md">
                  <Command 
                    className="border-0 p-0"
                    // Use a key to force re-render when search changes
                    key={`search-${searchQuery}`}
                    // Set the filter value to our search query to keep it in sync
                    filter={(value, search) => {
                      // Return 1 to indicate a match (we're handling filtering ourselves)
                      return 1;
                    }}
                    // Set the selected value to the top match from our search results
                    value={topMatchId}
                  >
                    <CommandInput 
                      value={searchQuery} 
                      onValueChange={(value) => setSearchQuery(value)}
                      className="hidden"
                    />
                    <CommandList onKeyDown={(e) => {
                      // Don't handle if the dropdown isn't shown
                      if (!showRecipientDropdown) return;
                      
                      // If Escape is pressed, close the dropdown
                      if (e.key === 'Escape') {
                        setShowRecipientDropdown(false);
                        // Return focus to the input
                        if (toInputRef.current) {
                          toInputRef.current.focus();
                        }
                      }
                    }}>
                      {filteredChannels.length > 0 && (
                        <CommandGroup heading="Channels">
                          {filteredChannels.map((channel, index) => (
                            <CommandItem
                              key={channel.id}
                              value={channel.id} // cmdk uses this for selection
                              onSelect={() => handleRecipientSelect('channel', channel.id)}
                              data-value={channel.id} // For our custom Tab handler
                              data-type="channel"      // For our custom Tab handler
                              className="flex items-center cursor-pointer"
                              // Make first channel result selected by default if it's the highest match
                              data-selected={index === 0 && (!filteredUsers.length || searchQuery.startsWith('#')) ? "true" : undefined}
                            >
                              {channel.isPrivate ? <AtSign size={14} className="mr-2" /> : <Hash size={14} className="mr-2" />}
                              <span>{channel.name}</span>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                      {filteredUsers.length > 0 && (
                        <CommandGroup heading="People">
                          {filteredUsers.map((user, index) => (
                            <CommandItem
                              key={user.id}
                              value={user.id} // cmdk uses this for selection
                              onSelect={() => handleRecipientSelect('user', user.id)}
                              data-value={user.id} // For our custom Tab handler
                              data-type="user"       // For our custom Tab handler
                              className="flex items-center cursor-pointer"
                              // Make first user result selected by default if it's the highest match
                              data-selected={index === 0 && (!filteredChannels.length || searchQuery.startsWith('@')) ? "true" : undefined}
                            >
                              <Avatar className="h-6 w-6 mr-2">
                                <AvatarImage src={user.avatar} />
                                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
                              </Avatar>
                              <span>{user.name}</span>
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      )}
                      {filteredChannels.length === 0 && filteredUsers.length === 0 && (
                        <div className="py-6 text-center text-gray-500">
                          <Search className="mx-auto h-6 w-6" />
                          <p className="mt-2">No results found</p>
                        </div>
                      )}
                    </CommandList>
                  </Command>
                </div>
              </div>
            )}
          </div>
          
          {selectedChannelId && (
            <div className="mb-4 relative">
              <div className="flex items-center border-b pb-2">
                <span className="text-sm text-gray-500 mr-2">Topic:</span>
                
                {topicTitle ? (
                  <div className="flex items-center gap-1 bg-gray-100 px-2 py-1 rounded-md">
                    <span>{topicTitle}</span>
                    <Button 
                      variant="ghost" 
                      size="icon" 
                      className="h-5 w-5 ml-1"
                      onClick={() => {
                        setTopicTitle('');
                        setSelectedTopicId(null);
                      }}
                    >
                      <X size={12} />
                    </Button>
                  </div>
                ) : (
                  <div className="flex items-center">
                    <Button 
                      variant="ghost"
                      ref={addTopicButtonRef}
                      size="sm"
                      className="p-0 h-7 text-blue-500 hover:text-blue-600 hover:bg-transparent"
                      onClick={() => {
                        const newShowState = !showTopicSelector;
                        setShowTopicSelector(newShowState);
                        if (newShowState) {
                          // Focus first item in topic list when it opens
                          setTimeout(() => {
                            const firstItem = topicListRef.current?.querySelector('button, [role="button"]');
                            if (firstItem instanceof HTMLElement) {
                              firstItem.focus();
                            }
                          }, 50); // Timeout to allow rendering
                        }
                      }}
                    >
                      <Plus size={14} className="mr-1" />
                      Add a topic
                    </Button>
                    {isKeyboardMode && (
                      <span className="ml-2 text-xs text-gray-400" title="Press Ctrl+Shift+T when focused in the message editor">
                        Ctrl+Shift+T
                      </span>
                    )}
                  </div>
                )}
              </div>
              
              {showTopicSelector && (
                <div className="absolute z-50 top-full left-0 w-full mt-1 bg-white border rounded-md shadow-md">
                  <ScrollArea className="max-h-48">
                    <div
                      className="p-1"
                      ref={topicListRef}
                      onKeyDown={handleTopicListKeyDown} // Add keydown handler
                      role="listbox" // Semantic role
                      tabIndex={-1} // Make the container focusable if needed, though items inside are buttons
                    >
                      <Button
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-blue-500"
                        onClick={() => handleTopicSelect('new')}
                      >
                        <Plus size={14} className="mr-2" />
                        Create new topic
                      </Button>
                      
                      {getChannelTopics().length > 0 && (
                        <>
                          <Separator className="my-1" />
                          <div className="text-xs text-gray-500 px-2 py-1">Existing topics</div>
                          
                          {getChannelTopics().map(topic => (
                            <Button
                              key={topic.id}
                              variant="ghost"
                              size="sm"
                              className="w-full justify-start"
                              onClick={() => handleTopicSelect(topic.id)}
                            >
                              {topic.title}
                            </Button>
                          ))}
                        </>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              )}
            </div>
          )}
          
          <div className="border rounded-md overflow-hidden flex-grow">
            <SimpleMDE
              id="message-editor"
              value={messageContent}
              onChange={setMessageContent}
              options={mdeOptions}
              getMdeInstance={(instance: EasyMDE) => {
                if (instance) {
                  messageEditorRef.current = instance;
                  
                  // Remove any existing event listeners to prevent duplicates
                  if ((instance as any)._ctrlEnterHandler) {
                    instance.codemirror.off("keydown", (instance as any)._ctrlEnterHandler);
                  }
                  
                  // Define and attach the Ctrl+Enter handler
                  const ctrlEnterHandler = (cm: any, event: KeyboardEvent) => {
                    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
                      event.preventDefault();
                      event.stopPropagation();
                      
                      // Ensure we're using the latest state via refs
                      if (messageContentRef.current.trim() && getSelectedRecipientRef.current()) {
                        handleSendMessage();
                      }
                    }
                    
                    // Add Ctrl+Shift+T shortcut for topic selection when a channel is selected
                    if (event.ctrlKey && event.shiftKey && event.key === 't' && selectedChannelId) {
                      event.preventDefault();
                      event.stopPropagation();
                      
                      // Focus and activate topic selector
                      if (addTopicButtonRef.current) {
                        addTopicButtonRef.current.click();
                      }
                    }
                    
                    // Set keyboard mode when using keyboard navigation
                    if (event.key === 'Tab') {
                      setIsKeyboardMode(true);
                    }
                  };
                  
                  // Attach the handler
                  instance.codemirror.on("keydown", ctrlEnterHandler);
                  
                  // Store the handler for potential cleanup
                  (instance as any)._ctrlEnterHandler = ctrlEnterHandler;
                  
                  // Register focus handler
                  instance.codemirror.on("focus", () => {
                    setShowTopicSelector(false);
                  });
                }
              }}
            />
          </div>
          
          <div className="mt-4 flex justify-end">
            <Button
              onClick={handleSendMessage}
              disabled={!recipient || !messageContent.trim()}
            >
              Send message
              {isKeyboardMode && <span className="ml-2 text-xs opacity-75">Ctrl+Enter</span>}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
