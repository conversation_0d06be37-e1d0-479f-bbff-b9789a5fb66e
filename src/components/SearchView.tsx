import React, { useEffect, useState, useRef } from 'react';
import { useApp } from '@/lib/app-context';
import { Message } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from './ui/button';
import { X } from 'lucide-react';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';

export const SearchView: React.FC = () => {
  const {
    searchResults,
    workspace,
    setCurrentChannel,
    setCurrentDirectMessage,
    setActiveThread,
    clearSearchResults,
    setIsSearchViewActive
  } = useApp();

  const { isKeyboardNavigationModeEnabled } = useKeyboardShortcuts();
  const [selectedResultIndex, setSelectedResultIndex] = useState<number>(-1);
  const selectedResultRef = useRef<HTMLDivElement>(null);

  const getConversationName = (item: { type: 'channel' | 'dm'; id: string }) => {
    if (item.type === 'channel') {
      for (const section of workspace.sections) {
        const channel = section.channels.find(ch => ch.id === item.id);
        if (channel) return `#${channel.name}`;
      }
    } else if (item.type === 'dm') {
      const dm = workspace.directMessages.find(d => d.id === item.id);
      if (dm) {
        return dm.participants
          .filter(id => id !== workspace.currentUserId)
          .map(id => workspace.users.find(user => user.id === id)?.name || 'Unknown User')
          .join(', ');
      }
    }
    return 'Unknown Conversation';
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24 && date.getDate() === now.getDate()) {
      return date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
    } else if (diffInHours < 168) { // Within a week
      return date.toLocaleDateString([], { weekday: 'short' }) + ' ' +
             date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' }) + ' ' +
             date.toLocaleTimeString([], { hour: 'numeric', minute: '2-digit' });
    }
  };

  const getUserName = (userId: string) => {
    const user = workspace.users.find(u => u.id === userId);
    return user?.name || 'Unknown User';
  };

  const handleResultClick = (message: Message) => {
    // Basic navigation logic (can be expanded)
    if (message.channelId) {
      const isDm = workspace.directMessages.some(dm => dm.id === message.channelId);
      if (isDm) {
        setCurrentDirectMessage(message.channelId, false); // fromNavigation = false to add to history
      } else {
        setCurrentChannel(message.channelId, false); // fromNavigation = false to add to history
      }
    }
    if (message.threadId) {
      setActiveThread(message.threadId);
    } else {
      // If not a thread, and it's a message in a channel/DM,
      // we might want to highlight it or scroll to it.
      // For now, just opening the channel/DM.
      // setActiveThread(message.id); // This might be incorrect if it's not a parent of a thread
    }
    // After navigating, hide the search view
    setIsSearchViewActive(false);
    clearSearchResults();
  };

  // Handle keyboard navigation in search results
  useEffect(() => {
    if (!searchResults || searchResults.length === 0) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard navigation when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // ESC key to exit search view
      if (e.key === 'Escape') {
        e.preventDefault();
        setIsSearchViewActive(false);
        return;
      }

      // Handle up/down arrow keys and j/k for result navigation
      const isUpNavigation = e.key === 'ArrowUp' || e.key === 'k';
      const isDownNavigation = e.key === 'ArrowDown' || e.key === 'j';
      const isEnterKey = e.key === 'Enter';

      if (!isUpNavigation && !isDownNavigation && !isEnterKey) {
        return;
      }

      e.preventDefault();

      if (isUpNavigation) {
        setSelectedResultIndex(prev => {
          const newIndex = prev <= 0 ? searchResults.length - 1 : prev - 1;
          return newIndex;
        });
      } else if (isDownNavigation) {
        setSelectedResultIndex(prev => {
          const newIndex = prev >= searchResults.length - 1 ? 0 : prev + 1;
          return newIndex;
        });
      } else if (isEnterKey && selectedResultIndex >= 0) {
        // Handle Enter key to select the current result
        handleResultClick(searchResults[selectedResultIndex]);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [searchResults, selectedResultIndex, setIsSearchViewActive]);

  // Scroll selected result into view
  useEffect(() => {
    if (selectedResultIndex >= 0 && selectedResultRef.current) {
      selectedResultRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest'
      });
    }
  }, [selectedResultIndex]);

  if (!searchResults || searchResults.length === 0) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center p-8 app-main-content">
        <p className="text-muted-foreground">No search results to display.</p>
        <Button variant="outline" onClick={() => setIsSearchViewActive(false)} className="mt-4">
          Close Search
        </Button>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col overflow-hidden app-main-content">
      <div className="flex items-center justify-between px-6 py-3 border-b border-app-border">
        <h2 className="text-lg font-semibold text-[var(--app-main-text)]">Search Results ({searchResults.length})</h2>
        <Button variant="ghost" size="icon" onClick={() => setIsSearchViewActive(false)}>
          <X className="h-5 w-5" />
        </Button>
      </div>
      <ScrollArea className="flex-1 p-4">
        <div className="space-y-3">
          {searchResults.map((message, index) => {
            const userName = getUserName(message.userId);
            const location = getConversationName({
              type: message.channelId && workspace.directMessages.find(dm => dm.id === message.channelId) ? 'dm' : 'channel',
              id: message.channelId || ''
            });
            const isSelected = index === selectedResultIndex;
            return (
              <div
                key={message.id}
                ref={isSelected ? selectedResultRef : null}
                className={`p-4 rounded-md border ${isSelected ? 'border-primary ring-2 ring-primary/20' : 'border-app-border'}
                  bg-background hover:bg-muted/50 cursor-pointer`}
                onClick={() => handleResultClick(message)}
                onMouseEnter={() => setSelectedResultIndex(index)}
              >
                <div className="flex items-start justify-between gap-x-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-x-2 mb-1">
                      <span className="font-medium text-sm">{userName}</span>
                      <span className="text-xs text-muted-foreground">in {location}</span>
                      {message.threadId && (
                        <span className="text-xs bg-muted px-1.5 py-0.5 rounded">thread</span>
                      )}
                    </div>
                    <p className="text-sm text-foreground break-words">{message.content}</p>
                  </div>
                  <time className="text-xs text-muted-foreground whitespace-nowrap">
                    {formatTimestamp(message.timestamp)}
                  </time>
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>
    </div>
  );
};