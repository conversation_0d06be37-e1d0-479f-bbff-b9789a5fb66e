import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON><PERSON>T<PERSON>le,
  DialogFooter,
  DialogDescription,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { 
  X,
  CheckCircle2,
  Clock,
  MinusCircle,
  BellOff,
} from 'lucide-react';
import { useApp } from '@/lib/app-context';
import { User } from '@/lib/types';
import { cn } from '@/lib/utils';

interface SetStatusDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

type PredefinedStatus = Exclude<User['status'], undefined | 'offline'>; // 'offline' is handled by clearing

const predefinedStatuses: Array<{
  value: PredefinedStatus;
  label: string;
  description: string;
  icon: React.ReactNode;
  color: string;
}> = [
  {
    value: 'online',
    label: 'Online',
    description: 'Active and available',
    icon: <CheckCircle2 className="h-4 w-4 text-green-500" />,
    color: 'bg-green-500',
  },
  {
    value: 'away',
    label: 'Away',
    description: 'Stepped away temporarily',
    icon: <Clock className="h-4 w-4 text-yellow-500" />,
    color: 'bg-yellow-500',
  },
  {
    value: 'busy',
    label: 'Busy',
    description: 'Not available right now',
    icon: <MinusCircle className="h-4 w-4 text-red-500" />,
    color: 'bg-red-500',
  },
  {
    value: 'dnd',
    label: 'Do Not Disturb',
    description: 'Mute all notifications',
    icon: <BellOff className="h-4 w-4 text-purple-500" />,
    color: 'bg-purple-500',
  },
];

export const SetStatusDialog: React.FC<SetStatusDialogProps> = ({
  open,
  onOpenChange,
}) => {
  const { getCurrentUser, updateUserStatus } = useApp();
  const currentUser = getCurrentUser();

  const [selectedStatus, setSelectedStatus] = useState<PredefinedStatus | ''>(
    currentUser?.status && currentUser.status !== 'offline' ? currentUser.status : 'online'
  );
  const [customMessage, setCustomMessage] = useState(currentUser?.statusMessage || '');

  useEffect(() => {
    if (open && currentUser) {
      setSelectedStatus(currentUser.status && currentUser.status !== 'offline' ? currentUser.status : 'online');
      setCustomMessage(currentUser.statusMessage || '');
    }
  }, [open, currentUser]);

  const handleSaveStatus = () => {
    if (currentUser) {
      const newStatus = selectedStatus || 'online'; // Default to online if somehow empty
      updateUserStatus(currentUser.id, newStatus, customMessage.trim() || undefined);
    }
    onOpenChange(false);
  };

  const handleClearStatus = () => {
    if (currentUser) {
      updateUserStatus(currentUser.id, 'online', undefined); // Clearing sets to online with no message
    }
    setSelectedStatus('online');
    setCustomMessage('');
    onOpenChange(false);
  };

  if (!currentUser) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Set your status</DialogTitle>
          <DialogDescription>
            Let others know your availability
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-3">
            <Label className="text-base font-medium">Status</Label>
            <RadioGroup
              value={selectedStatus}
              onValueChange={(value) => setSelectedStatus(value as PredefinedStatus)}
              className="space-y-2"
            >
              {predefinedStatuses.map((status) => (
                <div key={status.value} className="flex items-center space-x-2">
                  <RadioGroupItem
                    value={status.value}
                    id={status.value}
                    className="peer"
                  />
                  <Label
                    htmlFor={status.value}
                    className="flex flex-1 items-center p-3 -m-3 rounded-md cursor-pointer hover:bg-accent peer-data-[state=checked]:bg-accent peer-data-[state=checked]:border-primary"
                  >
                    <div className="flex items-center flex-1">
                      <div className="flex items-center gap-3">
                        {status.icon}
                        <div>
                          <div className="font-medium">{status.label}</div>
                          <div className="text-xs text-muted-foreground">
                            {status.description}
                          </div>
                        </div>
                      </div>
                      <div className={cn('ml-auto w-2 h-2 rounded-full', status.color)} />
                    </div>
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          <div className="space-y-2">
            <Label htmlFor="custom-status-message" className="text-base font-medium">
              Custom message (optional)
            </Label>
            <div className="relative">
              <Input
                id="custom-status-message"
                placeholder="What's on your mind?"
                value={customMessage}
                onChange={(e) => setCustomMessage(e.target.value)}
                className="pr-8"
                maxLength={60}
              />
              {customMessage && (
                <button
                  onClick={() => setCustomMessage('')}
                  className="absolute right-2 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground"
                  type="button"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
            </div>
            {customMessage && (
              <div className="text-xs text-muted-foreground text-right">
                {60 - customMessage.length} characters remaining
              </div>
            )}
          </div>
        </div>

        <DialogFooter className="sm:justify-between">
          <Button variant="outline" onClick={handleClearStatus}>
            Clear Status
          </Button>
          <div className="flex space-x-2">
            <Button variant="ghost" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveStatus}>Save Status</Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
