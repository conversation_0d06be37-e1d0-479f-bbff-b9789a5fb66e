import React, { useState, useEffect, useRef } from 'react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Search, ChevronLeft, ChevronRight, History, X } from 'lucide-react'; // Removed HelpCircle, Added X
import { useApp } from '@/lib/app-context';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Command, CommandEmpty, CommandGroup, CommandItem, CommandList, CommandSeparator } from '@/components/ui/command';
import { cn } from '@/lib/utils';
import { KeyboardShortcutIndicator } from './KeyboardShortcutIndicator';

interface TopBarProps {
  // Props to control visibility and behavior will be added later
}

const TopBar: React.FC<TopBarProps> = () => {
  const {
    searchHistory,
    searchResults,
    performSearch,
    clearSearchResults,
    // addSearchToHistory, // Not needed directly, performSearch handles it
    clearSearchHistory,
    navigationHistory, // Added
    currentHistoryIndex, // Added
    canGoBack, // Added
    canGoForward, // Added
    navigateBack, // Added
    navigateForward, // Added
    navigateTo, // Added
    recentConversations, // Added
    workspace, // Added to get channel/DM names
    setCurrentChannel, // Added for navigation
    setCurrentDirectMessage, // Added for navigation
    setIsSearchViewActive // Added
  } = useApp();

  const [searchTerm, setSearchTerm] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [isHistoryPopoverOpen, setIsHistoryPopoverOpen] = useState(false);
  const [isSearchHistoryPopoverOpen, setIsSearchHistoryPopoverOpen] = useState(false);
  const [selectedHistoryIndex, setSelectedHistoryIndex] = useState<number>(-1);
  const inputRef = useRef<HTMLInputElement>(null);
  const historyButtonRef = useRef<HTMLButtonElement>(null);

  // Debounce search
  useEffect(() => {
    const handler = setTimeout(() => {
      if (searchTerm.trim()) {
        performSearch(searchTerm);
      } else {
        clearSearchResults();
      }
    }, 300); // 300ms debounce

    return () => {
      clearTimeout(handler);
    };
  }, [searchTerm, performSearch, clearSearchResults]);

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newSearchTerm = event.target.value;
    setSearchTerm(newSearchTerm);
    // Reset search history index when manually typing
    setSearchHistoryIndex(-1);

    if (newSearchTerm.trim()) {
      setIsSearchHistoryPopoverOpen(false); // Close search history popover if user starts typing
    } else if (isSearchFocused && searchHistory.length > 0) {
      setIsSearchHistoryPopoverOpen(true); // Show search history if input becomes empty while focused
    }
  };

  // For search history navigation
  const [searchHistoryIndex, setSearchHistoryIndex] = useState<number>(-1);

  // Handle key events in search input
  const handleSearchKeyDown = (event: React.KeyboardEvent<HTMLInputElement>) => {
    // Handle ESC key to clear search and close search view
    if (event.key === 'Escape') {
      // If search view is active, close it
      if (searchResults && searchResults.length > 0) {
        setIsSearchViewActive(false);
        clearSearchResults();
      }
      // Clear the search input
      setSearchTerm('');
      // Blur the input to allow keyboard navigation to resume
      event.currentTarget.blur();
      return;
    }

    // Handle arrow keys for search history navigation
    if (event.key === 'ArrowUp' && searchHistory.length > 0) {
      event.preventDefault();
      const newIndex = searchHistoryIndex < 0
        ? searchHistory.length - 1
        : Math.max(0, searchHistoryIndex - 1);

      setSearchHistoryIndex(newIndex);
      setSearchTerm(searchHistory[newIndex]);
    } else if (event.key === 'ArrowDown' && searchHistory.length > 0) {
      event.preventDefault();
      if (searchHistoryIndex >= 0) {
        const newIndex = searchHistoryIndex < searchHistory.length - 1
          ? searchHistoryIndex + 1
          : -1;

        setSearchHistoryIndex(newIndex);
        setSearchTerm(newIndex >= 0 ? searchHistory[newIndex] : '');
      }
    }
  };

  const handleSearchSubmit = (event?: React.FormEvent) => {
    event?.preventDefault();
    if (searchTerm.trim()) {
      performSearch(searchTerm); // Ensure results are fresh
      setIsSearchViewActive(true); // Activate search view
      setIsSearchHistoryPopoverOpen(false); // Close search history popover
      setIsHistoryPopoverOpen(false); // Close history popover

      // Blur the input to allow keyboard navigation in search results
      if (inputRef.current) {
        inputRef.current.blur();
      }
    }
  };

  const handleFocus = () => {
    setIsSearchFocused(true);

    // Close history popover when search is focused
    setIsHistoryPopoverOpen(false);

    // Only show search history if there's no search term and there's history
    if (!searchTerm.trim() && searchHistory.length > 0) {
      // Use setTimeout to ensure the search input gets focus first
      setTimeout(() => {
        setIsSearchHistoryPopoverOpen(true);
      }, 0);
    }
  };

  const handleBlur = (event: React.FocusEvent<HTMLInputElement>) => {
    // Use setTimeout to allow click events on popover to process first
    setTimeout(() => {
      if (!document.activeElement?.closest('.cmd-palette')) {
        setIsSearchFocused(false);
        setIsSearchHistoryPopoverOpen(false);
        setSearchHistoryIndex(-1); // Reset search history index when blurring
      }
    }, 100);
  };

  const selectHistoryItem = (term: string) => {
    setSearchTerm(term);
    performSearch(term);
    setIsSearchViewActive(true);
    setIsSearchHistoryPopoverOpen(false);
    inputRef.current?.focus();
  };

  // Handle keyboard navigation for history dropdown
  const handleHistoryKeyDown = (e: React.KeyboardEvent) => {
    // Always stop propagation to prevent message list from capturing events
    e.stopPropagation();

    if (!isHistoryPopoverOpen) {
      // If the history dropdown is not open, open it when pressing the down arrow
      if (e.key === 'ArrowDown') {
        e.preventDefault();
        setIsHistoryPopoverOpen(true);
        setSelectedHistoryIndex(0);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedHistoryIndex(prev =>
          prev < recentConversations.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedHistoryIndex(prev => (prev > 0 ? prev - 1 : 0));
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedHistoryIndex >= 0 && selectedHistoryIndex < recentConversations.length) {
          navigateTo(recentConversations[selectedHistoryIndex]);
          setIsHistoryPopoverOpen(false);
          setSelectedHistoryIndex(-1);
        }
        break;
      case 'Escape':
        e.preventDefault();
        setIsHistoryPopoverOpen(false);
        setSelectedHistoryIndex(-1);
        break;
      // Prevent other keys from propagating
      default:
        if (['Home', 'End', 'PageUp', 'PageDown'].includes(e.key)) {
          e.preventDefault();
        }
        break;
    }
  };

  // Reset selected index when popover closes
  useEffect(() => {
    if (!isHistoryPopoverOpen) {
      setSelectedHistoryIndex(-1);
    }
  }, [isHistoryPopoverOpen]);

  // Add global keyboard event listener for history navigation
  useEffect(() => {
    const handleGlobalKeyDown = (e: KeyboardEvent) => {
      // Check if we're in the history dropdown
      const isInHistoryDropdown = isHistoryPopoverOpen &&
        (document.activeElement?.closest('[cmdk-root]') ||
         document.activeElement?.closest('[role="dialog"]'));

      // If we're in the history dropdown and using arrow keys, don't handle globally
      if (isInHistoryDropdown &&
          (e.key === 'ArrowUp' || e.key === 'ArrowDown' ||
           e.key === 'Enter' || e.key === 'Escape')) {
        return;
      }

      // Don't trigger shortcuts when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable ||
        // Check if search input is focused or trying to focus search
        document.activeElement === inputRef.current ||
        e.key === '/' // Don't intercept the / key which is used for search
      ) {
        return;
      }

      // Toggle history dropdown with H key
      if (e.key === 'h' && !e.ctrlKey && !e.metaKey && !e.altKey && !e.shiftKey) {
        e.preventDefault();
        if (!recentConversations.length) return;

        // Close search history popover if open
        setIsSearchHistoryPopoverOpen(false);

        // Toggle history popover
        setIsHistoryPopoverOpen(prev => !prev);
        if (!isHistoryPopoverOpen) {
          setSelectedHistoryIndex(0);
          // Focus will be handled by the onOpenChange handler
        }
      }
    };

    window.addEventListener('keydown', handleGlobalKeyDown);
    return () => window.removeEventListener('keydown', handleGlobalKeyDown);
  }, [isHistoryPopoverOpen, recentConversations.length]);

  // Separate popovers for history and search history
  const showSearchHistoryPopover = isSearchHistoryPopoverOpen && isSearchFocused && !searchTerm.trim() && searchHistory.length > 0;

  const getConversationName = (item: { type: 'channel' | 'dm'; id: string }) => {
    if (item.type === 'channel') {
      for (const section of workspace.sections) {
        const channel = section.channels.find(ch => ch.id === item.id);
        if (channel) return `# ${channel.name}`;
      }
    } else if (item.type === 'dm') {
      const dm = workspace.directMessages.find(d => d.id === item.id);
      if (dm) {
        return dm.participants
          .filter(id => id !== workspace.currentUserId)
          .map(id => workspace.users.find(user => user.id === id)?.name || 'Unknown User')
          .join(', ');
      }
    }
    return 'Unknown Conversation';
  };

  return (
    <div className="flex items-center h-12 px-4 border-b bg-background">
      <div className="flex items-center space-x-1"> {/* Reduced space for denser nav buttons */}
        <Popover
          open={isHistoryPopoverOpen}
          onOpenChange={(open) => {
            setIsHistoryPopoverOpen(open);

            if (open) {
              // When opening, focus the command component
              setTimeout(() => {
                // Find and focus the command component
                const commandInput = document.querySelector('[cmdk-root]');
                if (commandInput) {
                  (commandInput as HTMLElement).focus();
                }
              }, 0);
            } else {
              // When closing, blur the history button to avoid focus issues
              if (document.activeElement === historyButtonRef.current) {
                // Use setTimeout to ensure the focus is properly released
                setTimeout(() => {
                  historyButtonRef.current?.blur();
                  // Reset focus to the document body
                  document.body.focus();
                }, 0);
              }
            }
          }}
        >
          <PopoverTrigger asChild>
            <Button
              ref={historyButtonRef}
              variant="ghost"
              size="icon"
              aria-label="Show history"
              disabled={recentConversations.length === 0}
              onClick={(e) => {
                // Prevent this click from interfering with search focus
                e.stopPropagation();
                // Close search history popover if open
                setIsSearchHistoryPopoverOpen(false);
                // Toggle history popover
                setIsHistoryPopoverOpen(prev => !prev);
              }}
              onKeyDown={handleHistoryKeyDown}
              // Ensure the button doesn't stay focused after clicking
              onMouseUp={(e) => {
                // Use setTimeout to ensure the click event completes first
                setTimeout(() => {
                  if (document.activeElement === historyButtonRef.current) {
                    historyButtonRef.current?.blur();
                  }
                }, 0);
              }}
            >
              <History className="w-5 h-5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-64 p-0">
            <Command
              onKeyDown={(e) => {
                // Stop propagation to prevent message list from capturing events
                e.stopPropagation();
                handleHistoryKeyDown(e);
              }}
              // Ensure the command doesn't interfere with search focus
              shouldFilter={false}
              // Auto-focus the command when the popover opens
              autoFocus={true}
              // Prevent losing focus
              loop={true}
            >
              <CommandList>
                {recentConversations.length === 0 && <CommandEmpty>No recent history.</CommandEmpty>}
                <CommandGroup heading="Recent Activity">
                  {recentConversations.map((item, index) => (
                    <CommandItem
                      key={`${item.type}-${item.id}`}
                      onSelect={() => {
                        navigateTo(item);
                        setIsHistoryPopoverOpen(false);
                        // Ensure focus is released after selection
                        setTimeout(() => {
                          historyButtonRef.current?.blur();
                        }, 0);
                      }}
                      className={cn(
                        "cursor-pointer",
                        selectedHistoryIndex === index && "bg-accent"
                      )}
                      data-selected={selectedHistoryIndex === index}
                      // Add keyboard event handler to each item
                      onKeyDown={(e) => {
                        // Stop propagation to prevent message list from capturing events
                        e.stopPropagation();
                        if (e.key === 'Enter') {
                          navigateTo(item);
                          setIsHistoryPopoverOpen(false);
                        }
                      }}
                    >
                      {getConversationName(item)}
                    </CommandItem>
                  ))}
                </CommandGroup>
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>
        <Button variant="ghost" size="icon" aria-label="Navigate back" onClick={navigateBack} disabled={!canGoBack}>
          <ChevronLeft className="w-5 h-5" />
        </Button>
        <Button variant="ghost" size="icon" aria-label="Navigate forward" onClick={navigateForward} disabled={!canGoForward}>
          <ChevronRight className="w-5 h-5" />
        </Button>
      </div>

      {/* Search input takes up more space */}
      <div className="flex-grow min-w-0 ml-2 mr-2">
        <div className="relative w-full">
          <form onSubmit={handleSearchSubmit} className="relative w-full">
            <Search className="absolute left-2.5 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              ref={inputRef}
              type="search"
              placeholder="Search all messages..."
              className="w-full pl-8 bg-muted/40 h-9 search-input"
              value={searchTerm}
              onChange={handleSearchChange}
              onKeyDown={handleSearchKeyDown}
              onFocus={handleFocus}
              onBlur={handleBlur}
              onClick={(e) => {
                // Ensure the click doesn't propagate to other handlers
                e.stopPropagation();
                // Make sure the search input gets focus
                inputRef.current?.focus();
                // Close history popover when search is clicked
                setIsHistoryPopoverOpen(false);

                // Show search history if there's no search term and there's history
                if (!searchTerm.trim() && searchHistory.length > 0) {
                  // Use setTimeout to ensure the search input gets focus first
                  setTimeout(() => {
                    setIsSearchHistoryPopoverOpen(true);
                  }, 0);
                }
              }}
            />
          </form>

          {showSearchHistoryPopover && (
            <div className="absolute w-full z-50 mt-1">
              <Command shouldFilter={false} className="[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground border rounded-md shadow-md">
                <CommandList>
                  {searchHistory.length === 0 && (
                    <CommandEmpty>No recent searches.</CommandEmpty>
                  )}

                  {searchHistory.length > 0 && (
                    <CommandGroup heading="Recent Searches">
                      {searchHistory.map((historyTerm) => (
                        <CommandItem
                          key={historyTerm}
                          onSelect={() => selectHistoryItem(historyTerm)}
                          value={`history-${historyTerm}`}
                          className="cursor-pointer"
                        >
                          {historyTerm}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  )}

                  {searchHistory.length > 0 && (
                    <CommandSeparator />
                  )}

                  {searchHistory.length > 0 && (
                    <CommandItem
                      onSelect={() => {
                        clearSearchHistory();
                        setIsSearchHistoryPopoverOpen(false);
                      }}
                      value="clear-history"
                      className="text-red-500 cursor-pointer"
                    >
                      Clear Search History
                    </CommandItem>
                  )}
                </CommandList>
              </Command>
            </div>
          )}
        </div>
      </div>

      {/* Add keyboard shortcut indicator */}
      <div className="ml-2">
        <KeyboardShortcutIndicator />
      </div>
    </div>
  );
};

export default TopBar;