import { But<PERSON> } from "@/components/ui/button";
import { useState, useEffect } from "react"; // Added useEffect
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch"; // Added Switch
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ThemeSelectionGrid } from "./ui/ThemeSelectionGrid";
import { useApp } from "@/lib/app-context"; // Added useApp
import { WorkspaceSettings } from "@/lib/types"; // Added WorkspaceSettings

interface WorkspaceSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WorkspaceSettingsDialog({
  open,
  onOpenChange,
}: WorkspaceSettingsDialogProps) {
  const { workspace, updateWorkspaceSettings } = useApp();
  const [currentSettings, setCurrentSettings] = useState<Partial<WorkspaceSettings>>(workspace?.settings || {});

  useEffect(() => {
    if (workspace?.settings) {
      setCurrentSettings(workspace.settings);
    }
  }, [workspace?.settings, open]); // Reset form when dialog opens or settings change

  const handleSettingChange = (key: keyof WorkspaceSettings, value: any) => {
    setCurrentSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveChanges = () => {
    if (workspace) {
      updateWorkspaceSettings(workspace.id, currentSettings);
      onOpenChange(false); // Close dialog on save
    }
  };

  // Fallback for selectedTheme if not in currentSettings (e.g. initial load)
  const selectedTheme = currentSettings.theme || "default";

  const handleThemeChange = (theme: string) => {
    handleSettingChange('theme', theme);
    // Persistence is handled by updateWorkspaceSettings via handleSaveChanges
    console.log("Selected theme:", theme);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Workspace Settings</DialogTitle>
          <DialogDescription>
            Manage your workspace preferences and settings.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="general" className="mt-4">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>
          <TabsContent value="general" className="py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="workspace-name" className="text-right">
                  Workspace Name
                </Label>
                <Input
                  id="workspace-name"
                  defaultValue="Acme Inc" // This should likely come from workspace.name if displayed
                  className="col-span-3"
                  // value={workspace?.name || "Acme Inc"} // Display actual name
                  // readOnly // Typically workspace name is not changed here, or via a different process
                />
              </div>
              {/* Removed the duplicate Workspace URL input block that was causing confusion */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="workspace-url-display" className="text-right">
                  Workspace URL
                </Label>
                <Input
                  id="workspace-url-display"
                  defaultValue="acme.threadflow.com" // This is likely display-only
                  className="col-span-3"
                  readOnly
                />
              </div>
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <Label htmlFor="suppress-realtime-toast" className="flex flex-col space-y-1">
                  <span>Suppress Realtime Connection Toast</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Hide the toast notification for temporary Realtime connection issues.
                  </span>
                </Label>
                <Switch
                  id="suppress-realtime-toast"
                  checked={currentSettings.suppressRealtimeConnectionToast ?? false}
                  onCheckedChange={(checked) => handleSettingChange('suppressRealtimeConnectionToast', checked)}
                />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="appearance" className="py-4">
            <div className="space-y-4">
              <div>
                <Label>Theme</Label>
                <ThemeSelectionGrid
                  selectedTheme={selectedTheme}
                  onThemeChange={handleThemeChange}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="font-size" className="text-right">
                  Font Size
                </Label>
                <Input
                  id="font-size"
                  type="number"
                  defaultValue={14} // Reverted to defaultValue
                  className="col-span-3"
                />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="notifications" className="py-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Input type="checkbox" id="email-notifications" defaultChecked />
                <Label htmlFor="email-notifications">
                  Email Notifications
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Input type="checkbox" id="desktop-notifications" />
                <Label htmlFor="desktop-notifications">
                  Desktop Notifications
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Input type="checkbox" id="mobile-notifications" />
                <Label htmlFor="mobile-notifications">
                  Mobile Notifications
                </Label>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button onClick={handleSaveChanges}>Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
