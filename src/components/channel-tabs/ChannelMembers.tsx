import React, { useState, useEffect } from 'react';
import { MemberProfileDialog } from '@/components/MemberProfileDialog';
import { Channel, User, WorkspaceDisplayUser } from '@/lib/types';
import { findUserById } from '@/lib/mock-data';
import { Badge } from '@/components/ui/badge';
import { Users, Search, Plus, UserPlus, MoreHorizontal, StopCircle, User as UserIcon, MessageCircle } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { 
  DropdownMenu, 
  DropdownMenuTrigger, 
  DropdownMenuContent, 
  DropdownMenuItem,
  DropdownMenuSeparator
} from '@/components/ui/dropdown-menu';

interface ChannelMembersProps {
  channel: Channel;
}

export const ChannelMembers = ({ channel }: ChannelMembersProps) => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');
  const [isProfileDialogOpen, setIsProfileDialogOpen] = useState(false);
  const [selectedMember, setSelectedMember] = useState<WorkspaceDisplayUser | null>(null);
  
  useEffect(() => {
    console.log("ChannelMembers component - channel:", channel);
    console.log("ChannelMembers component - members:", channel.members);
  }, [channel]);
  
  // Get all member details
  const members = channel.members
    .map(memberId => findUserById(memberId))
    .filter(Boolean) as WorkspaceDisplayUser[];
  
  // Filter members based on search query
  const filteredMembers = members.filter(member => 
    member.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Group members by status
  const onlineMembers = filteredMembers.filter(m => m.status === 'online');
  const busyMembers = filteredMembers.filter(m => m.status === 'away'); // Using 'away' as 'busy' for now
  const offlineMembers = filteredMembers.filter(m => m.status === 'offline' || !m.status);
  
  const handleAddMember = () => {
    // Open dialog to add members
    toast({
      title: "Add members",
      description: "This would open a dialog to add new members to the channel"
    });
  };
 
  const handleMemberAction = (member: WorkspaceDisplayUser, action: string) => {
    switch (action) {
      case 'view':
        setSelectedMember(member);
        setIsProfileDialogOpen(true);
        break;
      case 'message':
        toast({
          title: "Direct message",
          description: `Starting a direct message with ${member.name}`
        });
        break;
      case 'remove':
        if (member.workspaceRole === 'admin') {
          toast({
            title: "Cannot remove admin",
            description: "Admin members cannot be removed from the channel",
            variant: "destructive"
          });
        } else {
          toast({
            title: "Member removed",
            description: `${member.name} has been removed from the channel`
          });
        }
        break;
      default:
        toast({
          title: "Member action",
          description: `${action} for ${member.name}`
        });
    }
  };
  
  const renderMemberGroup = (groupMembers: WorkspaceDisplayUser[], title: string, statusColor: string) => {
    if (groupMembers.length === 0) return null;
    
    return (
      <div className="mb-6">
        <h3 className="text-xs uppercase font-semibold text-gray-500 mb-2 px-4">
          {title} — {groupMembers.length}
        </h3>
        <ul>
          {groupMembers.map(member => (
            <li key={member.id} className="flex items-center px-4 py-2 hover:bg-gray-50 rounded-md">
              <div className="relative">
                <img 
                  src={member.avatar} 
                  alt={member.name}
                  className="w-8 h-8 rounded-full mr-3" 
                />
                <span 
                  className={`absolute bottom-0 right-2 w-3 h-3 rounded-full border-2 border-white ${statusColor}`}
                />
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{member.name}</p>
                {member.title && (
                  <p className="text-xs text-gray-500">{member.title}</p>
                )}
              </div>
              {member.workspaceRole === 'admin' && (
                <Badge variant="outline" className="text-xs mr-2">Admin</Badge>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreHorizontal size={16} />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handleMemberAction(member, 'view')}>
                    <UserIcon size={14} className="mr-2" />
                    View profile
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleMemberAction(member, 'message')}>
                    <MessageCircle size={14} className="mr-2" />
                    Direct message
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem 
                    className="text-red-500"
                    onClick={() => handleMemberAction(member, 'remove')}
                    disabled={member.workspaceRole === 'admin'}
                  >
                    <UserPlus size={14} className="mr-2" />
                    Remove from channel
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </li>
          ))}
        </ul>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Users size={18} />
            <h2 className="font-semibold text-lg">Members</h2>
            <Badge variant="outline">{members.length}</Badge>
          </div>
          
          <Button 
            size="sm"
            onClick={handleAddMember}
            className="flex items-center gap-1"
          >
            <UserPlus size={16} className="mr-1" />
            Add people
          </Button>
        </div>
        
        <div className="relative">
          <Search size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
          <Input
            placeholder="Find members"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 h-8"
          />
        </div>
      </div>
      
      <ScrollArea className="flex-1 p-4">
        {filteredMembers.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">No members found</p>
            {searchQuery && (
              <p className="text-xs text-gray-400 mt-2">
                Try a different search query
              </p>
            )}
          </div>
        ) : (
          <>
            {renderMemberGroup(onlineMembers, 'Online', 'bg-green-500')}
            {renderMemberGroup(busyMembers, 'Busy', 'bg-red-500')}
            {renderMemberGroup(offlineMembers, 'Offline', 'bg-gray-400')}
          </>
        )}
      </ScrollArea>
      {selectedMember && (
        <MemberProfileDialog
          member={selectedMember}
          open={isProfileDialogOpen}
          onOpenChange={setIsProfileDialogOpen}
        />
      )}
    </div>
  );
};
