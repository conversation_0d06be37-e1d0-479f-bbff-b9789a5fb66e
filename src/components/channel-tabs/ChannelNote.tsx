import React, { useState, useMemo } from 'react';
import { Channel } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { Pencil, Undo } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import SimpleMDE from 'react-simplemde-editor';
import ReactMarkdown from 'react-markdown';
import 'easymde/dist/easymde.min.css';

interface ChannelNoteProps {
  channel: Channel;
}

export const ChannelNote = ({ channel }: ChannelNoteProps) => {
  const { toast } = useToast();
  const { updateChannel } = useApp();
  const [isEditing, setIsEditing] = useState(false);
  const [noteContent, setNoteContent] = useState(channel.channelNote || '# Channel Information\n\nCollaborative space for channel information.');
  
  const handleSaveNote = () => {
    const updatedChannel = {
      ...channel,
      channelNote: noteContent
    };
    
    updateChannel(updatedChannel);
    setIsEditing(false);
    
    toast({
      title: "Note updated",
      description: "Channel note has been updated"
    });
  };
  
  const handleNoteChange = (value: string) => {
    setNoteContent(value);
  };
  
  const mdeOptions = useMemo(() => ({
    autofocus: true,
    spellChecker: false,
    status: false,
    previewClass: ["editor-preview", "prose", "max-w-none"],
    toolbar: [
      'bold', 'italic', 'heading', '|',
      'unordered-list', 'ordered-list', '|',
      'link', 'image', '|',
      'preview', 'side-by-side', 'fullscreen'
    ] as ('bold' | 'italic' | 'heading' | '|' | 'unordered-list' | 'ordered-list' | 'link' | 'image' | 'preview' | 'side-by-side' | 'fullscreen')[],
    renderingConfig: {
      singleLineBreaks: false,
    },
    lineWrapping: true,
    indentWithTabs: false,
    tabSize: 2
  }), []);
  
  const renderEditor = () => {
    return (
      <div className="flex flex-col h-full">
        <div className="flex-1 overflow-hidden min-h-0">
          <SimpleMDE
            className="h-full"
            value={noteContent}
            onChange={handleNoteChange}
            options={mdeOptions}
          />
        </div>
        
        <div className="flex justify-between p-3 bg-background border-t">
          <Button 
            variant="ghost" 
            size="sm"
            onClick={() => setIsEditing(false)}
          >
            Cancel
          </Button>
          <div className="flex gap-2">
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setNoteContent(channel.channelNote || '')}
            >
              <Undo size={16} className="mr-1" />
              Reset
            </Button>
            <Button 
              size="sm" 
              onClick={handleSaveNote}
            >
              Done
            </Button>
          </div>
        </div>
      </div>
    );
  };
  
  const renderViewer = () => {
    return (
      <div className="space-y-4">
        <div className="flex justify-end">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => setIsEditing(true)}
            className="flex items-center gap-1"
          >
            <Pencil size={14} />
            Edit
          </Button>
        </div>
        
        <div className="prose max-w-none">
          <ReactMarkdown>{noteContent}</ReactMarkdown>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      <div className="flex-1 overflow-hidden p-4">
        {isEditing ? renderEditor() : renderViewer()}
      </div>
    </div>
  );
};
