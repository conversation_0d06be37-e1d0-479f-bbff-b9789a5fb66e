import { useState, useEffect, useMemo } from 'react';
import { Channel, ChannelTopic } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { Plus, MessageCircle, ArrowLeft } from 'lucide-react';
import { useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/hooks/use-toast';
import { MessageList } from '../MessageList';
import { MessageInput } from '../MessageInput';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface ChannelTopicsProps {
  channel: Channel;
}

export const ChannelTopics = ({ channel }: ChannelTopicsProps) => {
  const { toast } = useToast();
  const [selectedTopic, setSelectedTopic] = useState<ChannelTopic | null>(null);
  const [highlightedTopicId, setHighlightedTopicId] = useState<string | null>(null);
  const [isCreateTopicDialogOpen, setIsCreateTopicDialogOpen] = useState(false);
  const [newTopicName, setNewTopicName] = useState("");
  const [newTopicSummary, setNewTopicSummary] = useState("");
  const { clearActiveChannelTopicForChannel, updateChannel } = useApp();
  const { isKeyboardNavigationModeEnabled } = useKeyboardShortcuts();

  // Mock data for channel topics
  const mockTopics: ChannelTopic[] = channel.channelTopics || [
    {
      id: 'topic1',
      title: 'Project Kickoff Discussion',
      summary: 'Initial planning and task assignments for Q3 project',
      messageIds: ['msg-1', 'msg-2', 'msg-3'],
      createdAt: new Date(Date.now() - 86400000 * 2).toISOString() // 2 days ago
    },
    {
      id: 'topic2',
      title: 'Budget Approval Update',
      summary: 'Updates on the budget approval process and next steps',
      messageIds: ['msg-4', 'msg-5'],
      createdAt: new Date(Date.now() - 86400000).toISOString() // 1 day ago
    },
    {
      id: 'topic3',
      title: 'Team Availability for Next Sprint',
      summary: 'Discussion about team member availability and capacity for upcoming sprint',
      messageIds: ['msg-6', 'msg-7', 'msg-8', 'msg-9'],
      createdAt: new Date().toISOString() // Today
    },
    {
      id: 'topic4',
      title: 'New Feature Requests',
      summary: 'Customer feedback and prioritization of new feature requests',
      messageIds: ['msg-10', 'msg-11'],
      createdAt: new Date().toISOString() // Today
    },
    {
      id: 'topic5',
      title: 'UI/UX Improvements',
      summary: 'Discussion on improving the user interface and experience',
      messageIds: ['msg-12', 'msg-13', 'msg-14'],
      createdAt: new Date(Date.now() - 86400000 * 3).toISOString() // 3 days ago
    }
  ];

  // updateChannel is already declared above

  // Group topics by date
  const groupedTopics: Record<string, ChannelTopic[]> = mockTopics.reduce((groups, topic) => {
    const date = new Date(topic.createdAt).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(topic);
    return groups;
  }, {} as Record<string, ChannelTopic[]>);

  // Sort dates newest to oldest
  const sortedDates = Object.keys(groupedTopics).sort((a, b) =>
    new Date(b).getTime() - new Date(a).getTime()
  );

  useEffect(() => {
    if (channel?.activeChannelTopicId) {
      const topicToSelect = mockTopics.find(t => t.id === channel.activeChannelTopicId);
      if (topicToSelect) {
        setSelectedTopic(topicToSelect);
        // Important: Clear the active topic ID from the context *after* setting it locally
        // to prevent an infinite loop if the parent re-renders and passes the same activeChannelTopicId.
        // Also, this ensures that if the user navigates away and back to this channel's topics tab
        // without a new explicit topic navigation, it doesn't automatically re-select the old topic.
        clearActiveChannelTopicForChannel(channel.id);
      } else {
        // If the topic ID is not found (e.g., stale ID), clear it from context too.
        clearActiveChannelTopicForChannel(channel.id);
      }
    }
  }, [channel?.activeChannelTopicId, channel?.id, clearActiveChannelTopicForChannel, mockTopics]);

  const processNewTopicCreation = () => {
    if (!newTopicName.trim()) {
      toast({
        title: "Error",
        description: "Topic name cannot be empty.",
        variant: "destructive",
      });
      return;
    }

    const newTopicData: ChannelTopic = {
      id: `topic-${Date.now()}`,
      title: newTopicName.trim(),
      summary: newTopicSummary.trim() || "Start a new topic thread",
      messageIds: [],
      createdAt: new Date().toISOString()
    };

    setSelectedTopic(newTopicData); // Select the new topic to view it

    if (channel) {
      const updatedChannel = {
        ...channel,
        // channelTopic: newTopicData.title, // This field seems to be for overall channel topic, not the last created sub-topic
        channelTopics: [...(channel.channelTopics || []), newTopicData]
      };
      updateChannel(updatedChannel); // Changed updateTopic to updateChannel
    }

    toast({
      title: "New Topic Created",
      description: `Topic "${newTopicData.title}" has been created. You can now start a conversation.`
    });

    setNewTopicName("");
    setNewTopicSummary("");
    setIsCreateTopicDialogOpen(false);
  };

  // Handle back to topics list
  const handleBackToTopics = () => {
    setSelectedTopic(null);
  };

  // Handle click on topic to view messages
  const handleTopicClick = (topic: ChannelTopic) => {
    setSelectedTopic(topic);
  };

  // Get a flat list of all topics for keyboard navigation
  const allTopics = useMemo(() => {
    return Object.values(groupedTopics).flat();
  }, [groupedTopics]);

  // Handle keyboard navigation for topics
  useEffect(() => {
    if (!isKeyboardNavigationModeEnabled || selectedTopic) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard navigation when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Check for modifier keys - don't handle navigation if Ctrl+Shift is pressed
      // This ensures we don't interfere with message navigation shortcuts
      const isCtrlShiftPressed = (e.ctrlKey || e.metaKey) && e.shiftKey;
      if (isCtrlShiftPressed) {
        return;
      }

      // Handle up/down arrow keys and k/j for topic navigation
      const isUpNavigation = e.key === 'ArrowUp' || e.key === 'k';
      const isDownNavigation = e.key === 'ArrowDown' || e.key === 'j';
      const isEnterKey = e.key === 'Enter';
      const isNewTopicKey = e.key === 'n' && !e.ctrlKey && !e.metaKey && !e.shiftKey;
      const isHomeKey = e.key === 'Home';
      const isEndKey = e.key === 'End';

      if (!isUpNavigation && !isDownNavigation && !isEnterKey && !isNewTopicKey && !isHomeKey && !isEndKey) {
        return;
      }

      e.preventDefault();

      // Handle new topic shortcut
      if (isNewTopicKey) {
        setIsCreateTopicDialogOpen(true);
        return;
      }

      // Handle Enter to open selected topic
      if (isEnterKey && highlightedTopicId) {
        const topicToOpen = allTopics.find(t => t.id === highlightedTopicId);
        if (topicToOpen) {
          setSelectedTopic(topicToOpen);
        }
        return;
      }

      // Handle Home key to go to first topic
      if (isHomeKey && allTopics.length > 0) {
        setHighlightedTopicId(allTopics[0].id);
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[0].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        return;
      }

      // Handle End key to go to last topic
      if (isEndKey && allTopics.length > 0) {
        const lastIndex = allTopics.length - 1;
        setHighlightedTopicId(allTopics[lastIndex].id);
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[lastIndex].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
        return;
      }

      // Handle navigation between topics
      if (isUpNavigation || isDownNavigation) {
        if (!allTopics.length) return;

        // Find the index of the currently highlighted topic
        const currentIndex = highlightedTopicId
          ? allTopics.findIndex(t => t.id === highlightedTopicId)
          : -1;

        let nextIndex: number;
        if (isUpNavigation) {
          // Move to previous topic or stay at first
          nextIndex = currentIndex > 0 ? currentIndex - 1 : 0;
        } else {
          // Move to next topic or stay at last
          nextIndex = currentIndex < allTopics.length - 1 ? currentIndex + 1 : allTopics.length - 1;
        }

        // If no topic is highlighted yet, select the first or last depending on direction
        if (currentIndex === -1) {
          nextIndex = isUpNavigation ? allTopics.length - 1 : 0;
        }

        // Highlight the next topic
        setHighlightedTopicId(allTopics[nextIndex].id);

        // Scroll the topic into view
        const topicElement = document.querySelector(`[data-topic-id="${allTopics[nextIndex].id}"]`);
        if (topicElement) {
          topicElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [allTopics, highlightedTopicId, isKeyboardNavigationModeEnabled, selectedTopic]);

  // Render topic list view
  const renderTopicList = () => {
    return (
      <>
        <div className="flex items-center justify-between mb-6 px-4">
          <div className="flex items-center gap-2">
            <MessageCircle size={18} />
            <h2 className="font-semibold text-lg">Topics</h2>
            <Badge variant="outline" className="ml-1">{mockTopics.length}</Badge>
          </div>

          <AlertDialog open={isCreateTopicDialogOpen} onOpenChange={setIsCreateTopicDialogOpen}>
            <AlertDialogTrigger asChild>
              <Button
                size="sm"
                onClick={() => setIsCreateTopicDialogOpen(true)}
                className="flex items-center gap-1"
                title="New topic (N)"
              >
                <Plus size={16} />
                <span>New topic</span>
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Create New Topic</AlertDialogTitle>
                <AlertDialogDescription>
                  Enter a name and an optional summary for your new topic.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="topic-name" className="text-right">
                    Name
                  </Label>
                  <Input
                    id="topic-name"
                    value={newTopicName}
                    onChange={(e) => setNewTopicName(e.target.value)}
                    className="col-span-3"
                    placeholder="e.g., Q4 Planning"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="topic-summary" className="text-right">
                    Summary
                  </Label>
                  <Input
                    id="topic-summary"
                    value={newTopicSummary}
                    onChange={(e) => setNewTopicSummary(e.target.value)}
                    className="col-span-3"
                    placeholder="Optional: A brief description"
                  />
                </div>
              </div>
              <AlertDialogFooter>
                <AlertDialogCancel onClick={() => { setNewTopicName(""); setNewTopicSummary(""); }}>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={processNewTopicCreation}>Create Topic</AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>

        {sortedDates.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">No topics found in this channel</p>
          </div>
        ) : (
          sortedDates.map(date => (
            <div key={date} className="mb-6">
              <h3 className="text-xs uppercase font-semibold text-gray-500 mb-2 px-4">{date}</h3>

              {groupedTopics[date].map(topic => (
                <Button
                  key={topic.id}
                  variant="ghost"
                  className={`w-full justify-between py-3 px-4 h-auto font-normal border-b border-gray-100 rounded-none ${
                    highlightedTopicId === topic.id ? 'bg-[var(--app-hover-bg)] ring-2 ring-[var(--app-highlight)]' : ''
                  }`}
                  onClick={() => handleTopicClick(topic)}
                  data-topic-id={topic.id}
                  onMouseEnter={() => setHighlightedTopicId(topic.id)}
                >
                  <div className="flex flex-col items-start text-left">
                    <h4 className="font-medium text-base">{topic.title}</h4>
                    <p className="text-sm text-gray-500 truncate max-w-[500px]">{topic.summary}</p>
                  </div>
                  <Badge variant="secondary" className="text-xs ml-2 self-center">
                    {topic.messageIds.length} messages
                  </Badge>
                </Button>
              ))}
            </div>
          ))
        )}
      </>
    );
  };

  // Add keyboard navigation for topic detail view
  useEffect(() => {
    if (!selectedTopic || !isKeyboardNavigationModeEnabled) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Don't handle keyboard navigation when typing in input fields
      if (
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable
      ) {
        return;
      }

      // Check for modifier keys - don't handle navigation if Ctrl+Shift is pressed
      // This ensures we don't interfere with message navigation shortcuts
      const isCtrlShiftPressed = (e.ctrlKey || e.metaKey) && e.shiftKey;
      if (isCtrlShiftPressed) {
        return;
      }

      // Handle Escape key to go back to topic list
      if (e.key === 'Escape') {
        e.preventDefault();
        handleBackToTopics();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [selectedTopic, isKeyboardNavigationModeEnabled]);

  // We no longer need the renderTopicView function as we're directly rendering the topic view in the return statement

  // If we're showing the topic list, wrap it in ScrollArea
  // If we're showing a specific topic, use a different layout to keep input visible
  if (selectedTopic) {
    // Topic view with fixed header and input, similar to MainContent.tsx implementation
    return (
      <div className="h-full flex flex-col">
        {/* Fixed header */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center mb-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={handleBackToTopics}
              className="h-8 w-8"
              title="Back to topics (Esc)"
            >
              <ArrowLeft size={16} />
            </Button>
            <h3 className="font-semibold text-lg">{selectedTopic.title}</h3>
          </div>
          <p className="text-sm text-gray-500 ml-8">{selectedTopic.summary}</p>
        </div>

        {/* Message list with scrolling - match the exact structure from MainContent.tsx */}
        <MessageList topicId={selectedTopic.id} />

        {/* Fixed input box at bottom */}
        <div className="p-4 border-t border-gray-200">
          <MessageInput topicId={selectedTopic.id} />
        </div>
      </div>
    );
  } else {
    // Topic list with scrolling
    return (
      <ScrollArea className="h-full">
        <div className="p-4">
          {renderTopicList()}
        </div>
      </ScrollArea>
    );
  }
};
