import React, { useState } from 'react';
import { Section, Message } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { findUserById, formatTimestamp } from '@/lib/mock-data';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar, Search, Hash, FileText, MessageSquare, ArrowRight } from 'lucide-react';

interface ProjectActivityProps {
  section: Section;
  recentMessages: Message[];
}

type TimeFilter = 'all' | 'today' | 'week' | 'month';

export const ProjectActivity: React.FC<ProjectActivityProps> = ({ section, recentMessages }) => {
  const { setCurrentChannel, showMemberProfile } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [timeFilter, setTimeFilter] = useState<TimeFilter>('all');
  const [activeTab, setActiveTab] = useState('messages');

  // Filter messages by time period
  const filterMessagesByTime = (messages: Message[], filter: TimeFilter): Message[] => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const weekAgo = new Date(now);
    weekAgo.setDate(now.getDate() - 7);
    const monthAgo = new Date(now);
    monthAgo.setMonth(now.getMonth() - 1);
    
    return messages.filter(msg => {
      const msgDate = new Date(msg.timestamp);
      switch (filter) {
        case 'today':
          return msgDate >= today;
        case 'week':
          return msgDate >= weekAgo;
        case 'month':
          return msgDate >= monthAgo;
        default:
          return true;
      }
    });
  };
  
  // Filter messages by search term
  const filteredMessages = filterMessagesByTime(recentMessages, timeFilter).filter(msg => 
    !searchTerm || msg.content.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Find channel by message
  const findChannelByMessage = (message: Message) => {
    return section.channels.find(ch => 
      ch.messages.some(m => m.id === message.id)
    );
  };
  
  // Calculate message activity by day (for future timeline or graph)
  const calculateMessagesByDay = () => {
    const last7Days = [...Array(7)].map((_, i) => {
      const date = new Date();
      date.setDate(date.getDate() - i);
      return date.toDateString();
    }).reverse();
    
    const messagesByDay: Record<string, number> = {};
    last7Days.forEach(day => {
      messagesByDay[day] = 0;
    });
    
    recentMessages.forEach(msg => {
      const msgDate = new Date(msg.timestamp).toDateString();
      if (messagesByDay[msgDate] !== undefined) {
        messagesByDay[msgDate] += 1;
      }
    });
    
    return messagesByDay;
  };
  
  const messagesByDay = calculateMessagesByDay();
  
  return (
    <div className="flex flex-col h-full space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-[var(--app-main-text)]">Project Activity</h2>
          <p className="text-[var(--app-main-text)]/70">
            Recent activity across {section.name}'s channels
          </p>
        </div>
        <div className="flex space-x-2">
          <div className="relative w-64">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-[var(--app-main-text)]/50" />
            <Input
              placeholder="Search activity..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
          <select 
            className="h-10 px-3 py-2 rounded-md border border-input bg-background text-sm"
            value={timeFilter}
            onChange={(e) => setTimeFilter(e.target.value as TimeFilter)}
          >
            <option value="all">All time</option>
            <option value="today">Today</option>
            <option value="week">This week</option>
            <option value="month">This month</option>
          </select>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
        <div className="border-b border-app-border">
          <TabsList className="h-10 px-4 bg-transparent justify-start">
            <TabsTrigger
              value="messages"
              className="data-[state=active]:border-b-2 data-[state=active]:border-[var(--app-highlight)] data-[state=active]:shadow-none h-9 px-4 rounded-none data-[state=active]:font-medium data-[state=active]:bg-transparent"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              Messages
            </TabsTrigger>
            <TabsTrigger
              value="timeline"
              className="data-[state=active]:border-b-2 data-[state=active]:border-[var(--app-highlight)] data-[state=active]:shadow-none h-9 px-4 rounded-none data-[state=active]:font-medium data-[state=active]:bg-transparent"
            >
              <Calendar className="h-4 w-4 mr-2" />
              Activity Timeline
            </TabsTrigger>
          </TabsList>
        </div>
        
        {/* Messages Tab */}
        <TabsContent value="messages" className="flex-1 overflow-auto pt-6 pb-0 px-0 m-0">
          <Card className="h-full overflow-hidden">
            <CardContent className="p-0 h-full overflow-auto">
              <ScrollArea className="h-full">
                <div className="p-6 space-y-6">
                  {filteredMessages.length === 0 ? (
                    <div className="text-center py-6 text-[var(--app-main-text)]/70">
                      {searchTerm 
                        ? 'No messages match your search' 
                        : `No messages found ${timeFilter !== 'all' ? 'in the selected time period' : ''}`}
                    </div>
                  ) : (
                    filteredMessages.map(message => {
                      const user = findUserById(message.userId);
                      const channel = findChannelByMessage(message);
                      
                      return (
                        <div key={message.id} className="flex space-x-4 pb-6 border-b border-border last:border-0 last:pb-0">
                          <button
                            className="flex-shrink-0"
                            onClick={() => user && showMemberProfile(user.id)}
                          >
                            <Avatar className="h-10 w-10">
                              <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />
                              <AvatarFallback>{user?.name?.[0] || 'U'}</AvatarFallback>
                            </Avatar>
                          </button>
                          
                          <div className="flex-1 space-y-1">
                            <div className="flex items-center flex-wrap">
                              <button 
                                className="font-medium text-[var(--app-main-text)] hover:underline"
                                onClick={() => user && showMemberProfile(user.id)}
                              >
                                {user?.name || 'Unknown user'}
                              </button>
                              <span className="mx-2 text-xs text-[var(--app-main-text)]/60">•</span>
                              <span className="text-xs text-[var(--app-main-text)]/60">{formatTimestamp(message.timestamp)}</span>
                              
                              {channel && (
                                <>
                                  <span className="mx-2 text-xs text-[var(--app-main-text)]/60">•</span>
                                  <button 
                                    className="flex items-center text-xs text-[var(--app-main-text)]/60 hover:underline"
                                    onClick={() => setCurrentChannel(channel.id)}
                                  >
                                    <Hash className="h-3 w-3 mr-1" />
                                    {channel.name}
                                  </button>
                                </>
                              )}
                            </div>
                            
                            <button
                              className="text-[var(--app-main-text)] text-sm hover:underline decoration-dotted text-left w-full"
                              onClick={() => channel && setCurrentChannel(channel.id)}
                            >
                              {message.content}
                            </button>
                            
                            {message.files && message.files.length > 0 && (
                              <div className="flex items-center mt-2 text-xs text-[var(--app-main-text)]/60">
                                <FileText className="h-3 w-3 mr-1" />
                                <span>
                                  {message.files.length} {message.files.length === 1 ? 'file' : 'files'} attached
                                </span>
                              </div>
                            )}
                            
                            {message.reactions && message.reactions.length > 0 && (
                              <div className="flex flex-wrap gap-2 mt-2">
                                {message.reactions.map((reaction, idx) => (
                                  <span 
                                    key={idx} 
                                    className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-[var(--app-hover-bg)]"
                                  >
                                    {reaction.emoji} {reaction.count}
                                  </span>
                                ))}
                              </div>
                            )}
                            
                            {channel && (
                              <div className="mt-2 flex justify-end">
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="h-7 text-xs"
                                  onClick={() => setCurrentChannel(channel.id)}
                                >
                                  View in context <ArrowRight className="ml-1 h-3 w-3" />
                                </Button>
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Timeline Tab */}
        <TabsContent value="timeline" className="flex-1 overflow-auto pt-6 px-0 pb-6 m-0">
          <Card className="h-full">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-8">Message Activity (Last 7 Days)</h3>
              <div className="flex items-end justify-between h-[300px]">
                {Object.entries(messagesByDay).map(([day, count]) => {
                  const maxCount = Math.max(...Object.values(messagesByDay), 1);
                  const height = Math.max((count / maxCount) * 200, count > 0 ? 40 : 4);
                  const dayName = new Date(day).toLocaleDateString(undefined, { weekday: 'short' });
                  
                  return (
                    <div key={day} className="flex flex-col items-center" style={{ width: `${100 / 7}%` }}>
                      <div 
                        className="w-16 bg-[var(--app-highlight)] rounded-t-md"
                        style={{ height: `${height}px` }}
                      />
                      <div className="mt-4 text-sm font-medium">{dayName}</div>
                      <div className="mt-1 text-base font-semibold">{count}</div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 