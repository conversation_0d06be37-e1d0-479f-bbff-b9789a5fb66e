import React from 'react';
import { Section } from '@/lib/types';

interface ProjectFilesProps {
  section: Section;
}

export const ProjectFiles: React.FC<ProjectFilesProps> = ({ section }) => {
  // Placeholder content
  // In a real implementation, you would fetch and display files related to the project section
  
  // Aggregate files from all channels in the section
  const allFiles = section.channels.reduce((acc, channel) => {
    if (channel.files) {
      return acc.concat(channel.files.map(file => ({ ...file, channelName: channel.name })));
    }
    return acc;
  }, [] as Array<{ id: string; name: string; type: string; url: string; size: number; uploadedBy: string; timestamp: string; channelName: string }>);

  return (
    <div className="p-4">
      <h3 className="text-xl font-semibold mb-4">Files in {section.name}</h3>
      {allFiles.length === 0 ? (
        <p className="text-gray-500">No files found in this project section.</p>
      ) : (
        <ul className="space-y-2">
          {allFiles.map(file => (
            <li key={file.id} className="p-2 border rounded-md">
              <a href={file.url} target="_blank" rel="noopener noreferrer" className="font-medium text-blue-600 hover:underline">
                {file.name}
              </a>
              <p className="text-sm text-gray-600">
                Type: {file.type}, Size: {(file.size / 1024).toFixed(2)} KB, Channel: {file.channelName}
              </p>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};