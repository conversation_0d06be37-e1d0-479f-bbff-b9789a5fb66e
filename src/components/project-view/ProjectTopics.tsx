import React, { useState } from 'react';
import { Section, ChannelTopic } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { formatTimestamp } from '@/lib/mock-data';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ArrowRight, Hash, Search, MessageSquare } from 'lucide-react';

interface ProjectTopicsProps {
  section: Section;
  topics: ChannelTopic[];
}

export const ProjectTopics: React.FC<ProjectTopicsProps> = ({ section, topics }) => {
  const { navigateToChannelTopic, setCurrentChannel } = useApp();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState<{
    key: string;
    direction: 'ascending' | 'descending';
  }>({ key: 'createdAt', direction: 'descending' });
  
  // Filter topics based on search
  const filteredTopics = topics.filter(topic => 
    topic.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
    topic.summary.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  // Sort topics
  const sortedTopics = [...filteredTopics].sort((a, b) => {
    if (sortConfig.key === 'createdAt') {
      const comparison = new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      return sortConfig.direction === 'ascending' ? -comparison : comparison;
    } else if (sortConfig.key === 'messageCount') {
      const comparison = b.messageIds.length - a.messageIds.length;
      return sortConfig.direction === 'ascending' ? -comparison : comparison;
    } else if (sortConfig.key === 'title') {
      const comparison = a.title.localeCompare(b.title);
      return sortConfig.direction === 'ascending' ? comparison : -comparison;
    }
    return 0;
  });
  
  // Handle sorting click
  const handleSort = (key: string) => {
    if (sortConfig.key === key) {
      setSortConfig({ 
        ...sortConfig, 
        direction: sortConfig.direction === 'ascending' ? 'descending' : 'ascending'
      });
    } else {
      setSortConfig({ key, direction: 'descending' });
    }
  };
  
  // Get sort direction indicator
  const getSortDirectionIndicator = (key: string) => {
    if (sortConfig.key !== key) return null;
    return sortConfig.direction === 'ascending' ? ' ↑' : ' ↓';
  };
  
  // Find the channel for a topic
  const getTopicChannel = (topicId: string) => {
    return section.channels.find(ch => 
      ch.channelTopics?.some(t => t.id === topicId)
    );
  };
  
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-[var(--app-main-text)]">Project Topics</h2>
          <p className="text-[var(--app-main-text)]/70">
            All discussion topics across {section.name}'s channels
          </p>
        </div>
        <div className="relative w-64">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-[var(--app-main-text)]/50" />
          <Input
            placeholder="Search topics..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-8"
          />
        </div>
      </div>
      
      <Card>
        <CardHeader className="p-0">
          <div className="overflow-hidden rounded-t-lg">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead 
                    className="w-[400px] cursor-pointer"
                    onClick={() => handleSort('title')}
                  >
                    Topic {getSortDirectionIndicator('title')}
                  </TableHead>
                  <TableHead></TableHead>
                  <TableHead 
                    className="text-right cursor-pointer"
                    onClick={() => handleSort('messageCount')}
                  >
                    Messages {getSortDirectionIndicator('messageCount')}
                  </TableHead>
                  <TableHead 
                    className="text-right cursor-pointer"
                    onClick={() => handleSort('createdAt')}
                  >
                    Created {getSortDirectionIndicator('createdAt')}
                  </TableHead>
                  <TableHead className="w-[100px]"></TableHead>
                </TableRow>
              </TableHeader>
            </Table>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[calc(100vh-270px)]">
            <Table>
              <TableBody>
                {sortedTopics.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-6 text-[var(--app-main-text)]/70">
                      {searchTerm ? 'No topics match your search' : 'No topics found in this project'}
                    </TableCell>
                  </TableRow>
                ) : (
                  sortedTopics.map(topic => {
                    const channel = getTopicChannel(topic.id);
                    
                    return (
                      <TableRow key={topic.id}>
                        <TableCell className="align-top">
                          <div>
                            <div className="font-medium">{topic.title}</div>
                            <div className="text-sm text-[var(--app-main-text)]/70 line-clamp-2 mt-1">
                              {topic.summary}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell className="align-top">
                          {channel && (
                            <button 
                              className="flex items-center text-sm hover:underline"
                              onClick={() => setCurrentChannel(channel.id)}
                            >
                              <Hash className="h-3.5 w-3.5 mr-1 flex-shrink-0" />
                              <span>{channel.name}</span>
                            </button>
                          )}
                        </TableCell>
                        <TableCell className="text-right align-top">
                          <div className="flex items-center justify-end">
                            <MessageSquare className="h-3.5 w-3.5 mr-1.5 text-[var(--app-main-text)]/60" />
                            <span>{topic.messageIds.length}</span>
                          </div>
                        </TableCell>
                        <TableCell className="text-right align-top text-sm text-[var(--app-main-text)]/70">
                          {formatTimestamp(topic.createdAt)}
                        </TableCell>
                        <TableCell className="text-right align-top">
                          {channel && (
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-7 ml-auto"
                              onClick={() => navigateToChannelTopic(channel.id, topic.id)}
                            >
                              View <ArrowRight className="ml-1 h-3.5 w-3.5" />
                            </Button>
                          )}
                        </TableCell>
                      </TableRow>
                    );
                  })
                )}
              </TableBody>
            </Table>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}; 