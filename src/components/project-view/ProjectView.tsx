import React, { useState, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { ProjectOverview } from './ProjectOverview';
import { ProjectTopics } from './ProjectTopics';
import { ProjectActivity } from './ProjectActivity';
import { ProjectAnalytics } from './ProjectAnalytics';
import { ProjectFiles } from './ProjectFiles'; // Assuming this will be created
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ProjectViewKey } from '@/lib/types';
import { ScrollArea } from '@/components/ui/scroll-area';

const ALL_PROJECT_VIEWS_ORDERED: ProjectViewKey[] = ['Overview', 'Topics', 'Activity', 'Analytics', 'Files'];

export const ProjectView: React.FC = () => {
  const { currentSection, workspace, getCurrentUser } = useApp();
  const currentUser = getCurrentUser();
  const [activeTab, setActiveTab] = useState<string>('overview');
  const [displayableTabs, setDisplayableTabs] = useState<ProjectViewKey[]>(['Overview']);

  useEffect(() => {
    if (currentSection) {
      const userOverrides = currentUser.settings?.projectViewOverrides?.[currentSection.id];
      const sectionDefault = currentSection.sectionSpecificDefaultViews;
      const workspaceDefault = workspace.settings?.defaultProjectViews;

      let determinedViews: ProjectViewKey[] = [];

      if (userOverrides && userOverrides.length > 0) {
        determinedViews = userOverrides;
      } else if (sectionDefault && sectionDefault.length > 0) {
        determinedViews = sectionDefault;
      } else if (workspaceDefault && workspaceDefault.length > 0) {
        determinedViews = workspaceDefault;
      } else {
        determinedViews = [...ALL_PROJECT_VIEWS_ORDERED]; // Fallback to all views
      }
      
      if (determinedViews.length === 0) {
        determinedViews = ['Overview'];
      }
      
      const newDisplayableTabs = ALL_PROJECT_VIEWS_ORDERED.filter(view => determinedViews.includes(view));
      setDisplayableTabs(newDisplayableTabs);

      const currentActiveTabIsValid = newDisplayableTabs.some(tab => tab.toLowerCase() === activeTab);
      if (newDisplayableTabs.length > 0 && !currentActiveTabIsValid) {
        setActiveTab(newDisplayableTabs[0].toLowerCase());
      } else if (newDisplayableTabs.length === 0) {
        setActiveTab('overview');
      }
    } else {
      setDisplayableTabs(['Overview']);
      setActiveTab('overview');
    }
  }, [currentSection, currentUser.settings, workspace.settings, activeTab]);

  if (!currentSection) {
    return (
      <div className="flex-1 flex items-center justify-center p-6">
        <p className="text-gray-500">No project section selected.</p>
      </div>
    );
  }

  // Aggregate topics and messages for the entire section
  const allTopicsInSection = currentSection.channels.flatMap(ch => ch.channelTopics || []);
  const allMessagesInSection = currentSection.channels.flatMap(ch => ch.messages || []);
  // Sort messages by timestamp descending to get recent ones
  const recentMessagesInSection = [...allMessagesInSection].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());


  return (
    <ScrollArea className="h-full">
      <div className="flex-1 flex flex-col p-6 bg-[var(--app-bg)] text-[var(--app-main-text)]">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
          <div className="border-b border-app-border mb-4">
            <TabsList className="h-10 px-0 bg-transparent justify-start">
              {displayableTabs.map((tabKey) => (
                <TabsTrigger
                  key={tabKey}
                  value={tabKey.toLowerCase()}
                  className="h-9 px-4 text-sm rounded-none data-[state=active]:border-b-2 data-[state=active]:border-[var(--app-highlight)] data-[state=active]:shadow-none data-[state=active]:bg-transparent data-[state=active]:font-medium"
                >
                  {tabKey}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          <div className="flex-1 overflow-auto">
            {displayableTabs.includes('Overview') && (
              <TabsContent value="overview" className="m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ProjectOverview section={currentSection} topics={allTopicsInSection} recentMessages={recentMessagesInSection} />
              </TabsContent>
            )}
            {displayableTabs.includes('Topics') && (
              <TabsContent value="topics" className="m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ProjectTopics section={currentSection} topics={allTopicsInSection} />
              </TabsContent>
            )}
            {displayableTabs.includes('Activity') && (
              <TabsContent value="activity" className="m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ProjectActivity section={currentSection} recentMessages={recentMessagesInSection} />
              </TabsContent>
            )}
            {displayableTabs.includes('Analytics') && (
              <TabsContent value="analytics" className="m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ProjectAnalytics section={currentSection} />
              </TabsContent>
            )}
            {displayableTabs.includes('Files') && (
              <TabsContent value="files" className="m-0 p-0 data-[state=active]:block data-[state=inactive]:hidden">
                <ProjectFiles section={currentSection} />
              </TabsContent>
            )}
          </div>
        </Tabs>
      </div>
    </ScrollArea>
  );
};