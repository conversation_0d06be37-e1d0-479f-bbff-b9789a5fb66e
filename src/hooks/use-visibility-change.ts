import { useState, useEffect } from 'react';

/**
 * Hook to detect when the page visibility changes (tab switching, window focus/blur)
 * and provide a way to handle loading states more gracefully
 */
export function useVisibilityChange() {
  const [isVisible, setIsVisible] = useState(!document.hidden);
  const [isReturningToVisible, setIsReturningToVisible] = useState(false);
  const [loadingAfterReturn, setLoadingAfterReturn] = useState(false);
  
  useEffect(() => {
    const handleVisibilityChange = () => {
      const wasHidden = !isVisible;
      const nowVisible = !document.hidden;
      
      setIsVisible(nowVisible);
      
      // If we're coming back to the page after it was hidden
      if (wasHidden && nowVisible) {
        setIsReturningToVisible(true);
        setLoadingAfterReturn(true);
        
        // After a short delay, reset the returning flag
        // This gives components time to show a loading state if needed
        const returnTimer = setTimeout(() => {
          setIsReturningToVisible(false);
        }, 100);
        
        // After another delay, reset the loading flag
        // This should match approximately how long your data refresh takes
        const loadingTimer = setTimeout(() => {
          setLoadingAfterReturn(false);
        }, 1000);
        
        return () => {
          clearTimeout(returnTimer);
          clearTimeout(loadingTimer);
        };
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);
    window.addEventListener('blur', () => setIsVisible(false));
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
      window.removeEventListener('blur', () => setIsVisible(false));
    };
  }, [isVisible]);
  
  return {
    isVisible,
    isReturningToVisible,
    loadingAfterReturn,
    // Manually reset the loading state if needed
    resetLoadingState: () => setLoadingAfterReturn(false)
  };
}
