import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from './auth-context';
import { mockWorkspace, mockWorkspaces } from './mock-data'; // Retained for fallback and some functions
import { getInitialWorkspaceDataForUser, getMessagesForChannel, getMessagesForDirectMessage, getUserConversationReadStates } from './supabase-data-provider'; // Added getMessagesForDirectMessage and getUserConversationReadStates
import { DirectMessage, Message, Section, Thread, Channel, User, Workspace, UserSettings, WorkspaceSettings, ChannelViewKey, WorkspaceDisplayUser, PersistentUnreadInfo, UserConversationReadState } from './types';
import { deduplicateDirectMessages, findSectionById, findChannelById, transformSupabaseMessage, findOrCreateThreadFromContext, updateOptimisticMessageInArray, mergeWorkspaceData, applyRealtimeMessageToWorkspace, applyOptimisticMessageUpdate, revertOptimisticMessageUpdate, applyReactionUpdateToWorkspace, applyAddChannelUpdateToWorkspace, applyAddSectionUpdateToWorkspace, applyAddDirectMessageUpdateToWorkspace, applyUpdateChannelToWorkspace, applyMarkConversationReadToWorkspace, applyUpdateUserSettingToWorkspace, applyUpdateWorkspaceSettingsToWorkspace, applyUpdateUserStatusToWorkspace, applyNavigateToChannelTopicToWorkspace, applyClearActiveChannelTopicToWorkspace, applySetCurrentSectionToWorkspace, applySetCurrentChannelToWorkspace, applySetCurrentDirectMessageToWorkspace, applySetActiveThreadToWorkspace } from './app-context-utils'; // Added utility imports
import { MemberProfileDialog } from '@/components/MemberProfileDialog';
import { supabase } from './supabaseClient'; // For sendMessage
import { generateDisplayId } from './utils'; // Import generateDisplayId
import { toast } from 'sonner'; // Import toast for notifications
import { AppLoadingScreen } from '@/components/AppLoadingScreen';
import { useVisibilityChange } from '@/hooks/use-visibility-change';

const MESSAGE_FETCH_LIMIT = 10; // Define the message fetch limit
const DELTA_MESSAGE_FETCH_LIMIT = 50; // Define the message fetch limit for delta sync

export type NavigationItem = { type: 'channel'; id: string } | { type: 'dm'; id: string };

interface AppContextType {
  workspace: Workspace | null;
  currentSection: Section | null;
  currentChannel: Channel | null;
  currentDirectMessage: DirectMessage | null;
  currentThread: Thread | null;
  setCurrentSection: (sectionId: string | null) => void;
  setCurrentChannel: (channelId: string | null, fromNavigation?: boolean, fromTopicNavigation?: boolean) => void;
  setCurrentDirectMessage: (directMessageId: string | null, fromNavigation?: boolean) => void;
  setActiveThread: (threadId: string | null) => void;
  sendMessage: (content: string, channelId?: string, directMessageId?: string, threadId?: string, topicId?: string) => Promise<void>; // Made async
  getCurrentUser: () => User;
  toggleSidebar: () => void;
  isSidebarOpen: boolean;
  addReaction: (messageId: string, emoji: string) => void;
  addChannel: (name: string, sectionId: string) => void;
  addSection: (name: string) => void;
  addDirectMessage: (userId: string) => void;
  switchWorkspace: (workspaceId: string) => void;
  updateChannel: (updatedChannel: Channel) => void;
  conversationReadMarkers: Record<string, string | undefined>;
  markConversationRead: (conversationId: string, type: 'channel' | 'dm') => void;
  effectiveMarkAsReadDelaySeconds: number;
  updateUserSetting: (userId: string, newSettings: UserSettings) => void;
  workspaceSettings?: WorkspaceSettings;
  updateWorkspaceSettings: (workspaceId: string, newSettings: Partial<WorkspaceSettings>) => void;
  updateUserStatus: (userId: string, status: User['status'], statusMessage?: string) => void;
  searchHistory: string[];
  searchResults: Message[];
  performSearch: (term: string) => void;
  addSearchToHistory: (term: string) => void;
  clearSearchHistory: () => void;
  clearSearchResults: () => void;
  navigationHistory: NavigationItem[];
  currentHistoryIndex: number;
  canGoBack: boolean;
  canGoForward: boolean;
  navigateBack: () => void;
  navigateForward: () => void;
  navigateTo: (item: NavigationItem) => void;
  recentConversations: NavigationItem[];
  isSearchViewActive: boolean;
  setIsSearchViewActive: (isActive: boolean) => void;
  showMemberProfile: (userId: string) => void;
  navigateToChannelTopic: (channelId: string, topicId: string) => void;
  currentChannelActiveView: ChannelViewKey | null;
  setCurrentChannelActiveView: (view: ChannelViewKey | null) => void;
  clearActiveChannelTopicForChannel: (channelId: string) => void;
  autoResetNewMessages: boolean;
  setAutoResetNewMessages: (value: boolean) => void;
  persistentUnreadInfo: PersistentUnreadInfo;
  setPersistentUnreadInfo: React.Dispatch<React.SetStateAction<PersistentUnreadInfo>>;
  loadOlderMessages: (conversationId: string, conversationType: 'channel' | 'dm') => Promise<void>;
  fetchNewerMessagesForConversation: (conversationId: string, conversationType: 'channel' | 'dm') => Promise<void>;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const { user: authUser, profile: authProfile, session, loading: authLoading } = useAuth();
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [workspaceLoading, setWorkspaceLoading] = useState(true);
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const [conversationReadMarkers, setConversationReadMarkers] = useState<Record<string, string | undefined>>({});
  const [searchHistory, setSearchHistory] = useState<string[]>([]);
  const [searchResults, setSearchResults] = useState<Message[]>([]);
  const [navigationHistory, setNavigationHistory] = useState<NavigationItem[]>([]);
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState<number>(-1);
  const [isSearchViewActive, setIsSearchViewActive] = useState<boolean>(false);
  const [memberProfileDialogOpen, setMemberProfileDialogOpen] = useState<boolean>(false);
  const [selectedMemberId, setSelectedMemberId] = useState<string | null>(null);
  const [currentChannelActiveView, setCurrentChannelActiveView] = useState<ChannelViewKey | null>(null);
  const [autoResetNewMessages, setAutoResetNewMessagesState] = useState<boolean>(true);
  const [persistentUnreadInfo, setPersistentUnreadInfo] = useState<PersistentUnreadInfo>({});
  const [isPuiLoaded, setIsPuiLoaded] = useState(false); // New state for PUI loading status
  const prevVisibilityStateRef = useRef(document.visibilityState);

  // Track visibility changes for better loading experience
  const { isReturningToVisible, loadingAfterReturn, resetLoadingState } = useVisibilityChange();

  const applyInitialUnreadCountsToWorkspaceData = (
    wsData: Workspace,
    pui: PersistentUnreadInfo,
    serverReadStates: UserConversationReadState[] | null
  ): Workspace => {
    const updatedWsData = JSON.parse(JSON.stringify(wsData)) as Workspace; // Deep clone
    // console.log('[UnreadCalc] Applying initial unread counts. Server states:', serverReadStates, 'PUI:', pui);

    if (updatedWsData.sections) {
      updatedWsData.sections.forEach((section: Section) => {
        section.channels.forEach((channel: Channel) => {
          const conversationId = channel.id;
          const dbTimestampStr = channel.lastMessageTimestamp;
          let initialUnreadCount = 0;
          let isPlaceholder = false;
          let usedSource = "fallback (no db timestamp)";

          const serverState = serverReadStates?.find(srs => srs.conversation_id === conversationId);

          if (serverState && serverState.last_read_message_timestamp) {
            usedSource = "server";
            if (dbTimestampStr) {
              const dbDate = new Date(dbTimestampStr);
              const serverReadDate = new Date(serverState.last_read_message_timestamp);
              if (dbDate > serverReadDate) {
                initialUnreadCount = 1;
                isPlaceholder = false;
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
            console.log(`[UnreadCalc CH ${conversationId}] Using SERVER state. DB: ${dbTimestampStr}, ServerRead: ${serverState.last_read_message_timestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
          } else {
            // Fallback to PUI (localStorage)
            const storedInfo = pui[conversationId];
            if (storedInfo && storedInfo.lastReadMessageTimestamp) {
              usedSource = "localStorage";
              if (dbTimestampStr) {
                const dbDate = new Date(dbTimestampStr);
                const storedDate = new Date(storedInfo.lastReadMessageTimestamp);
                if (dbDate > storedDate) {
                  initialUnreadCount = 1;
                  isPlaceholder = false;
                } else {
                  initialUnreadCount = 0;
                  isPlaceholder = false;
                }
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
              // console.log(`[UnreadCalc CH ${conversationId}] No server state, using PUI. DB: ${dbTimestampStr}, PUIRead: ${storedInfo.lastReadMessageTimestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
            } else {
              // No server state and no PUI, or PUI has no timestamp
              usedSource = dbTimestampStr ? "localStorage (no PUI entry, has DB timestamp)" : "localStorage (no PUI entry, no DB timestamp)";
              if (dbTimestampStr) {
                  initialUnreadCount = 1;
                  isPlaceholder = true; // This is the "Case A"
              } else {
                  initialUnreadCount = 0;
                  isPlaceholder = false;
              }
              // console.log(`[UnreadCalc CH ${conversationId}] No server state, no/invalid PUI. DB: ${dbTimestampStr}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
            }
          }
          channel.unreadCount = initialUnreadCount;
          channel.isUnreadCountPlaceholder = isPlaceholder && initialUnreadCount === 1;
          channel.oldestFetchedMessageTimestamp = null; // Initialize pagination flag
          channel.hasMoreOlderMessages = !!channel.lastMessageTimestamp; // Initialize based on whether any message has ever existed
          channel.last_fetched_message_timestamp = null; // Initialize delta sync timestamp
          // console.log(`[UnreadCalc] Channel ${channel.name} (${conversationId}) unread: ${initialUnreadCount}, Placeholder: ${channel.isUnreadCountPlaceholder} (Source: ${usedSource})`);
        });
      });
    }

    if (updatedWsData.directMessages) {
      updatedWsData.directMessages.forEach((dm: DirectMessage) => {
        const conversationId = dm.id;
        const dbTimestampStr = dm.lastMessageTimestamp;
        let initialUnreadCount = 0;
        let isPlaceholder = false;
        let usedSource = "fallback (no db timestamp)";

        const serverState = serverReadStates?.find(srs => srs.conversation_id === conversationId);

        if (serverState && serverState.last_read_message_timestamp) {
          usedSource = "server";
          if (dbTimestampStr) {
            const dbDate = new Date(dbTimestampStr);
            const serverReadDate = new Date(serverState.last_read_message_timestamp);
            if (dbDate > serverReadDate) {
              initialUnreadCount = 1;
              isPlaceholder = false;
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
          } else {
            initialUnreadCount = 0;
            isPlaceholder = false;
          }
          console.log(`[UnreadCalc DM ${conversationId}] Using SERVER state. DB: ${dbTimestampStr}, ServerRead: ${serverState.last_read_message_timestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
        } else {
          const storedInfo = pui[conversationId];
          if (storedInfo && storedInfo.lastReadMessageTimestamp) {
            usedSource = "localStorage";
            if (dbTimestampStr) {
              const dbDate = new Date(dbTimestampStr);
              const storedDate = new Date(storedInfo.lastReadMessageTimestamp);
              if (dbDate > storedDate) {
                initialUnreadCount = 1;
                isPlaceholder = false;
              } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
              }
            } else {
              initialUnreadCount = 0;
              isPlaceholder = false;
            }
            // console.log(`[UnreadCalc DM ${conversationId}] No server state, using PUI. DB: ${dbTimestampStr}, PUIRead: ${storedInfo.lastReadMessageTimestamp}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
          } else {
            usedSource = dbTimestampStr ? "localStorage (no PUI entry, has DB timestamp)" : "localStorage (no PUI entry, no DB timestamp)";
            if (dbTimestampStr) {
                initialUnreadCount = 1;
                isPlaceholder = true; // This is the "Case A"
            } else {
                initialUnreadCount = 0;
                isPlaceholder = false;
            }
            // console.log(`[UnreadCalc DM ${conversationId}] No server state, no/invalid PUI. DB: ${dbTimestampStr}. Unread: ${initialUnreadCount}, Placeholder: ${isPlaceholder}`);
          }
        }
        dm.unreadCount = initialUnreadCount;
        dm.isUnreadCountPlaceholder = isPlaceholder && initialUnreadCount === 1;
        dm.oldestFetchedMessageTimestamp = null; // Initialize pagination flag
        dm.hasMoreOlderMessages = !!dm.lastMessageTimestamp; // Initialize based on whether any message has ever existed
        dm.last_fetched_message_timestamp = null; // Initialize delta sync timestamp
        // console.log(`[UnreadCalc] DM ${dm.id} unread: ${initialUnreadCount}, Placeholder: ${dm.isUnreadCountPlaceholder} (Source: ${usedSource})`);
      });
    }
    return updatedWsData;
  };

  useEffect(() => {
    const loadWorkspaceData = async () => {
      if (authUser && authProfile) { // This is line 148 after additions
        setWorkspaceLoading(true);
        let loadedWorkspace: Workspace | null = null;
        let serverReadStates: UserConversationReadState[] | null = null;

        try {
          // Fetch workspace data and server read states concurrently
          const [fetchedWorkspaceResult, serverReadStatesResult] = await Promise.allSettled([
            getInitialWorkspaceDataForUser(authUser.id),
            getUserConversationReadStates(authUser.id)
          ]);

          if (fetchedWorkspaceResult.status === 'fulfilled' && fetchedWorkspaceResult.value) {
            loadedWorkspace = fetchedWorkspaceResult.value;
          } else {
            console.error("Error fetching initial workspace data:", fetchedWorkspaceResult.status === 'rejected' ? fetchedWorkspaceResult.reason : "No data returned");
            // loadedWorkspace will remain null if the fetch failed or returned null
          }

          if (serverReadStatesResult.status === 'fulfilled' && serverReadStatesResult.value) {
            serverReadStates = serverReadStatesResult.value;
            // console.log("[app-context] Successfully fetched server read states:", serverReadStates);
          } else {
            console.warn("Error fetching server read states, will fallback to localStorage for unread counts:", serverReadStatesResult.status === 'rejected' ? serverReadStatesResult.reason : "No data returned");
            serverReadStates = null; // Ensure it's null on error
          }

        } catch (fetchError) { // Catch any other unforeseen errors from Promise.allSettled or setup
          console.error("Unexpected error during initial data fetch:", fetchError);
          // loadedWorkspace will remain null
        }

        if (loadedWorkspace) {
          if (loadedWorkspace.directMessages && loadedWorkspace.directMessages.length > 0) {
            const dedupedDMs = deduplicateDirectMessages(loadedWorkspace.directMessages, authUser.id);
            if (dedupedDMs.length !== loadedWorkspace.directMessages.length) {
              // console.log(`[app-context] Removed ${loadedWorkspace.directMessages.length - dedupedDMs.length} duplicate DM entries`);
              loadedWorkspace.directMessages = dedupedDMs;
            }
          }
          // Apply initial unread counts using serverReadStates and pui
          loadedWorkspace = applyInitialUnreadCountsToWorkspaceData(loadedWorkspace, persistentUnreadInfo, serverReadStates);
          setWorkspace(prevWorkspace => mergeWorkspaceData(prevWorkspace, loadedWorkspace));
        } else {
          // Handle failure to load initial workspace data
          toast.error("Failed to load live data. Displaying cached/placeholder content. Please check your connection.");
          console.warn("Initial workspace data not found or fetch failed, falling back to mock data.");
          const fallbackMock = mockWorkspaces['w1a2b3c4d'];
          if (fallbackMock) {
            let mockWsToSet: Workspace = {
              ...fallbackMock,
              currentUserId: authUser.id,
              users: fallbackMock.users.map(u =>
                u.id === authUser.id ? { ...u, ...authProfile, id: authUser.id } as WorkspaceDisplayUser : u
              ),
            };
            // Apply initial unread counts to mock data (pass null for serverReadStates as it's a fallback)
            mockWsToSet = applyInitialUnreadCountsToWorkspaceData(mockWsToSet, persistentUnreadInfo, null);
            setWorkspace(mockWsToSet);
          } else {
            console.error("Fallback mock workspace (w1a2b3c4d) not found.");
            setWorkspace(null);
          }
        }
        setWorkspaceLoading(false);
      } else if (!authLoading && !session) {
        setWorkspace(null);
        setWorkspaceLoading(false);
      }
    };

    // Only load workspace data if auth is settled AND persistent unread info is loaded
    if (!authLoading && authUser && authProfile && session && isPuiLoaded) {
      loadWorkspaceData();
    } else if (!authLoading && !session) { // Handle logout case: clear workspace
      setWorkspace(null);
      setWorkspaceLoading(false);
    }
  }, [authUser, authProfile, session, authLoading, isPuiLoaded]); // Dependencies updated

  // Load persistentUnreadInfo from localStorage when authUser is available
  useEffect(() => {
    if (authUser?.id) {
      setIsPuiLoaded(false); // Reset loading status for new user
      const localStorageKey = `unreadStatus_${authUser.id}`;
      try {
        const storedInfo = localStorage.getItem(localStorageKey);
        // console.log('[app-context] LOADED PUI string from LS:', storedInfo); // Added log
        if (storedInfo) {
          const parsedInfo = JSON.parse(storedInfo) as PersistentUnreadInfo;
          // console.log('[app-context] PARSED PUI from LS:', parsedInfo); // Added log
          setPersistentUnreadInfo(parsedInfo);
          // console.log('[app-context] Loaded persistentUnreadInfo from localStorage:', parsedInfo);
        } else {
          // console.log('[app-context] No PUI string in LS, will initialize to empty.'); // Added log for else case
          setPersistentUnreadInfo({}); // Initialize if nothing is stored
          // console.log('[app-context] No persistentUnreadInfo in localStorage, initialized to empty.');
        }
      } catch (error) {
        console.error("Error loading persistentUnreadInfo from localStorage:", error);
        setPersistentUnreadInfo({}); // Initialize on error
      } finally {
        setIsPuiLoaded(true); // Mark as loaded regardless of outcome
      }
    } else {
      // Clear persistent info if user logs out or is not available
      setPersistentUnreadInfo({});
      setIsPuiLoaded(false); // Not loaded if no user
    }
  }, [authUser?.id]); // Depend on authUser.id

  // Save persistentUnreadInfo to localStorage when it changes
  // Save persistentUnreadInfo to localStorage when it or authUser.id changes
  useEffect(() => {
    if (authUser?.id) { // Only proceed if there is an authenticated user
      const localStorageKey = `unreadStatus_${authUser.id}`;
      try {
        if (Object.keys(persistentUnreadInfo).length === 0) {
          localStorage.setItem(localStorageKey, JSON.stringify(persistentUnreadInfo));
          // console.log('[app-context] Saved empty persistentUnreadInfo as "{}" to localStorage.');
        } else {
          // console.log('[app-context] SAVING NON-EMPTY PUI to LS:', persistentUnreadInfo);
          localStorage.setItem(localStorageKey, JSON.stringify(persistentUnreadInfo));
        }
      } catch (error) {
        console.error("Error saving persistentUnreadInfo to localStorage:", error);
      }
    }
  }, [persistentUnreadInfo, authUser?.id]); // Depend on persistentUnreadInfo and authUser.id

  useEffect(() => {
    if (!workspace) return;
    const storedSearchHistory = localStorage.getItem('searchHistory');
    if (storedSearchHistory) setSearchHistory(JSON.parse(storedSearchHistory));

    const storedNavHistory = localStorage.getItem('navigationHistory');
    if (storedNavHistory) {
      const parsedNavHistory = JSON.parse(storedNavHistory);
      setNavigationHistory(parsedNavHistory);
      const currentNavId = workspace.currentChannelId || workspace.currentDirectMessageId;
      const currentNavType = workspace.currentChannelId ? 'channel' : (workspace.currentDirectMessageId ? 'dm' : null);
      if (currentNavId && currentNavType && parsedNavHistory.length > 0) {
        const idx = parsedNavHistory.findIndex((item: NavigationItem) => item.id === currentNavId && item.type === currentNavType);
        setCurrentHistoryIndex(idx > -1 ? idx : parsedNavHistory.length - 1);
      } else if (parsedNavHistory.length > 0) {
        setCurrentHistoryIndex(parsedNavHistory.length - 1);
      }
    }
  }, [workspace]);

  // Effect to refresh data when returning to the tab
  // useEffect(() => {
  //   if (isReturningToVisible && authUser && authProfile) {
  //     // Refresh workspace data when returning to the tab
  //     const refreshData = async () => {
  //       try {
  //         const fetchedWorkspace = await getInitialWorkspaceDataForUser(authUser.id);
  //         if (fetchedWorkspace) {
  //           setWorkspace(prevWorkspace => {
  //             if (!prevWorkspace) return fetchedWorkspace;
  //
  //             // Create a merged workspace similar to the initial load logic
  //             const refreshedWorkspace = {
  //               ...prevWorkspace,
  //               ...fetchedWorkspace
  //             };
  //
  //             // Keep the current state of dynamically loaded content
  //             // This is a simplified version of the merge logic from loadWorkspaceData
  //             refreshedWorkspace.sections = fetchedWorkspace.sections.map(newSection => {
  //               const prevSection = prevWorkspace.sections.find(ps => ps.id === newSection.id);
  //               return {
  //                 ...newSection,
  //                 channels: newSection.channels.map(newChannel => {
  //                   const prevChannel = prevSection?.channels.find(pc => pc.id === newChannel.id);
  //                   if (prevChannel) {
  //                     return {
  //                       ...newChannel,
  //                       messages: prevChannel.messages || [],
  //                       threads: prevChannel.threads || {},
  //                       channelTopics: prevChannel.channelTopics || [],
  //                       files: prevChannel.files || [],
  //                     };
  //                   }
  //                   return newChannel;
  //                 }),
  //               };
  //             });
  //
  //             return refreshedWorkspace;
  //           });
  //         }
  //       } catch (error) {
  //         console.error("Error refreshing workspace data:", error);
  //       } finally {
  //         // Reset loading state when done
  //         resetLoadingState();
  //       }
  //     };
  //
  //     refreshData();
  //   }
  // }, [isReturningToVisible, authUser, authProfile, resetLoadingState]);

  useEffect(() => {
    if (workspace && authUser) {
      const userForEffect = authProfile || workspace.users.find(u => u.id === authUser.id);
      const settingsForEffect = workspace.settings;
      const effectiveSetting = userForEffect?.settings?.autoResetNewMessagesOverride ?? settingsForEffect?.showUnreadBadgeDefault ?? true;
      setAutoResetNewMessagesState(effectiveSetting);
    } else {
      setAutoResetNewMessagesState(true);
    }
  }, [workspace, authUser, authProfile]);


  // Effect to fetch messages when currentChannel changes
  useEffect(() => {
    const channelIdToFetch = workspace?.currentChannelId;
    if (channelIdToFetch && workspace) {
      const fetchMessages = async () => {
        // console.log(`Fetching initial messages for channel: ${channelIdToFetch}`);
        const fetchedMessages = await getMessagesForChannel(channelIdToFetch, MESSAGE_FETCH_LIMIT);
        if (fetchedMessages === null) {
          toast.error("Failed to load messages. Please check your connection.");
          // Optionally, clear existing messages or show an empty state indicator
          // For now, just toast and leave existing messages (if any) or empty array
          setWorkspace(prevWs => {
            if (!prevWs) return null;
            const newSections = prevWs.sections.map(section => ({
              ...section,
              channels: section.channels.map(ch => {
                if (ch.id === channelIdToFetch) {
                  return {
                    ...ch,
                    messages: ch.messages || [], // Keep existing or ensure it's an array
                    hasMoreOlderMessages: false, // Assume no more if fetch failed
                  };
                }
                return ch;
              })
            }));
            return { ...prevWs, sections: newSections };
          });
          return; // Exit early
        }

        setWorkspace(prevWs => {
          if (!prevWs || !prevWs.id || !channelIdToFetch) return prevWs;

          // fetchedMessages from provider are newest-first. Sort them oldest-first for state.
          const sortedMessages = fetchedMessages.slice().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          const numberOfMessagesFetched = sortedMessages.length;
          const newHasMoreFlag = numberOfMessagesFetched === MESSAGE_FETCH_LIMIT;
          const oldestTs = numberOfMessagesFetched > 0 ? sortedMessages[0].timestamp : null;
          const newestTs = numberOfMessagesFetched > 0 ? sortedMessages[numberOfMessagesFetched - 1].timestamp : null;

          if (newestTs) {
            console.log('[InitialMsgLoad CH] About to set last_fetched_ts for', channelIdToFetch, 'to', newestTs, 'oldestTs:', oldestTs, 'hasMoreOlder:', newHasMoreFlag);
            // Store in localStorage for delta sync
            localStorage.setItem(`last_fetched_ts_${channelIdToFetch}`, newestTs);
          }

          const sectionIndex = prevWs.sections.findIndex(s => s.channels.some(c => c.id === channelIdToFetch));
          if (sectionIndex === -1) return prevWs;

          const channelIndex = prevWs.sections[sectionIndex].channels.findIndex(c => c.id === channelIdToFetch);
          if (channelIndex === -1) return prevWs;

          const updatedChannel = {
            ...prevWs.sections[sectionIndex].channels[channelIndex],
            messages: sortedMessages,
            oldestFetchedMessageTimestamp: oldestTs,
            hasMoreOlderMessages: newHasMoreFlag,
            last_fetched_message_timestamp: newestTs || prevWs.sections[sectionIndex].channels[channelIndex].last_fetched_message_timestamp,
          };

          const updatedChannels = [
            ...prevWs.sections[sectionIndex].channels.slice(0, channelIndex),
            updatedChannel,
            ...prevWs.sections[sectionIndex].channels.slice(channelIndex + 1),
          ];

          const updatedSection = {
            ...prevWs.sections[sectionIndex],
            channels: updatedChannels,
          };

          const updatedSections = [
            ...prevWs.sections.slice(0, sectionIndex),
            updatedSection,
            ...prevWs.sections.slice(sectionIndex + 1),
          ];

          return {
            ...prevWs,
            sections: updatedSections,
          };
        });
      };
      // Check if messages are already loaded or if it's a new channel selection
      const currentChannelData = findChannelById(workspace.sections, channelIdToFetch);
      if (!currentChannelData?.messages?.length || currentChannelData.oldestFetchedMessageTimestamp === undefined) {
        fetchMessages();
      }
    }
  }, [workspace?.currentChannelId, workspace?.id]); // Depend on the ID from workspace state

  // Effect to fetch messages when currentDirectMessage changes
  useEffect(() => {
    const dmIdToFetch = workspace?.currentDirectMessageId;
    if (dmIdToFetch && workspace) {
      const fetchDMMessages = async () => {
        // console.log(`Fetching initial messages for DM: ${dmIdToFetch}`);
        const fetchedMessages = await getMessagesForDirectMessage(dmIdToFetch, MESSAGE_FETCH_LIMIT);
        if (fetchedMessages === null) {
          toast.error("Failed to load messages. Please check your connection.");
          setWorkspace(prevWs => {
            if (!prevWs) return null;
            const newDms = prevWs.directMessages.map(dm => {
              if (dm.id === dmIdToFetch) {
                return {
                  ...dm,
                  messages: dm.messages || [],
                  hasMoreOlderMessages: false,
                };
              }
              return dm;
            });
            return { ...prevWs, directMessages: newDms };
          });
          return; // Exit early
        }

        setWorkspace(prevWs => {
          if (!prevWs || !prevWs.id || !dmIdToFetch) return prevWs;

          // fetchedMessages from provider are newest-first. Sort them oldest-first for state.
          const sortedMessages = fetchedMessages.slice().sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          const numberOfMessagesFetched = sortedMessages.length;
          const newHasMoreFlag = numberOfMessagesFetched === MESSAGE_FETCH_LIMIT;
          const oldestTs = numberOfMessagesFetched > 0 ? sortedMessages[0].timestamp : null;
          const newestTs = numberOfMessagesFetched > 0 ? sortedMessages[numberOfMessagesFetched - 1].timestamp : null;

          if (newestTs) {
            console.log('[InitialMsgLoad DM] About to set last_fetched_ts for', dmIdToFetch, 'to', newestTs, 'oldestTs:', oldestTs, 'hasMoreOlder:', newHasMoreFlag);
            // Store in localStorage for delta sync
            localStorage.setItem(`last_fetched_ts_${dmIdToFetch}`, newestTs);
          }

          const dmIndex = prevWs.directMessages.findIndex(d => d.id === dmIdToFetch);
          if (dmIndex === -1) return prevWs;

          const updatedDm = {
            ...prevWs.directMessages[dmIndex],
            messages: sortedMessages,
            oldestFetchedMessageTimestamp: oldestTs,
            hasMoreOlderMessages: newHasMoreFlag,
            last_fetched_message_timestamp: newestTs || prevWs.directMessages[dmIndex].last_fetched_message_timestamp,
          };

          const updatedDirectMessages = [
            ...prevWs.directMessages.slice(0, dmIndex),
            updatedDm,
            ...prevWs.directMessages.slice(dmIndex + 1),
          ];

          return {
            ...prevWs,
            directMessages: updatedDirectMessages,
          };
        });
      };
      const currentDmData = workspace.directMessages.find(dm => dm.id === dmIdToFetch);
      if (!currentDmData?.messages?.length || currentDmData.oldestFetchedMessageTimestamp === undefined) {
        fetchDMMessages();
      }
    }
  }, [workspace?.currentDirectMessageId, workspace?.id]);

  // Effect for Realtime message updates
  useEffect(() => {
    if (!authUser || !workspace?.id) {
      return;
    }

    const messagesChannel = supabase
      .channel(`realtime:messages:${workspace.id}`) // Unique channel name per workspace
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          // We could filter by workspace_id if messages table had it.
          // For now, we'll receive all messages and filter client-side.
        },
        (payload) => {
          // console.log('Realtime: New message received', payload);
          const newMessageFromDb = payload.new as any; // Cast to any to handle snake_case

          // Transform to client-side Message type
          const newMessage = transformSupabaseMessage(newMessageFromDb);

          setWorkspace(prevWs => {
            if (!prevWs || !authUser) return prevWs;

            // Store a copy of unread counts before applying the new message
            const unreadCountsBefore: Record<string, { count: number | undefined, type: 'channel' | 'dm' }> = {};
            prevWs.sections.forEach(s => s.channels.forEach(c => unreadCountsBefore[c.id] = { count: c.unreadCount, type: 'channel' }));
            prevWs.directMessages.forEach(dm => unreadCountsBefore[dm.id] = { count: dm.unreadCount, type: 'dm' });

            const updatedWs = applyRealtimeMessageToWorkspace(prevWs, newMessage, authUser.id);

            // START: Added for Delta Sync localStorage update from Realtime
            if (newMessage.timestamp) {
              // Use newMessageFromDb (raw payload) to get channel_id or dm_id
              const conversationId = newMessageFromDb.channel_id || newMessageFromDb.dm_id;
              if (conversationId) {
                // console.log(`[Realtime] Updating localStorage last_fetched_ts_${conversationId} to ${newMessage.timestamp}`);
                localStorage.setItem(`last_fetched_ts_${conversationId}`, newMessage.timestamp);
              }
            }
            // END: Added for Delta Sync localStorage update from Realtime

            // After applying, check for changes in unread counts for non-active conversations
            if (newMessage.userId !== authUser.id) { // Only consider messages from others
              const activeChannelId = updatedWs.currentChannelId;
              const activeDmId = updatedWs.currentDirectMessageId;

              Object.keys(unreadCountsBefore).forEach(convoId => {
                const oldInfo = unreadCountsBefore[convoId];
                let newUnread: number | undefined;
                let isChannel = false;

                if (oldInfo.type === 'channel') {
                  const ch = updatedWs.sections.flatMap(s => s.channels).find(c => c.id === convoId);
                  if (ch) {
                    newUnread = ch.unreadCount;
                    isChannel = true;
                  }
                } else { // DM
                  const dm = updatedWs.directMessages.find(d => d.id === convoId);
                  if (dm) newUnread = dm.unreadCount;
                }

                const oldUnreadCount = oldInfo.count || 0;
                const currentUnreadCount = newUnread || 0;

                if (currentUnreadCount > oldUnreadCount) {
                  const isActiveConversation = isChannel ? convoId === activeChannelId : convoId === activeDmId;
                  if (!isActiveConversation) {
                    setPersistentUnreadInfo(prevPui => ({
                      ...prevPui,
                      [convoId]: {
                        // Preserve existing lastReadMessageTimestamp, only update unreadCount
                        lastReadMessageTimestamp: prevPui[convoId]?.lastReadMessageTimestamp,
                        unreadCount: currentUnreadCount,
                      }
                    }));
                    // console.log(`[Persistent Unread] RT ${oldInfo.type} ${convoId} unread updated to ${currentUnreadCount}`);
                  }
                }
              });
            }
            return updatedWs;
          });
        }
      )
      .subscribe((status, err) => {
        if (status === 'SUBSCRIBED') {
          // console.log(`Realtime: Subscribed to messages in workspace ${workspace.id}`);
        }
        if (status === 'CHANNEL_ERROR' || status === 'TIMED_OUT' || status === 'CLOSED') {
          console.error(`Realtime: Subscription error for workspace ${workspace.id}`, status, err);
          // Check workspace setting before showing toast
          // Only show toast if suppressRealtimeConnectionToast is false or undefined (default behavior)
          if (!workspace?.settings?.suppressRealtimeConnectionToast) {
            toast.warning("Realtime connection issue. Some updates may be delayed. Attempting to reconnect...");
          }
          // Optionally, implement more sophisticated retry logic or user notification here
        }
      });

    // Cleanup subscription on component unmount or when dependencies change
    return () => {
      // console.log(`Realtime: Unsubscribing from messages in workspace ${workspace.id}`);
      supabase.removeChannel(messagesChannel);
    };
  }, [authUser, workspace?.id]); // Re-subscribe if user or workspace ID changes

  const fetchNewerMessagesForConversation = useCallback(async (conversationId: string, conversationType: 'channel' | 'dm') => {
    if (!workspace || !authUser) {
      // console.log("[DeltaSync] Skipping fetch: no workspace or authUser");
      return;
    }

    let conversation: Channel | DirectMessage | undefined;
    let lastFetchedTimestampFromState: string | null | undefined;

    if (conversationType === 'channel') {
      conversation = findChannelById(workspace.sections, conversationId);
      lastFetchedTimestampFromState = conversation?.last_fetched_message_timestamp;
    } else {
      conversation = workspace.directMessages.find(dm => dm.id === conversationId);
      lastFetchedTimestampFromState = conversation?.last_fetched_message_timestamp;
    }

    // Prioritize localStorage timestamp for delta sync
    const timestampFromLocalStorage = localStorage.getItem(`last_fetched_ts_${conversationId}`);
    const actualSinceTimestampUsed = timestampFromLocalStorage || lastFetchedTimestampFromState;

    console.log('[DeltaSync] Attempting for Conv:', conversationId, 'Type:', conversationType, 'Timestamp from localStorage:', timestampFromLocalStorage, 'Timestamp from state:', lastFetchedTimestampFromState, 'Using sinceTimestamp:', actualSinceTimestampUsed);

    if (!conversation || !actualSinceTimestampUsed) {
      console.log(`[DeltaSync] Skipping for ${conversationType} ${conversationId}. No last fetched timestamp found in localStorage or current state, or conversation not found.`);
      return;
    }

    console.log('[DeltaSync] Calling provider with sinceTimestamp:', actualSinceTimestampUsed);

    try {
      let newerMessages: Message[] | null = null; // These are expected to be newest-first from provider
      if (conversationType === 'channel') {
        newerMessages = await getMessagesForChannel(conversationId, DELTA_MESSAGE_FETCH_LIMIT, undefined, actualSinceTimestampUsed);
      } else {
        newerMessages = await getMessagesForDirectMessage(conversationId, DELTA_MESSAGE_FETCH_LIMIT, undefined, actualSinceTimestampUsed);
      }

      console.log('[DeltaSync] Received newer messages:', newerMessages ? JSON.parse(JSON.stringify(newerMessages)) : null);

      if (newerMessages === null) {
        toast.error("Failed to fetch newer messages. Please check your connection.");
        return;
      }

      if (newerMessages.length > 0) {
        // `newerMessages` from the provider (when using sinceTimestamp) are OLDEST-FIRST.
        // The new `last_fetched_message_timestamp` should be the timestamp of the NEWEST message in this batch.
        const newLastFetchedTimestampForDeltaSync = newerMessages[newerMessages.length - 1].timestamp;

        setWorkspace(prevWs => {
          if (!prevWs) return null;

          let existingMessages: Message[] = [];
          if (conversationType === 'channel') {
            const currentCh = findChannelById(prevWs.sections, conversationId);
            existingMessages = currentCh?.messages || []; // These are oldest-first in state
          } else {
            const currentDm = prevWs.directMessages.find(d => d.id === conversationId);
            existingMessages = currentDm?.messages || []; // These are oldest-first in state
          }
          console.log('[DeltaSync] Messages BEFORE merge for', conversationId, '(Count:', existingMessages.length, '):', existingMessages.map(m => m.id));

          // `newerMessages` (from provider, oldest-first) are the newly fetched messages.
          // `existingMessages` (from state, oldest-first).

          // 1. Combine arrays
          const combinedMessages = [...existingMessages, ...newerMessages];

          // 2. Deduplicate by `id`, keeping the latest instance (from `newerMessages` if IDs collide)
          const messageMap = new Map<string, Message>();
          combinedMessages.forEach(msg => messageMap.set(msg.id, msg));
          const uniqueMessages = Array.from(messageMap.values());

          // 3. Sort uniqueMessages chronologically by `timestamp` (ascending, oldest first)
          const uniqueSortedMessages = uniqueMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());

          console.log('[DeltaSync] newlyFetchedMessages.length for', conversationId, ':', newerMessages.length, 'IDs:', newerMessages.map(m => m.id));
          console.log('[DeltaSync] Messages AFTER merge for', conversationId, '(Count:', uniqueSortedMessages.length, '):', uniqueSortedMessages.map(m => m.id));

          // The overall last message timestamp for the conversation should be the newest in the merged list
          const newOverallLastMessageTimestamp = uniqueSortedMessages.length > 0
            ? uniqueSortedMessages[uniqueSortedMessages.length - 1].timestamp
            : (conversation?.lastMessageTimestamp || newLastFetchedTimestampForDeltaSync);

          console.log(`[DeltaSync] For ${conversationType} ${conversationId}: New last_fetched_message_timestamp (for delta sync): ${newLastFetchedTimestampForDeltaSync}, New overall lastMessageTimestamp (for conversation): ${newOverallLastMessageTimestamp}`);

          // Store updated timestamp in localStorage for delta sync
          localStorage.setItem(`last_fetched_ts_${conversationId}`, newLastFetchedTimestampForDeltaSync);

          if (conversationType === 'channel') {
            const newSections = prevWs.sections.map(section => ({
              ...section,
              channels: section.channels.map(ch => {
                if (ch.id === conversationId) {
                  return {
                    ...ch,
                    messages: uniqueSortedMessages, // Set the correctly sorted messages
                    last_fetched_message_timestamp: newLastFetchedTimestampForDeltaSync, // Timestamp of newest message from THIS fetch
                    lastMessageTimestamp: newOverallLastMessageTimestamp, // Timestamp of absolute newest message in conversation
                  };
                }
                return ch;
              })
            }));
            return { ...prevWs, sections: newSections };
          } else { // Direct Message
            const newDms = prevWs.directMessages.map(dm => {
              if (dm.id === conversationId) {
                return {
                  ...dm,
                  messages: uniqueSortedMessages, // Set the correctly sorted messages
                  last_fetched_message_timestamp: newLastFetchedTimestampForDeltaSync, // Timestamp of newest message from THIS fetch
                  lastMessageTimestamp: newOverallLastMessageTimestamp, // Timestamp of absolute newest message in conversation
                };
              }
              return dm;
            });
            return { ...prevWs, directMessages: newDms };
          }
        });
      } else {
        console.log(`[DeltaSync] No new messages for ${conversationType} ${conversationId} since ${actualSinceTimestampUsed}`);
      }
    } catch (error) {
      console.error(`Error fetching newer messages for ${conversationType} ${conversationId}:`, error);
      toast.error("An error occurred while fetching new messages.");
    }
  }, [workspace, authUser, setWorkspace]);

  // Derive activeConversationObject outside the effect for more precise dependency tracking
  const activeConversationObject = workspace?.currentChannelId
    ? findChannelById(workspace.sections, workspace.currentChannelId)
    : workspace?.currentDirectMessageId
    ? workspace.directMessages.find(dm => dm.id === workspace.currentDirectMessageId)
    : null;

  // Derive the specific timestamp as a primitive value for more precise dependency tracking
  const specificActiveTimestamp = activeConversationObject?.last_fetched_message_timestamp || null;

  // Effect to trigger delta sync on tab visibility change
  // This must be AFTER fetchNewerMessagesForConversation is defined.
  useEffect(() => {
    const wasPreviouslyHidden = prevVisibilityStateRef.current !== 'visible';
    const isNowVisible = document.visibilityState === 'visible';

    if (isNowVisible && wasPreviouslyHidden) {
      console.log('[DeltaSync Visibility] Tab JUST BECAME visible.');
      if (authUser && workspace) {
        const activeConversationId = workspace.currentChannelId || workspace.currentDirectMessageId;
        let conversationType: 'channel' | 'dm' | null = null;

        if (workspace.currentChannelId) {
          conversationType = 'channel';
        } else if (workspace.currentDirectMessageId) {
          conversationType = 'dm';
        }

        if (activeConversationId && conversationType) {
          const timestampFromLocalStorage = localStorage.getItem(`last_fetched_ts_${activeConversationId}`);
          console.log(`[DeltaSync Visibility] Active ID: ${activeConversationId}, Type: ${conversationType}, Timestamp from localStorage: ${timestampFromLocalStorage}`);

          if (timestampFromLocalStorage) {
            fetchNewerMessagesForConversation(activeConversationId, conversationType)
              .catch(error => console.error(`[DeltaSync Visibility] Error during visibility change fetch for ${conversationType} ${activeConversationId}:`, error));
          } else {
            console.log(`[DeltaSync Visibility] Skipping: No timestamp in localStorage on becoming visible for ${conversationType} ${activeConversationId}.`);
          }
        } else {
          console.log(`[DeltaSync Visibility] Skipping: No active conversation or type on becoming visible. Active ID: ${activeConversationId}, Type: ${conversationType}`);
        }
      } else {
        console.log('[DeltaSync Visibility] Skipping: No authUser or workspace on becoming visible.');
      }
    }
    prevVisibilityStateRef.current = document.visibilityState;
  }, [
    document.visibilityState,
    authUser,
    workspace, // For activeConversationId, conversationType, and the conditional check
    fetchNewerMessagesForConversation,
  ]);

  // Show loading screen when:
  // 1. Initial auth or workspace loading - use skeleton for first load
  if (authLoading || workspaceLoading || !workspace || !authUser) {
    return <AppLoadingScreen variant="skeleton" />;
  }

  const updateNavigationHistory = (newHistory: NavigationItem[], newIndex: number) => {
    setNavigationHistory(newHistory);
    setCurrentHistoryIndex(newIndex);
    localStorage.setItem('navigationHistory', JSON.stringify(newHistory));
  };

  const addToNavigationHistory = (item: NavigationItem) => {
    if (navigationHistory[currentHistoryIndex]?.id === item.id && navigationHistory[currentHistoryIndex]?.type === item.type) return;
    const newHistory = navigationHistory.slice(0, currentHistoryIndex + 1);
    newHistory.push(item);
    const limitedHistory = newHistory.slice(-50);
    updateNavigationHistory(limitedHistory, limitedHistory.length - 1);
  };

  const getCurrentUser = (): User => {
    if (authProfile) return { ...authProfile, id: authUser.id } as User;
    const wsUser = workspace.users.find(user => user.id === authUser.id);
    if (wsUser) return wsUser;
    return { id: authUser.id, name: authUser.email || "User", avatar: authUser.user_metadata?.avatar_url || "" } as User;
  };

  // Use utility functions for finding section and channel
  const currentSection = findSectionById(workspace.sections, workspace.currentSectionId);
  const currentChannel = findChannelById(workspace.sections, workspace.currentChannelId);
  const currentDirectMessage = workspace.currentDirectMessageId ? workspace.directMessages.find(dm => dm.id === workspace.currentDirectMessageId) || null : null;

  // Use utility function for finding/creating thread
  const currentThread = workspace.activeThreadId ? findOrCreateThreadFromContext(workspace.activeThreadId, currentChannel || currentDirectMessage) : null;
  const selectedMember = selectedMemberId && workspace ? workspace.users.find(user => user.id === selectedMemberId) || null : null;

  const setCurrentSection = (sectionId: string | null) => {
    setWorkspace(prev => prev ? applySetCurrentSectionToWorkspace(prev, sectionId) : null);
  };

  const setCurrentChannel = (channelId: string | null, fromNavigation = false, fromTopicNavigation = false) => {
    if (!workspace) return;
    if (channelId === workspace.currentChannelId && !fromNavigation && !fromTopicNavigation) return;

    let sectionIdForChannel = workspace.currentSectionId;
    if (channelId) {
      const foundSection = workspace.sections.find(s => s.channels.some(ch => ch.id === channelId));
      if (foundSection) sectionIdForChannel = foundSection.id;
    } else {
      sectionIdForChannel = null; // Clear section if channel is cleared
    }

    setWorkspace(prev => prev ? applySetCurrentChannelToWorkspace(prev, channelId, sectionIdForChannel) : null);

    if (channelId && !fromNavigation && !fromTopicNavigation) addToNavigationHistory({ type: 'channel', id: channelId });
    if (isSearchViewActive && !fromNavigation && !fromTopicNavigation) setIsSearchViewActive(false);
    if (!fromTopicNavigation) setCurrentChannelActiveView(null);
  };

  const setCurrentDirectMessage = (directMessageId: string | null, fromNavigation = false) => {
    // Store the current DM ID for comparison (if active conversation is changing)
    const isChangingConversation = workspace.currentDirectMessageId !== directMessageId;

    // First update the selection state without resetting unread count
    setWorkspace(prev => prev ? applySetCurrentDirectMessageToWorkspace(prev, directMessageId) : null);

    // If we're changing conversations and it's not from a navigation action,
    // add the new DM to navigation history
    if (directMessageId && !fromNavigation) {
      addToNavigationHistory({ type: 'dm', id: directMessageId });
    }

    // Reset search view if needed
    if (isSearchViewActive && !fromNavigation) {
      setIsSearchViewActive(false);
    }

    // Reset channel view
    setCurrentChannelActiveView(null);

    // If we're changing conversations, set a longer delay before resetting the unread count
    // This allows the unread badge to be displayed for a sufficient time before being reset
    if (isChangingConversation && directMessageId) {
      // Increased timeout from 2 seconds to 5 seconds to make the badge more visible
      setTimeout(() => {
        // console.log(`[Debug DM Unread] Resetting unread count for DM ID: ${directMessageId} after timeout`);
        // Only reset the unread count if this DM is still the current one
        setWorkspace(prev => {
          if (!prev || prev.currentDirectMessageId !== directMessageId) return prev;

          // Log current unread count before resetting
          const dmBeforeReset = prev.directMessages.find(dm => dm.id === directMessageId);
          if (dmBeforeReset && dmBeforeReset.unreadCount) {
            // console.log(`[Debug DM Unread] Resetting unread count for DM ID: ${directMessageId} from ${dmBeforeReset.unreadCount} to 0`);
          }

          return {
            ...prev,
            directMessages: prev.directMessages.map(dm =>
              dm.id === directMessageId ? { ...dm, unreadCount: 0 } : dm
            )
          };
        });
      }, 5000); // Increased from 2000 to 5000 milliseconds
    }
  };

  const setActiveThread = (messageId: string | null) => {
    setWorkspace(prev => {
      if (!prev) return null;
      const activeContext = prev.currentChannelId
        ? findChannelById(prev.sections, prev.currentChannelId)
        : (prev.currentDirectMessageId ? prev.directMessages.find(dm => dm.id === prev.currentDirectMessageId) : null);
      return applySetActiveThreadToWorkspace(prev, messageId, activeContext);
    });
  };

  const sendMessage = async (content: string, channelIdParam?: string, directMessageId?: string, threadId?: string, topicIdParam?: string) => {
    if (!content.trim() || !workspace || !authUser) return;

    const messageToInsert = {
      content,
      user_id: authUser.id,
      channel_id: channelIdParam || (directMessageId ? null : currentChannel?.id),
      dm_id: directMessageId || (channelIdParam ? null : currentDirectMessage?.id),
      parent_message_id: threadId,
      topic_id: topicIdParam,
      // Supabase will add id and timestamp
    };

    // Optimistic update
    const clientTempId = `temp-${Date.now()}`;
    const optimisticMessage: Message = {
      id: clientTempId,
      content,
      timestamp: new Date().toISOString(),
      userId: authUser.id,
      threadId,
      channelId: messageToInsert.channel_id || undefined,
      topicId: topicIdParam,
    };

    setWorkspace(prev => {
      if (!prev) return null;
      return applyOptimisticMessageUpdate(prev, optimisticMessage, messageToInsert.channel_id, messageToInsert.dm_id, threadId);
    });

    try {
      const rpcParams = {
        p_content: content,
        p_workspace_id: workspace.id,
        p_channel_id: messageToInsert.channel_id || null,
        p_dm_id: messageToInsert.dm_id || null,
        p_parent_message_id: threadId || null,
        p_topic_id: messageToInsert.topic_id || null,
      };

      const { data: dbMessage, error } = await supabase.rpc('send_message', rpcParams);

      if (error) {
        console.error("Error sending message via RPC:", error);
        // Revert optimistic update
        setWorkspace(prev => {
          if (!prev) return null;
          return revertOptimisticMessageUpdate(prev, clientTempId, messageToInsert.channel_id, messageToInsert.dm_id, threadId);
        });
      } else if (dbMessage) { // dbMessage is the direct object returned by RPC
        // If successful, update the optimistic message with the real ID from the database.
        setWorkspace(prev => {
          if (!prev) return null;
          const updatedWs = JSON.parse(JSON.stringify(prev));
          const targetChannelIdSuccess = messageToInsert.channel_id;
          const targetDMIdSuccess = messageToInsert.dm_id;

          // Transform the dbMessage (which is raw from RPC) to a client-side Message type
          // The RPC returns the newly inserted row, which might have snake_case keys.
          // transformSupabaseMessage handles this.
          const finalMessageFromDb = transformSupabaseMessage(dbMessage);

          if (threadId) {
            if (targetChannelIdSuccess) {
              const ch = updatedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelIdSuccess);
              if (ch && ch.threads[threadId]) {
                ch.threads[threadId].messages = updateOptimisticMessageInArray(ch.threads[threadId].messages, clientTempId, finalMessageFromDb);
              }
            } else if (targetDMIdSuccess) {
              const dm = updatedWs.directMessages.find((d: DirectMessage) => d.id === targetDMIdSuccess);
              if (dm && dm.threads[threadId]) {
                dm.threads[threadId].messages = updateOptimisticMessageInArray(dm.threads[threadId].messages, clientTempId, finalMessageFromDb);
              }
            }
          } else if (targetChannelIdSuccess) {
            const ch = updatedWs.sections.flatMap((s: Section) => s.channels).find((c: Channel) => c.id === targetChannelIdSuccess);
            if (ch) {
              ch.messages = updateOptimisticMessageInArray(ch.messages, clientTempId, finalMessageFromDb);
            }
          } else if (targetDMIdSuccess) {
            const dm = updatedWs.directMessages.find((d: DirectMessage) => d.id === targetDMIdSuccess);
            if (dm) {
              dm.messages = updateOptimisticMessageInArray(dm.messages, clientTempId, finalMessageFromDb);
            }
          }
          return updatedWs;
        });
      }
    } catch (e) {
      console.error("Exception sending message:", e);
      // Consider if additional revert logic is needed here, though the error block above should handle most cases.
    }
  };

  const toggleSidebar = () => setIsSidebarOpen(prev => !prev);

  const addReaction = (messageId: string, emoji: string) => {
    setWorkspace(prev => {
      if (!prev || !authUser) return null;
      return applyReactionUpdateToWorkspace(prev, messageId, emoji, authUser.id);
    });
  };

  const addChannel = async (name: string, sectionId: string) => {
    if (!name.trim() || !sectionId || !workspace || !authUser) return;

    try {
      // 1. Insert the new channel into Supabase
      const { data: newDbChannel, error: channelError } = await supabase
        .from('channels')
        .insert({
          name: name.trim(),
          section_id: sectionId,
          workspace_id: workspace.id,
          is_private: false, // Default to public for now
        })
        .select()
        .single();

      if (channelError) {
        console.error('Error creating channel in Supabase:', channelError);
        if (channelError.code === '42501') { // RLS violation
          toast.error("Permission Denied: You don't have the necessary rights to create a channel in this workspace.");
        } else {
          toast.error(`Failed to create channel: ${channelError.message}`);
        }
        return;
      }

      if (!newDbChannel) {
        console.error('No data returned after creating channel.');
        toast.error('Failed to create channel: No data returned from server.');
        return;
      }

      // 2. Add the current user as a member of the new channel
      const { error: memberError } = await supabase
        .from('channel_members')
        .insert({
          channel_id: newDbChannel.id,
          user_id: authUser.id,
        });

      if (memberError) {
        console.error('Error adding member to new channel:', memberError);
        toast.error(`Failed to set up channel members: ${memberError.message}`);
        // Note: Channel is created but user isn't a member. This state might need more robust handling (e.g., delete the channel).
        // For now, we'll proceed with adding channel to UI but user might not be able to see/use it.
        // Or, simply return and don't add to UI if membership is critical.
        // Let's return for now to avoid inconsistent state in UI.
        return;
      }

      // 3. Construct the client-side Channel object
      const newChannelForState: Channel = {
        id: newDbChannel.id,
        displayId: generateDisplayId(newDbChannel.id, 'c-'),
        name: newDbChannel.name,
        description: newDbChannel.description,
        isPrivate: newDbChannel.is_private,
        members: [authUser.id], // Start with the creator
        messages: [],
        threads: {},
        createdAt: newDbChannel.created_at,
        channelTopics: [],
        files: [],
        // Initialize other optional fields as needed
      };

      // 4. Update the local workspace state
      setWorkspace(prev => {
        if (!prev) return null;
        return applyAddChannelUpdateToWorkspace(prev, newChannelForState, sectionId);
      });
      toast.success(`Channel "${newChannelForState.name}" created!`);

    } catch (error: any) {
      console.error('Exception in addChannel:', error);
      toast.error(`An unexpected error occurred: ${error.message || 'Unknown error'}`);
    }
  };

  const addSection = (name: string) => {
    if (!name.trim() || !workspace) return;
    const newSection: Section = { id: `section-${Date.now()}`, name: name.trim(), channels: [] };
    setWorkspace(prev => prev ? applyAddSectionUpdateToWorkspace(prev, newSection) : null);
  };

  const addDirectMessage = (userId: string) => {
    if (!workspace || !authUser) return;

    // Prevent creating a DM with oneself
    if (userId === authUser.id) {
      console.warn("Attempt to create a DM with oneself. Aborting.");
      // If a DM with self is a feature and might exist, you could try to select it:
      // const selfDm = workspace.directMessages.find(dm => dm.participants.length === 1 && dm.participants[0] === authUser.id);
      // if (selfDm) setCurrentDirectMessage(selfDm.id);
      return;
    }

    // Find all existing DMs with this user
    const existingDms = workspace.directMessages.filter(dm => {
      if (dm.participants.length === 2) {
        return (dm.participants.includes(userId) && dm.participants.includes(authUser.id));
      }
      return false;
    });

    // If we found any, use the first one (preferably one with messages)
    if (existingDms.length > 0) {
      // Prefer DMs with existing messages if available
      const dmWithMessages = existingDms.find(dm => dm.messages && dm.messages.length > 0);
      const dmToUse = dmWithMessages || existingDms[0]; // Use one with messages or just the first one

      console.log(`DM with ${userId} already exists (ID: ${dmToUse.id}). Setting as current.`);
      setCurrentDirectMessage(dmToUse.id);
      return;
    }

    // If no existing DM, create a new one (client-side for now)
    // This part might need to be replaced with an RPC call to create a DM session in the DB
    // and then update the state with the DB-created DM.
    console.log(`Creating new client-side DM object for user ${userId}. This should ideally be a DB operation.`);
    const newDmId = `temp-dm-${Date.now()}`; // Use a prefix to indicate it's temporary/client-side
    const newDm: DirectMessage = {
      id: newDmId,
      displayId: generateDisplayId(newDmId),
      participants: [authUser.id, userId],
      messages: [],
      threads: {},
      unreadCount: 0, // Initialize unread count
      // name will be derived by Sidebar or could be set here if logic exists
    };

    setWorkspace(prev => {
      if (!prev) return null;
      return applyAddDirectMessageUpdateToWorkspace(prev, newDm);
    });
  };

  const switchWorkspace = (workspaceId: string) => {
    const targetWorkspace = mockWorkspaces[workspaceId as keyof typeof mockWorkspaces];
    if (targetWorkspace && authUser && authProfile) { // Ensure authProfile is also available
      setWorkspace({ ...targetWorkspace, currentUserId: authUser.id, users: targetWorkspace.users.map(u => u.id === authUser.id ? {...u, ...authProfile, id: authUser.id} as WorkspaceDisplayUser : u) });
    } else { console.error(`Workspace ${workspaceId} not found or user not authenticated.`); }
  };

  const updateChannel = (updatedChannel: Channel) => {
    setWorkspace(prev => prev ? applyUpdateChannelToWorkspace(prev, updatedChannel) : null);
  };

  const markConversationRead = (conversationId: string, type: 'channel' | 'dm') => {
    if (!workspace) return;

    // Determine the timestamp to use for marking as read
    let timestampToMark: string | undefined;
    let conversationObject: Channel | DirectMessage | null = null;

    if (type === 'channel') {
        conversationObject = findChannelById(workspace.sections, conversationId);
    } else {
        conversationObject = workspace.directMessages.find(dm => dm.id === conversationId) || null;
    }

    if (conversationObject) {
        if (conversationObject.messages && conversationObject.messages.length > 0) {
            // Find the maximum timestamp from the loaded messages by iterating through them
            timestampToMark = conversationObject.messages.reduce((maxTs, message) => {
                return new Date(message.timestamp) > new Date(maxTs) ? message.timestamp : maxTs;
            }, conversationObject.messages[0].timestamp); // Initialize with the first message's timestamp as a starting point
            // console.log(`[markConversationRead] Determined latest from loaded messages: ${timestampToMark} for ${conversationId} (type: ${type})`);
        } else {
            // If no messages loaded client-side, or messages array is empty,
            // use the conversation's last_message_timestamp from DB summary
            timestampToMark = conversationObject.lastMessageTimestamp;
            if (timestampToMark) {
                // console.log(`[markConversationRead] No/empty loaded messages, using DB lastMessageTimestamp: ${timestampToMark} for ${conversationId} (type: ${type})`);
            } else {
                // console.log(`[markConversationRead] No/empty loaded messages and no DB lastMessageTimestamp for ${conversationId} (type: ${type})`);
            }
        }
    } else {
        console.warn(`[markConversationRead] Conversation object not found for ID: ${conversationId}, type: ${type}. Cannot determine timestamp.`);
    }

    // If no timestamp could be determined (e.g., new channel, no messages, no DB timestamp yet),
    // use the current time.
    const finalTimestampToStore = timestampToMark || new Date().toISOString();

    // --- Idempotency Check & Early Exit (Issue 1 Fix) ---
    const puiEntry = persistentUnreadInfo[conversationId];
    const isAlreadyPersistentlyReadCorrectly = puiEntry &&
                                             puiEntry.unreadCount === 0 &&
                                             puiEntry.lastReadMessageTimestamp === finalTimestampToStore;

    let isAlreadyReadInWorkspace = false;
    if (type === 'channel') {
        const ch = findChannelById(workspace.sections, conversationId);
        if (ch && ch.unreadCount === 0) isAlreadyReadInWorkspace = true;
    } else { // dm
        const dm = workspace.directMessages.find(d => d.id === conversationId);
        if (dm && dm.unreadCount === 0) isAlreadyReadInWorkspace = true;
    }

    if (isAlreadyPersistentlyReadCorrectly && isAlreadyReadInWorkspace) {
      // console.log(`[Persistent Unread] SKIPPING mark ${type} ${conversationId}. Already up-to-date with timestamp ${finalTimestampToStore}.`);
      return; // Exit early, no log, no state updates.
    }
    // --- End Idempotency Check ---

    // Call Supabase RPC to mark conversation as read on the server
    if (authUser && workspace) { // Ensure user and workspace are available
      (async () => {
        try {
          const { error } = await supabase.rpc('mark_conversation_as_read', {
            p_conversation_id: conversationId,
            p_conversation_type: type,
            p_last_read_message_timestamp: finalTimestampToStore
          });

          if (error) {
            console.error(`[RPC Error] Failed to mark ${type} ${conversationId} as read on server:`, error);
            // Optionally, notify the user or implement retry logic if critical
          } else {
            // console.log(`[RPC Success] Successfully marked ${type} ${conversationId} as read on server up to ${finalTimestampToStore}`);
          }
        } catch (rpcException) {
          console.error(`[RPC Exception] Unexpected error calling mark_conversation_as_read for ${type} ${conversationId}:`, rpcException);
        }
      })();
    } else {
      console.warn(`[markConversationRead] Skipping RPC call because authUser or workspace is not available. User: ${!!authUser}, Workspace: ${!!workspace}`);
    }

    setConversationReadMarkers(prevMarkers => ({ ...prevMarkers, [conversationId]: finalTimestampToStore }));

    // Update workspace state (sets unreadCount to 0 in the main workspace object)
    setWorkspace(prev => {
      if (!prev) return null;
      return applyMarkConversationReadToWorkspace(prev, conversationId, type);
    });

    // Update persistentUnreadInfo
    setPersistentUnreadInfo(prevPui => {
      const currentPuiEntry = prevPui[conversationId];
      // Avoid redundant update if already correct (double check after other state updates)
      if (currentPuiEntry && currentPuiEntry.unreadCount === 0 && currentPuiEntry.lastReadMessageTimestamp === finalTimestampToStore) {
        return prevPui;
      }
     // console.log(`[Persistent Unread] Marked ${type} ${conversationId} as read. Last read timestamp: ${finalTimestampToStore}`);
     return {
       ...prevPui,
        [conversationId]: {
          unreadCount: 0,
          lastReadMessageTimestamp: finalTimestampToStore
        }
      };
    });
  };

  const userToUse = getCurrentUser();
  const effectiveMarkAsReadDelaySeconds = userToUse?.settings?.markAsReadDelaySecondsOverride ?? workspace.settings?.markAsReadDelaySecondsDefault ?? 5;

  const setAutoResetNewMessages = (value: boolean) => {
    if (!workspace || !authUser) return;
    const currentUserId = getCurrentUser().id;
    const currentUserSettings = getCurrentUser().settings || {};
    updateUserSetting(currentUserId, { ...currentUserSettings, autoResetNewMessagesOverride: value });
  };

  const updateUserSetting = (userId: string, newSettings: UserSettings) => {
    setWorkspace(prev => {
      if (!prev) return null;
      // The mockWorkspaces update is a side effect that will remain here for now.
      const mockWs = mockWorkspaces[prev.id as keyof typeof mockWorkspaces];
      if (mockWs) {
        const mockUserIdx = mockWs.users.findIndex(u => u.id === userId);
        if (mockUserIdx !== -1) mockWs.users[mockUserIdx] = { ...mockWs.users[mockUserIdx], settings: newSettings };
      }
      return applyUpdateUserSettingToWorkspace(prev, userId, newSettings);
    });
  };

  const updateWorkspaceSettings = (workspaceId: string, newSettings: Partial<WorkspaceSettings>) => {
    setWorkspace(prev => {
      if (!prev || prev.id !== workspaceId) return prev;
      // The mockWorkspaces update is a side effect that will remain here for now.
      const mockWs = mockWorkspaces[workspaceId as keyof typeof mockWorkspaces];
      if (mockWs) mockWs.settings = { ...mockWs.settings, ...newSettings }; // Ensure mock settings are also spread
      return applyUpdateWorkspaceSettingsToWorkspace(prev, newSettings);
    });
  };

  const updateUserStatus = (userId: string, status: User['status'], statusMessage?: string) => {
    setWorkspace(prev => {
      if (!prev) return null;
      // The mockWorkspaces update is a side effect that will remain here for now.
      const mockWs = mockWorkspaces[prev.id as keyof typeof mockWorkspaces];
      if (mockWs) {
        const mockUserIdx = mockWs.users.findIndex(u => u.id === userId);
        if (mockUserIdx !== -1) mockWs.users[mockUserIdx] = { ...mockWs.users[mockUserIdx], status, statusMessage };
      }
      return applyUpdateUserStatusToWorkspace(prev, userId, status, statusMessage);
    });
  };

  const addSearchToHistory = (term: string) => {
    if (!term.trim()) return;
    setSearchHistory(prev => {
      const newHistory = [term, ...prev.filter(t => t !== term)].slice(0, 10);
      localStorage.setItem('searchHistory', JSON.stringify(newHistory));
      return newHistory;
    });
  };

  const clearSearchHistory = () => { setSearchHistory([]); localStorage.removeItem('searchHistory'); };

  const performSearch = (term: string) => {
    if (!term.trim() || !workspace) { setSearchResults([]); return; }
    const lowerCaseTerm = term.toLowerCase();
    const results: Message[] = [];
    workspace.sections.forEach(s => s.channels.forEach(c => {
      c.messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: c.id }); });
      Object.values(c.threads).forEach(th => (th as Thread).messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: c.id, threadId: th.id }); }));
    }));
    workspace.directMessages.forEach(dm => {
      dm.messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: dm.id }); });
      Object.values(dm.threads).forEach(th => (th as Thread).messages.forEach(m => { if (m.content.toLowerCase().includes(lowerCaseTerm)) results.push({ ...m, channelId: dm.id, threadId: th.id }); }));
    });
    setSearchResults(results);
    if (results.length > 0) addSearchToHistory(term);
  };

  const clearSearchResults = () => setSearchResults([]);

  const canGoBack = currentHistoryIndex > 0;
  const canGoForward = currentHistoryIndex < navigationHistory.length - 1;

  const navigateBack = () => {
    if (canGoBack) {
      const newIndex = currentHistoryIndex - 1;
      const item = navigationHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      if (item.type === 'channel') setCurrentChannel(item.id, true); else setCurrentDirectMessage(item.id, true);
    }
  };

  const navigateForward = () => {
    if (canGoForward) {
      const newIndex = currentHistoryIndex + 1;
      const item = navigationHistory[newIndex];
      setCurrentHistoryIndex(newIndex);
      if (item.type === 'channel') setCurrentChannel(item.id, true); else setCurrentDirectMessage(item.id, true);
    }
  };

  const navigateTo = (item: NavigationItem) => {
    if (item.type === 'channel') setCurrentChannel(item.id, false); else setCurrentDirectMessage(item.id, false);
  };

  const recentConversations = navigationHistory.slice().reverse().filter((item, index, self) => index === self.findIndex((t) => t.id === item.id && t.type === item.type)).slice(0, 15);
  const showMemberProfile = (userId: string) => { setSelectedMemberId(userId); setMemberProfileDialogOpen(true); };

  const navigateToChannelTopic = (channelId: string, topicId: string) => {
    setCurrentChannel(channelId, false, true); // This already handles some state updates
    setWorkspace(prev => prev ? applyNavigateToChannelTopicToWorkspace(prev, channelId, topicId) : null);
    setCurrentChannelActiveView('Topics');
    if (isSearchViewActive) setIsSearchViewActive(false);
  };

  const clearActiveChannelTopicForChannel = (channelId: string) => {
    setWorkspace(prev => prev ? applyClearActiveChannelTopicToWorkspace(prev, channelId) : null);
  };


  const loadOlderMessages = async (conversationId: string, conversationType: 'channel' | 'dm') => {
    if (!workspace) return;

    let conversation: Channel | DirectMessage | undefined;
    if (conversationType === 'channel') {
      conversation = findChannelById(workspace.sections, conversationId);
    } else {
      conversation = workspace.directMessages.find(dm => dm.id === conversationId);
    }

    if (!conversation || !conversation.hasMoreOlderMessages) {
      console.log(`[LoadOlder] Skipping for ${conversationType} ${conversationId}. No conversation or no more messages.`);
      return;
    }

    // If oldestFetchedMessageTimestamp is null but we have messages, use the oldest message's timestamp
    let currentOldestTsFromState = conversation.oldestFetchedMessageTimestamp;
    if (!currentOldestTsFromState && conversation.messages && conversation.messages.length > 0) {
      currentOldestTsFromState = conversation.messages[0].timestamp;
      console.log(`[LoadOlder] Using oldest message timestamp as fallback: ${currentOldestTsFromState}`);
    }

    if (!currentOldestTsFromState) {
      console.log(`[LoadOlder] Skipping for ${conversationType} ${conversationId}. No timestamp available.`);
      return;
    }
    console.log('[LoadOlder] Called for conv:', conversationId, 'type:', conversationType, 'using olderThan:', currentOldestTsFromState);

    try {
      let olderMessagesArray: Message[] | null = null; // Renamed from olderMessages to avoid confusion
      if (conversationType === 'channel') {
        olderMessagesArray = await getMessagesForChannel(conversationId, MESSAGE_FETCH_LIMIT, currentOldestTsFromState);
      } else {
        olderMessagesArray = await getMessagesForDirectMessage(conversationId, MESSAGE_FETCH_LIMIT, currentOldestTsFromState);
      }

      console.log('[LoadOlder] Received older messages:', olderMessagesArray ? JSON.parse(JSON.stringify(olderMessagesArray)) : null);

      if (olderMessagesArray === null) {
        toast.error("Failed to load older messages. Please check your connection.");
        setWorkspace(prevWs => {
          if (!prevWs) return null;
          if (conversationType === 'channel') {
            return {
              ...prevWs,
              sections: prevWs.sections.map(s => ({
                ...s,
                channels: s.channels.map(ch => ch.id === conversationId ? { ...ch, hasMoreOlderMessages: false } : ch)
              }))
            };
          } else {
            return {
              ...prevWs,
              directMessages: prevWs.directMessages.map(dm => dm.id === conversationId ? { ...dm, hasMoreOlderMessages: false } : dm)
            };
          }
        });
        return;
      }

      setWorkspace(prevWs => {
        if (!prevWs || !prevWs.id) return prevWs;

        // olderMessagesArray is newest-first from provider. Reverse to make it oldest-first.
        const olderMessagesOldestFirst = olderMessagesArray!.slice().reverse();
        const numberOfNewlyFetchedOlderMessages = olderMessagesArray!.length;
        const newHasMoreFlag = numberOfNewlyFetchedOlderMessages === MESSAGE_FETCH_LIMIT;

        if (conversationType === 'channel') {
          const sectionIndex = prevWs.sections.findIndex(s => s.channels.some(c => c.id === conversationId));
          if (sectionIndex === -1) return prevWs;

          const channelIndex = prevWs.sections[sectionIndex].channels.findIndex(c => c.id === conversationId);
          if (channelIndex === -1) return prevWs;

          const currentChannel = prevWs.sections[sectionIndex].channels[channelIndex];
          // console.log('[LoadOlder CH] Messages BEFORE prepend for', conversationId, ':', JSON.parse(JSON.stringify(currentChannel.messages)));

          const combinedMessages = [...olderMessagesOldestFirst, ...currentChannel.messages];
          const uniqueMessages = Array.from(new Map(combinedMessages.map(msg => [msg.id, msg])).values())
                                          .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          // console.log('[LoadOlder CH] Messages AFTER prepend & sort for', conversationId, ':', JSON.parse(JSON.stringify(uniqueMessages)));

          const newOldestTs = olderMessagesOldestFirst.length > 0
                                ? olderMessagesOldestFirst[0].timestamp
                                : currentChannel.oldestFetchedMessageTimestamp;

          const currentOverallNewestTimestamp = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].timestamp : currentChannel.lastMessageTimestamp;

          console.log('[LoadOlder CH] About to set oldestFetchedMessageTimestamp:', newOldestTs, 'hasMoreOlderMessages:', newHasMoreFlag, 'for channel', conversationId);

          const updatedChannel = {
            ...currentChannel,
            messages: uniqueMessages,
            oldestFetchedMessageTimestamp: newOldestTs,
            hasMoreOlderMessages: newHasMoreFlag,
            lastMessageTimestamp: currentOverallNewestTimestamp,
            // Update last_fetched_message_timestamp if it was null before and we loaded messages
            last_fetched_message_timestamp: currentChannel.last_fetched_message_timestamp || (uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length - 1].timestamp : currentChannel.last_fetched_message_timestamp),
          };

          // If last_fetched_message_timestamp was updated from null, store in localStorage
          if (!currentChannel.last_fetched_message_timestamp && uniqueMessages.length > 0) {
            const newTimestamp = uniqueMessages[uniqueMessages.length - 1].timestamp;
            localStorage.setItem(`last_fetched_ts_${conversationId}`, newTimestamp);
          }

          const updatedChannels = [
            ...prevWs.sections[sectionIndex].channels.slice(0, channelIndex),
            updatedChannel,
            ...prevWs.sections[sectionIndex].channels.slice(channelIndex + 1),
          ];

          const updatedSection = {
            ...prevWs.sections[sectionIndex],
            channels: updatedChannels,
          };

          const updatedSections = [
            ...prevWs.sections.slice(0, sectionIndex),
            updatedSection,
            ...prevWs.sections.slice(sectionIndex + 1),
          ];

          return {
            ...prevWs,
            sections: updatedSections,
          };

        } else { // Direct Message
          const dmIndex = prevWs.directMessages.findIndex(d => d.id === conversationId);
          if (dmIndex === -1) return prevWs;

          const currentDm = prevWs.directMessages[dmIndex];
          // console.log('[LoadOlder DM] Messages BEFORE prepend for', conversationId, ':', JSON.parse(JSON.stringify(currentDm.messages)));

          const combinedMessages = [...olderMessagesOldestFirst, ...currentDm.messages];
          const uniqueMessages = Array.from(new Map(combinedMessages.map(msg => [msg.id, msg])).values())
                                        .sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
          // console.log('[LoadOlder DM] Messages AFTER prepend & sort for', conversationId, ':', JSON.parse(JSON.stringify(uniqueMessages)));

          const newOldestTs = olderMessagesOldestFirst.length > 0
                                ? olderMessagesOldestFirst[0].timestamp
                                : currentDm.oldestFetchedMessageTimestamp;

          const currentOverallNewestTimestamp = uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length -1].timestamp : currentDm.lastMessageTimestamp;

          console.log('[LoadOlder DM] About to set oldestFetchedMessageTimestamp:', newOldestTs, 'hasMoreOlderMessages:', newHasMoreFlag, 'for DM', conversationId);

          const updatedDm = {
            ...currentDm,
            messages: uniqueMessages,
            oldestFetchedMessageTimestamp: newOldestTs,
            hasMoreOlderMessages: newHasMoreFlag,
            lastMessageTimestamp: currentOverallNewestTimestamp,
            // Update last_fetched_message_timestamp if it was null before and we loaded messages
            last_fetched_message_timestamp: currentDm.last_fetched_message_timestamp || (uniqueMessages.length > 0 ? uniqueMessages[uniqueMessages.length - 1].timestamp : currentDm.last_fetched_message_timestamp),
          };

          // If last_fetched_message_timestamp was updated from null, store in localStorage
          if (!currentDm.last_fetched_message_timestamp && uniqueMessages.length > 0) {
            const newTimestamp = uniqueMessages[uniqueMessages.length - 1].timestamp;
            localStorage.setItem(`last_fetched_ts_${conversationId}`, newTimestamp);
          }

          const updatedDirectMessages = [
            ...prevWs.directMessages.slice(0, dmIndex),
            updatedDm,
            ...prevWs.directMessages.slice(dmIndex + 1),
          ];

          return {
            ...prevWs,
            directMessages: updatedDirectMessages,
          };
        }
      });
    } catch (error) {
      console.error(`Error loading older messages for ${conversationType} ${conversationId}:`, error);
    }
  };

  const value: AppContextType = {
    workspace, currentSection, currentChannel, currentDirectMessage, currentThread,
    setCurrentSection, setCurrentChannel, setCurrentDirectMessage, setActiveThread,
    sendMessage, getCurrentUser, toggleSidebar, isSidebarOpen, addReaction,
    addChannel, addSection, addDirectMessage, switchWorkspace, updateChannel,
    conversationReadMarkers, markConversationRead, effectiveMarkAsReadDelaySeconds,
    updateUserSetting, workspaceSettings: workspace.settings, updateWorkspaceSettings,
    updateUserStatus, searchHistory, searchResults, performSearch, addSearchToHistory,
    clearSearchHistory, clearSearchResults, navigationHistory, currentHistoryIndex,
    canGoBack, canGoForward, navigateBack, navigateForward, navigateTo, recentConversations,
    isSearchViewActive, setIsSearchViewActive, showMemberProfile, navigateToChannelTopic,
    currentChannelActiveView, setCurrentChannelActiveView, clearActiveChannelTopicForChannel,
    autoResetNewMessages, setAutoResetNewMessages,
    persistentUnreadInfo, setPersistentUnreadInfo,
    loadOlderMessages,
    fetchNewerMessagesForConversation,
  };

  // When returning to the tab, show the app with a loading overlay
  // if (loadingAfterReturn) {
  //   return (
  //     <>
  //       <AppContext.Provider value={value}>
  //         {children}
  //         <MemberProfileDialog member={selectedMember} open={memberProfileDialogOpen} onOpenChange={setMemberProfileDialogOpen} />
  //       </AppContext.Provider>
  //
  //       {/* Overlay with a subtle spinner */}
  //       <AppLoadingScreen variant="minimal" />
  //     </>
  //   );
  // }

  // Normal render
  return (
    <AppContext.Provider value={value}>
      {children}
      <MemberProfileDialog member={selectedMember} open={memberProfileDialogOpen} onOpenChange={setMemberProfileDialogOpen} />
    </AppContext.Provider>
  );
}

export const useApp = () => {
  const context = useContext(AppContext);
  if (context === undefined) throw new Error('useApp must be used within an AppProvider');
  return context;
};

// deduplicateDirectMessages is now imported from app-context-utils.ts
