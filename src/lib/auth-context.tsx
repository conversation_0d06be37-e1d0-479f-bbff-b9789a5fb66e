import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { Session, User as AuthUser } from '@supabase/supabase-js';
import { supabase } from './supabaseClient';
import { User as AppUserProfile } from './types'; // Assuming your app-specific user profile type

interface AuthContextType {
  session: Session | null;
  user: AuthUser | null;
  profile: AppUserProfile | null;
  loading: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<AuthUser | null>(null);
  const [profile, setProfile] = useState<AppUserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Store last fetch timestamp per user ID
  const lastProfileFetchTimestampRef = useRef<Record<string, number>>({});
  const PROFILE_REFETCH_INTERVAL = 15 * 60 * 1000; // 15 minutes

  useEffect(() => {
    const fetchInitialSession = async () => {
      try {
        const { data: { session: currentSession }, error } = await supabase.auth.getSession();
        if (error) {
          console.error('Error fetching session:', error);
        }
        setSession(currentSession);
        setUser(currentSession?.user ?? null);
      } catch (e) {
        console.error('Exception fetching initial session:', e);
        setSession(null);
        setUser(null);
      } finally {
        setLoading(false); // Ensure loading is false after initial attempt
      }
    };

    fetchInitialSession();

    const { data: { subscription: authListenerSubscription } } = supabase.auth.onAuthStateChange(async (_event, newSession) => {
      setSession(newSession);
      const newUser = newSession?.user ?? null;
      // If user changes, we might want to clear the old profile immediately
      // or let the profile fetch logic handle it.
      // For now, just setting user. The profile fetch effect will run.
      setUser(newUser);
    });

    return () => {
      authListenerSubscription?.unsubscribe();
    };
  }, []);

  useEffect(() => {
    const fetchProfile = async () => {
      if (user && user.id) {
        const now = Date.now();
        const userId = user.id;
        const lastFetchTimeForUser = lastProfileFetchTimestampRef.current[userId];

        // Fetch if:
        // 1. Profile is not currently loaded for this user (profile is null or profile.id doesn't match user.id)
        // 2. Or, it has been more than PROFILE_REFETCH_INTERVAL since the last successful fetch for this user.
        // 3. Or, there's no last fetch time recorded for this user yet.
        if (!profile || profile.id !== userId || !lastFetchTimeForUser || (now - lastFetchTimeForUser > PROFILE_REFETCH_INTERVAL)) {
          setLoading(true); // Indicate loading for profile fetch
          try {
            const { data, error } = await supabase
              .from('profiles')
              .select('*')
              .eq('id', userId)
              .single();

            if (error) {
              console.warn(`Error fetching profile for user ${userId} or profile not found:`, error.message);
              // If the current profile is for this user, set it to null due to error.
              // Or if no profile was set, ensure it's null.
              if (!profile || profile.id === userId) {
                  setProfile(null);
              }
              // Do not update timestamp on error, so it retries sooner.
            } else if (data) {
              setProfile(data as AppUserProfile);
              lastProfileFetchTimestampRef.current[userId] = now; // Update timestamp on success
            } else {
              // No data and no error, means profile doesn't exist for this user
              setProfile(null);
              lastProfileFetchTimestampRef.current[userId] = now; // Record that we attempted and found nothing
            }
          } catch (e: any) {
            console.error(`Exception fetching profile for user ${userId}:`, e.message);
            if (!profile || profile.id === userId) {
              setProfile(null);
            }
          } finally {
            setLoading(false);
          }
        } else {
          // Profile fetch skipped, ensure loading is false if it was somehow true
          if (loading) setLoading(false);
        }
      } else {
        setProfile(null); // No user, so no profile
        if (loading) setLoading(false); // Ensure loading is false if user becomes null
      }
    };

    fetchProfile();
  }, [user, profile?.id]); // Add profile.id to dependencies to re-evaluate if profile changes externally

  const signOut = async () => {
    setLoading(true);
    await supabase.auth.signOut();
    // State updates (session, user, profile to null) will be handled by onAuthStateChange listener
    setLoading(false);
  };

  const value = {
    session,
    user,
    profile,
    loading,
    signOut,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
