export const APP_NAME = "MyApp";
import { DirectMessage, Message, Section, Thread, Channel, User, Workspace, ChannelTopic, File, WorkspaceDisplayUser, UserClassification, WorkspaceUserRole } from './types';

function getLatestMessageTimestamp(messages: Message[], defaultTimestamp?: string): string | undefined {
  if (!messages || messages.length === 0) {
    return defaultTimestamp;
  }
  const latestMessage = messages.reduce((latest, msg) =>
    new Date(msg.timestamp) > new Date(latest.timestamp) ? msg : latest
  );
  if (defaultTimestamp && new Date(latestMessage.timestamp) < new Date(defaultTimestamp)) {
    return defaultTimestamp;
  }
  return latestMessage.timestamp;
}

// Common users across workspaces
const users: User[] = [
  {
    id: 'u8fbc62ae', // <PERSON>
    name: '<PERSON>',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>&background=random',
    status: 'online',
    title: 'Full Stack Developer',
    classification: 'standard',
  },
  {
    id: 'u1d9e0f7a', // <PERSON>
    name: '<PERSON>',
    avatar: 'https://ui-avatars.com/api/?name=<PERSON>+Doe&background=random',
    status: 'online',
    title: 'Product Manager',
    classification: 'standard',
    settings: {
      markAsReadDelaySecondsOverride: 3,
      showMessageCountOverride: false,
      messageCountTypeOverride: null,
      showUnreadBadgeOverride: null,
      themeOverride: 'default',
      channelViewOverrides: {
        'c7a0b3c4d': ['Messages', 'Files', 'Note'] // For 'general' channel in Acme Inc.
      },
      showStatusIndicatorInMessagesOverride: false
    }
  },
  {
    id: 'u5c2b8d4e', // Alex Johnson
    name: 'Alex Johnson',
    avatar: 'https://ui-avatars.com/api/?name=Alex+Johnson&background=random',
    status: 'away',
    title: 'UX Designer',
    classification: 'standard',
    settings: {
      showMessageCountOverride: true,
      messageCountTypeOverride: 'today',
      showUnreadBadgeOverride: true,
      markAsReadDelaySecondsOverride: 0
    }
  },
  {
    id: 'ue0f1c2d3', // Sam Wilson
    name: 'Sam Wilson',
    avatar: 'https://ui-avatars.com/api/?name=Sam+Wilson&background=random',
    status: 'offline',
    title: 'Backend Developer',
    classification: 'standard'
  }
];

const acmeWorkspaceUsers: WorkspaceDisplayUser[] = [
  { ...(users.find(u => u.id === 'u8fbc62ae')!), classification: users.find(u => u.id === 'u8fbc62ae')?.classification || 'standard', workspaceRole: 'admin' },
  { ...(users.find(u => u.id === 'u1d9e0f7a')!), classification: users.find(u => u.id === 'u1d9e0f7a')?.classification || 'standard', workspaceRole: 'member' },
  { ...(users.find(u => u.id === 'u5c2b8d4e')!), classification: users.find(u => u.id === 'u5c2b8d4e')?.classification || 'standard', workspaceRole: 'member' },
  { ...(users.find(u => u.id === 'ue0f1c2d3')!), classification: users.find(u => u.id === 'ue0f1c2d3')?.classification || 'standard', workspaceRole: 'member' }
];

const personalWorkspaceUsers: WorkspaceDisplayUser[] = [
  { ...(users.find(u => u.id === 'u8fbc62ae')!), classification: users.find(u => u.id === 'u8fbc62ae')?.classification || 'standard', workspaceRole: 'admin' },
];

const designTeamWorkspaceUsers: WorkspaceDisplayUser[] = [
  { ...(users.find(u => u.id === 'u8fbc62ae')!), classification: users.find(u => u.id === 'u8fbc62ae')?.classification || 'standard', workspaceRole: 'member' },
  { ...(users.find(u => u.id === 'u5c2b8d4e')!), classification: users.find(u => u.id === 'u5c2b8d4e')?.classification || 'standard', workspaceRole: 'admin' }
];

// Mock ChannelTopics for each channel
const mockChannelTopics: ChannelTopic[] = [
  {
    id: 'tc1d0e9f8', // Project Kickoff Discussion
    title: 'Project Kickoff Discussion',
    summary: 'Initial planning, task assignments, and timeline for Q3 project. Setting up the groundwork and defining scope.',
    messageIds: ['m1a2b3c4d', 'm5e6f7g8h', 'm9i0j1k2l', 'm3m4n5o6p', 'm7q8r9s0t', 'mb1c2d3e4', 'mf5g6h7i8'], // Added 2 new messages
    createdAt: new Date(Date.now() - ******** * 2).toISOString()
  },
  {
    id: 'tb2a1f0e9', // Budget Approval Update & Next Steps
    title: 'Budget Approval Update & Next Steps',
    summary: 'Updates on the budget approval process, implications, and immediate next steps for resource allocation and vendor contracts.',
    messageIds: ['mu1v2w3x4', 'my5z6a7b8', 'mc9d0e1f2', 'mg3h4i5j6'],
    createdAt: new Date(Date.now() - ********).toISOString()
  },
  {
    id: 'ta3b2e1f0', // Team Availability & Sprint Capacity
    title: 'Team Availability & Sprint Capacity',
    summary: 'Discussion about team member availability, PTO, training schedules, and overall capacity for the upcoming sprint planning.',
    messageIds: ['mk7l8m9n0', 'mo1p2q3r4', 'ms5t6u7v8', 'mw9x0y1z2', 'ma3b4c5d6', 'me7f8g9h0', 'mj9k0l1m2'], // Added 1 new message
    createdAt: new Date().toISOString()
  },
  {
    id: 'tf4e3d2c1', // New Feature Requests & Prioritization
    title: 'New Feature Requests & Prioritization',
    summary: 'Collecting customer feedback, discussing new feature ideas from sales and support, and prioritizing them for the product roadmap based on impact and effort.',
    messageIds: ['mi1j2k3l4', 'mm5n6o7p8', 'mq9r0s1t2', 'mu3v4w5x6', 'mz7a8b9c0', 'md1e2f3g4'], // Added 2 new messages
    createdAt: new Date().toISOString()
  },
  {
    id: 'te5f4c3b2', // UI/UX Improvements & Feedback
    title: 'UI/UX Improvements & Feedback',
    summary: 'Discussion on improving the user interface, user experience, incorporating recent usability test feedback, and A/B test results.',
    messageIds: ['my7z8a9b0', 'mc1d2e3f4', 'mg5h6i7j8', 'mk9l0m1n2'],
    createdAt: new Date(Date.now() - ******** * 3).toISOString()
  },
  // Topics for DMs
  {
    id: 'td6c5b4a3', // Urgent Project X Discussion
    title: 'Urgent Project X Discussion',
    summary: 'Critical updates and decisions for Project X that require immediate attention and cross-functional alignment.',
    messageIds: ['mp1q0r9s8', 'mt2u1v0w9', 'mx3y2z1a0'], // Updated from previous
    createdAt: new Date(Date.now() - 3600000 * 10).toISOString()
  },
  {
    id: 'tc7d6a5b4', // Quick Sync on Design Mockups
    title: 'Quick Sync on Design Mockups',
    summary: 'Feedback and quick alignment on the latest design mockups for the new interface, focusing on key user flows.',
    messageIds: ['mb4c3d2e1', 'mf5g4h3i2', 'mj6k5l4m3'], // Updated from previous
    createdAt: new Date(Date.now() - ******** / 3).toISOString()
  },
  // Topics for Personal Workspace (Retro Modding Corner)
  {
    id: 'tb8a7f6e5', // SNES RGB Mod Components & Techniques
    title: 'SNES RGB Mod Components & Techniques',
    summary: 'Discussion about specific components like amps (THS7374 vs others), capacitors, and wiring techniques for SNES RGB mods.',
    messageIds: ['mp1q2r3s4', 'mt5u6v7w8', 'mx9y0z1a2', 'mh5i6j7k8'], // Added 1 new message
    createdAt: new Date(Date.now() - ******** * 2).toISOString()
  },
  {
    id: 'ta9b8e7f6', // Game Boy Color IPS Screen Kits & Shell Mods
    title: 'Game Boy Color IPS Screen Kits & Shell Mods',
    summary: 'Comparing different IPS screen kits (FunnyPlaying, Cloud Game Store, etc.) for the Game Boy Color, installation tips, and compatible shell modifications.',
    messageIds: ['mb3c4d5e6', 'mf7g8h9i0', 'mj1k2l3m4', 'mn5o6p7q8'], // Added 1 new message
    createdAt: new Date(Date.now() - ******** * 1).toISOString()
  }
];

// Mock files for each channel
const mockFiles: File[] = [
  {
    id: 'f7a8b9c0d',
    name: 'Q3_ProjectPlan_v2.1.pdf',
    type: 'application/pdf',
    url: '#',
    size: 2550000,
    uploadedBy: 'u8fbc62ae',
    timestamp: new Date(Date.now() - ******** * 3).toISOString(),
    isPinned: true
  },
  {
    id: 'f1e2f3a4b',
    name: 'team-photo-q2-2025.jpg',
    type: 'image/jpeg',
    url: '#',
    size: 1250000,
    uploadedBy: 'u1d9e0f7a',
    timestamp: new Date(Date.now() - ******** * 2).toISOString(),
    isPinned: true,
    isStarred: true
  },
  {
    id: 'f5c6d7b8a',
    name: 'budgetforecastQ3_approved.xlsx',
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    url: '#',
    size: 520000,
    uploadedBy: 'u8fbc62ae',
    timestamp: new Date(Date.now() - ********).toISOString(),
    isPinned: false
  },
  {
    id: 'f9e0f1d2c',
    name: 'clientpresentation_deck_final.pptx',
    type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    url: '#',
    size: 4600000,
    uploadedBy: 'u5c2b8d4e',
    timestamp: new Date().toISOString(),
    isPinned: false
  },
  {
    id: 'f3a4b5e6f',
    name: 'snesths7374_mod_guide_rev3.pdf',
    type: 'application/pdf',
    url: '#',
    size: 1850000,
    uploadedBy: 'u8fbc62ae',
    timestamp: new Date(Date.now() - ******** * 4).toISOString(),
    isPinned: false
  }
];

// Acme Inc workspace data
const acmeGeneralMessages: Message[] = [
  {
    id: 'm1a2b3c4d',
    content: 'Welcome to the general topic! This is where we discuss company-wide topics and kick off new initiatives. First up: Q3 Project Planning!',
    timestamp: new Date(Date.now() - ******** * 2).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8',
    reactions: [
      { emoji: '👍', count: 2, users: ['u8fbc62ae', 'u5c2b8d4e'] },
      { emoji: '🎉', count: 1, users: ['ue0f1c2d3'] }
    ]
  },
  {
    id: 'm5e6f7g8h',
    content: 'Hey everyone! Reminder: Q3 project kickoff meeting tomorrow at 10 AM. Agenda includes scope definition, initial roles, and timeline. Please come prepared with your department inputs.',
    timestamp: new Date(Date.now() - 3600000 * 23).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8',
    reactions: [
      { emoji: '👍', count: 3, users: ['u8fbc62ae', 'u5c2b8d4e', 'ue0f1c2d3'] }
    ]
  },
  {
    id: 'm9i0j1k2l',
    content: 'Thanks for the reminder Jane! Looking forward to aligning on the Q3 goals.',
    timestamp: new Date(Date.now() - 3600000 * 22).toISOString(),
    userId: 'u5c2b8d4e',
    threadId: 'th1a2b3c4d',
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8'
  },
  {
    id: 'm3m4n5o6p',
    content: "I've created the initial task board in Jira for the Q3 project. Link: example.com/jira/Q3Project. Let's start populating it with high-level epics after the meeting.",
    timestamp: new Date(Date.now() - 3600000 * 21).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8'
  },
  {
    id: 'm7q8r9s0t',
    content: "For the kickoff, can we also briefly discuss potential risks and mitigation strategies? Better to identify them early.",
    timestamp: new Date(Date.now() - 3600000 * 20).toISOString(),
    userId: 'ue0f1c2d3',
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8'
  },
  { // New message for Project Kickoff
    id: 'mb1c2d3e4',
    content: "Good point, Sam. I'll add a 15-minute slot for risk brainstorming to the agenda. Everyone, please think about potential hurdles from your perspective.",
    timestamp: new Date(Date.now() - 3600000 * 19.5).toISOString(),
    userId: 'u1d9e0f7a', // Jane responding
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8'
  },
  { // New message for Project Kickoff
    id: 'mf5g6h7i8',
    content: "Just added the first set of user stories for the 'User Authentication Module' epic in Jira. Team, feel free to review and add acceptance criteria.",
    timestamp: new Date(Date.now() - 3600000 * 18).toISOString(),
    userId: 'u8fbc62ae', // John
    channelId: 'c7a0b3c4d',
    topicId: 'tc1d0e9f8'
  },
  {
    id: 'mu1v2w3x4',
    content: 'Has anyone seen the final quarterly report numbers for Q2? Need them for the budget discussion and to finalize Q3 allocations.',
    timestamp: new Date(Date.now() - 3600000 * 10).toISOString(),
    userId: 'ue0f1c2d3',
    channelId: 'c7a0b3c4d',
    topicId: 'tb2a1f0e9'
  },
  {
    id: 'my5z6a7b8',
    content: 'I just sent them over via email, Sam. Also attached the Q3 budget forecast spreadsheet. Key highlights: marketing spend up 5%, R&D flat.',
    timestamp: new Date(Date.now() - 3600000 * 9).toISOString(),
    userId: 'u5c2b8d4e',
    threadId: 'th5e6f7g8h',
    channelId: 'c7a0b3c4d',
    topicId: 'tb2a1f0e9',
    files: [mockFiles.find(f => f.id === 'f5c6d7b8a')!]
  },
  {
    id: 'mc9d0e1f2',
    content: "Great news, everyone! The Q3 budget has been officially approved by finance. We can proceed with the planned new hires and software upgrades. Details in the attached doc.",
    timestamp: new Date(Date.now() - 3600000 * 8).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c7a0b3c4d',
    topicId: 'tb2a1f0e9',
    reactions: [{ emoji: '🥳', count: 4, users: ['u8fbc62ae', 'u1d9e0f7a', 'u5c2b8d4e', 'ue0f1c2d3']}],
    files: [mockFiles.find(f => f.id === 'f7a8b9c0d')!]
  },
  {
    id: 'mg3h4i5j6',
    content: "Excellent! With the budget approved, I'll start the paperwork for the new Senior Dev role. HR needs the final job description by tomorrow.",
    timestamp: new Date(Date.now() - 3600000 * 7).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7a0b3c4d',
    topicId: 'tb2a1f0e9'
  },
  {
    id: 'mz1y2x3w4',
    content: 'Quick update on the office coffee machine - it is fixed and brewing again! Thanks to facilities.',
    timestamp: new Date(Date.now() - 3600000 * 0.5).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7a0b3c4d'
  }
];

const acmeRandomMessages: Message[] = [
  {
    id: 'mk7l8m9n0',
    content: 'Just discovered this cool new design tool called "PixelPerfect Pro" that might help our workflow for the next sprint. It has AI-assisted layout features. Anyone heard of it?',
    timestamp: new Date(Date.now() - ********).toISOString(),
    userId: 'u5c2b8d4e',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'mo1p2q3r4',
    content: 'Oh nice, Alex! Is it collaborative? We need something for real-time feedback during sprint reviews, especially with remote team members.',
    timestamp: new Date(Date.now() - ******** + 1800000).toISOString(),
    userId: 'u8fbc62ae',
    threadId: 'th9i0j1k2l',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'ms5t6u7v8',
    content: "Speaking of the next sprint, I'll be on PTO next Monday and Tuesday for a quick getaway. Just a heads-up for capacity planning.",
    timestamp: new Date(Date.now() - ******** + 3600000).toISOString(),
    userId: 'u5c2b8d4e',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'mw9x0y1z2',
    content: "Thanks for the heads-up, Alex. Sam, are you free to cover his critical tasks if anything urgent comes up? We need to ensure sprint continuity and support for the junior devs.",
    timestamp: new Date(Date.now() - ******** + 3700000).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'ma3b4c5d6',
    content: "Yep, I can manage that. My schedule is relatively clear for those two days. I'll sync with Alex before he leaves on his current tasks.",
    timestamp: new Date(Date.now() - ******** + 3800000).toISOString(),
    userId: 'ue0f1c2d3',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'me7f8g9h0',
    content: "Also, a reminder that Friday is a company-wide training day on the new security protocols. Sprint planning will need to account for that lost day.",
    timestamp: new Date(Date.now() - ******** + 4000000).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  { // New message for Team Availability
    id: 'mj9k0l1m2',
    content: "Re: PixelPerfect Pro demo - I've scheduled it for next Wednesday at 2 PM. Invite is out. Hopefully, it's a good fit!",
    timestamp: new Date(Date.now() - ******** + 4200000).toISOString(), // Follow up on demo
    userId: 'u8fbc62ae', // John, who asked about it
    channelId: 'c5e6f7g8h',
    topicId: 'ta3b2e1f0'
  },
  {
    id: 'mi1j2k3l4',
    content: 'Has anyone started working on the new landing page design? We got some interesting feature requests from sales, including an interactive demo section.',
    timestamp: new Date(Date.now() - ********).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  },
  {
    id: 'mm5n6o7p8',
    content: 'I can help with that! Just let me know when you want to discuss requirements for these new features. I have some capacity this week to start wireframing.',
    timestamp: new Date(Date.now() - 21600000).toISOString(),
    userId: 'ue0f1c2d3',
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  },
  {
    id: 'mq9r0s1t2',
    content: "One key feature request is a dynamic pricing calculator based on user input (company size, features needed). It's a bit complex, so we'll need to scope it carefully with engineering.",
    timestamp: new Date(Date.now() - 21000000).toISOString(),
    userId: 'u1d9e0f7a',
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  },
  {
    id: 'mu3v4w5x6',
    content: "Agreed on the calculator. Alex, could you mock up a simple UI for that based on the sales brief? We can then discuss technical feasibility.",
    timestamp: new Date(Date.now() - 20000000).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  },
  { // New message for Feature Requests
    id: 'mz7a8b9c0',
    content: "Sure, John. I'll work on a low-fi mockup for the pricing calculator today. Should have something to share by EOD.",
    timestamp: new Date(Date.now() - 19000000).toISOString(),
    userId: 'u5c2b8d4e', // Alex responding
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  },
  { // New message for Feature Requests
    id: 'md1e2f3g4',
    content: "For the calculator, what are the primary data inputs we need to consider? Number of users, storage tier, specific add-on modules? Need this for the backend logic.",
    timestamp: new Date(Date.now() - 18500000).toISOString(),
    userId: 'ue0f1c2d3', // Sam, the backend dev
    channelId: 'c5e6f7g8h',
    topicId: 'tf4e3d2c1'
  }
];

const acmeThreads: Record<string, Thread> = {
  'th1a2b3c4d': {
    id: 'th1a2b3c4d',
    parentMessageId: 'm5e6f7g8h',
    messages: [
      {
        id: 'm9i0j1k2l',
        content: 'Thanks for the reminder Jane! Looking forward to aligning on the Q3 goals.',
        timestamp: new Date(Date.now() - 3600000 * 22).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c7a0b3c4d',
        topicId: 'tc1d0e9f8'
      },
      {
        id: 'r1a2b3c4d',
        content: 'No problem! Make sure to bring your project updates and any blockers. The more prepared we are, the smoother the kickoff.',
        timestamp: new Date(Date.now() - 3600000 * 21.8).toISOString(),
        userId: 'u1d9e0f7a',
        channelId: 'c7a0b3c4d',
        topicId: 'tc1d0e9f8'
      },
      {
        id: 'r5e6f7g8h',
        content: 'Will do. I have some exciting progress on the new API to share, which should de-risk one of our major Q3 deliverables!',
        timestamp: new Date(Date.now() - 3600000 * 21.5).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c7a0b3c4d',
        topicId: 'tc1d0e9f8'
      }
    ]
  },
  'th5e6f7g8h': {
    id: 'th5e6f7g8h',
    parentMessageId: 'mu1v2w3x4',
    messages: [
      {
        id: 'my5z6a7b8',
        content: 'I just sent them over via email, Sam. Also attached the Q3 budget forecast spreadsheet. Key highlights: marketing spend up 5%, R&D flat.',
        timestamp: new Date(Date.now() - 3600000 * 9).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c7a0b3c4d',
        topicId: 'tb2a1f0e9',
        files: [mockFiles.find(f => f.id === 'f5c6d7b8a')!]
      },
      {
        id: 'r9i0j1k2l',
        content: 'Got it, thanks Alex! I\'ll take a look right away and get back to you if anything is unclear for the budget meeting. Specifically interested in the R&D breakdown.',
        timestamp: new Date(Date.now() - 3600000 * 8.8).toISOString(),
        userId: 'ue0f1c2d3',
        channelId: 'c7a0b3c4d',
        topicId: 'tb2a1f0e9'
      }
    ]
  },
  'th9i0j1k2l': {
    id: 'th9i0j1k2l',
    parentMessageId: 'mk7l8m9n0',
    messages: [
      {
        id: 'mo1p2q3r4',
        content: 'Oh nice, Alex! Is it collaborative? We need something for real-time feedback during sprint reviews, especially with remote team members.',
        timestamp: new Date(Date.now() - ******** + 1800000).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c5e6f7g8h',
        topicId: 'ta3b2e1f0'
      },
      {
        id: 'r3m4n5o6p',
        content: 'Yes, "PixelPerfect Pro" is fully collaborative. Really intuitive and great for quick mockups and iterations! It also integrates with Jira for task tracking.',
        timestamp: new Date(Date.now() - ******** + 2100000).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c5e6f7g8h',
        topicId: 'ta3b2e1f0'
      },
      {
        id: 'r7q8r9s0t',
        content: 'Sounds promising. Maybe we could schedule a quick demo for the team next week? Could be a good addition to our toolkit if it streamlines reviews.',
        timestamp: new Date(Date.now() - ******** + 2400000).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c5e6f7g8h',
        topicId: 'ta3b2e1f0'
      }
    ]
  }
};

const acmeChannels: Channel[] = [
  {
    id: 'c7a0b3c4d', // general
    name: 'general',
    description: 'Company-wide announcements and work-based matters',
    isPrivate: false,
    members: ['u8fbc62ae', 'u1d9e0f7a', 'u5c2b8d4e', 'ue0f1c2d3'],
    messages: acmeGeneralMessages,
    threads: {
      'th1a2b3c4d': acmeThreads['th1a2b3c4d'],
      'th5e6f7g8h': acmeThreads['th5e6f7g8h'],
    },
    createdAt: new Date(Date.now() - ******** * 30).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'tc1d0e9f8')!), messageIds: acmeGeneralMessages.filter(m => m.topicId === 'tc1d0e9f8').map(m => m.id) },
      { ...(mockChannelTopics.find(t => t.id === 'tb2a1f0e9')!), messageIds: acmeGeneralMessages.filter(m => m.topicId === 'tb2a1f0e9').map(m => m.id) },
    ],
    activeChannelTopicId: 'tc1d0e9f8',
    files: mockFiles.filter(f => ['f7a8b9c0d', 'f1e2f3a4b', 'f5c6d7b8a', 'f9e0f1d2c'].includes(f.id)),
    pinnedFiles: ['f7a8b9c0d', 'f1e2f3a4b'],
    channelNote: `# General Channel Note\n\nKey Information and Action Items. Q3 Planning ongoing. Budget approved.`,
    unreadCount: 3,
    lastMessageTimestamp: getLatestMessageTimestamp(acmeGeneralMessages, new Date(Date.now() - ******** * 30).toISOString()),
    channelSpecificDefaultViews: ['Messages', 'Topics', 'Files', 'Members', 'Note']
  },
  {
    id: 'c5e6f7g8h', // random
    name: 'random',
    description: 'Non-work banter and water cooler conversation',
    isPrivate: false,
    members: ['u8fbc62ae', 'u1d9e0f7a', 'u5c2b8d4e', 'ue0f1c2d3'],
    messages: acmeRandomMessages,
    threads: {
      'th9i0j1k2l': acmeThreads['th9i0j1k2l'],
    },
    createdAt: new Date(Date.now() - ******** * 25).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'ta3b2e1f0')!), messageIds: acmeRandomMessages.filter(m => m.topicId === 'ta3b2e1f0').map(m => m.id) },
      { ...(mockChannelTopics.find(t => t.id === 'tf4e3d2c1')!), messageIds: acmeRandomMessages.filter(m => m.topicId === 'tf4e3d2c1').map(m => m.id) }
    ],
    activeChannelTopicId: 'ta3b2e1f0',
    files: [],
    channelNote: `# Random Channel Note\n\nFun links, casual chat, and team building ideas.`,
    unreadCount: 0,
    lastMessageTimestamp: getLatestMessageTimestamp(acmeRandomMessages, new Date(Date.now() - ******** * 25).toISOString()),
    channelSpecificDefaultViews: ['Messages', 'Files']
  },
  {
    id: 'c9i0j1k2l', // dev-team
    name: 'dev-team',
    description: 'Development team discussions, PR reviews, and technical deep dives',
    isPrivate: true,
    members: ['u8fbc62ae', 'ue0f1c2d3'],
    messages: [
      {
        id: 'my7z8a9b0',
        content: 'Starting work on the API performance optimization task (JIRA-123). Focusing on the user authentication endpoint first. Profiling shows it as a bottleneck.',
        timestamp: new Date(Date.now() - ******** * 1.5).toISOString(),
        userId: 'ue0f1c2d3',
        channelId: 'c9i0j1k2l',
        topicId: 'te5f4c3b2'
      },
      {
        id: 'mc1d2e3f4',
        content: 'Can someone review my PR for the database schema changes? #PR-45. It includes new indices for faster queries on the `orders` table. Test coverage is at 95%.',
        timestamp: new Date(Date.now() - 3600000 * 8).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c9i0j1k2l',
        topicId: 'te5f4c3b2'
      },
      {
        id: 'mg5h6i7j8',
        content: 'The staging server deployment is complete. All new API endpoints (v1.2) are live for testing. Please report any issues found during QA.',
        timestamp: new Date(Date.now() - 3600000 * 2).toISOString(),
        userId: 'ue0f1c2d3',
        channelId: 'c9i0j1k2l',
        topicId: 'te5f4c3b2'
      },
      {
        id: 'mk9l0m1n2',
        content: 'Just pushed a hotfix for the null pointer exception on the `/users/:id/profile` endpoint. Staging is updated. Root cause was missing data validation.',
        timestamp: new Date(Date.now() - 3600000 * 1).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c9i0j1k2l',
        topicId: 'te5f4c3b2'
      }
    ],
    threads: {},
    createdAt: new Date(Date.now() - ******** * 15).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'te5f4c3b2')!), title: 'Technical Improvements & Deployments', summary: 'Discussions on API optimization, database changes, deployments, and hotfixes.', messageIds: ['my7z8a9b0', 'mc1d2e3f4', 'mg5h6i7j8', 'mk9l0m1n2'], createdAt: new Date(Date.now() - ******** * 1.5).toISOString() }
    ],
    files: [],
    channelNote: `# Development Team Notes\n\nSprint goals, tech stack, PR queue, and deployment schedule.`,
    unreadCount: 3,
    lastMessageTimestamp: getLatestMessageTimestamp([
      { id: 'my7z8a9b0', content: '...', timestamp: new Date(Date.now() - ******** * 1.5).toISOString(), userId: 'ue0f1c2d3', channelId: 'c9i0j1k2l', topicId: 'te5f4c3b2' },
      { id: 'mc1d2e3f4', content: '...', timestamp: new Date(Date.now() - 3600000 * 8).toISOString(), userId: 'u8fbc62ae', channelId: 'c9i0j1k2l', topicId: 'te5f4c3b2' },
      { id: 'mg5h6i7j8', content: '...', timestamp: new Date(Date.now() - 3600000 * 2).toISOString(), userId: 'ue0f1c2d3', channelId: 'c9i0j1k2l', topicId: 'te5f4c3b2' },
      { id: 'mk9l0m1n2', content: '...', timestamp: new Date(Date.now() - 3600000 * 1).toISOString(), userId: 'u8fbc62ae', channelId: 'c9i0j1k2l', topicId: 'te5f4c3b2' }
    ], new Date(Date.now() - ******** * 15).toISOString()),
  },
  {
    id: 'c3m4n5o6p', // design-ux
    name: 'design-ux',
    description: 'Design discussions, UX feedback, and creative brainstorming',
    isPrivate: false,
    members: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: [
      {
        id: 'mo3p4q5r6',
        content: 'Sharing the latest mockups for the homepage redesign (Figma link in thread). Feedback on the new hero section and CTA placement is welcome!',
        timestamp: new Date(Date.now() - ******** * 0.5).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c3m4n5o6p',
        topicId: 'te5f4c3b2'
      },
      {
        id: 'ms7t8u9v0',
        content: 'We need to finalize the color palette for the new branding guide by EOW. I lean towards the cooler tones (blues/greens) for a more modern feel. Thoughts?',
        timestamp: new Date(Date.now() - 3600000 * 6).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c3m4n5o6p',
        topicId: 'te5f4c3b2'
      },
      {
        id: 'mw1x2y3z4',
        content: 'Any new feature requests from the latest user feedback session that impact UI? Specifically for the dashboard customization options.',
        timestamp: new Date(Date.now() - 3600000 * 3).toISOString(),
        userId: 'u5c2b8d4e',
        channelId: 'c3m4n5o6p',
        topicId: 'tf4e3d2c1'
      },
      {
        id: 'ma5b6c7d8',
        content: 'Regarding the homepage hero, I think the imagery is strong, but the headline could be punchier. Let\'s brainstorm some alternatives.',
        timestamp: new Date(Date.now() - ******** * 0.4).toISOString(),
        userId: 'u8fbc62ae',
        channelId: 'c3m4n5o6p',
        topicId: 'te5f4c3b2'
      }
    ],
    threads: {},
    createdAt: new Date(Date.now() - ******** * 10).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'tf4e3d2c1')!), messageIds: ['mw1x2y3z4'] },
      { ...(mockChannelTopics.find(t => t.id === 'te5f4c3b2')!), messageIds: ['mo3p4q5r6', 'ms7t8u9v0', 'ma5b6c7d8'] }
    ],
    files: [mockFiles.find(f => f.id === 'f9e0f1d2c')!],
    channelNote: `# Design Team Notes\n\nMoodboards, style guides, user personas, and accessibility checklist.`,
    unreadCount: 1,
    lastMessageTimestamp: getLatestMessageTimestamp([
      { id: 'mo3p4q5r6', content: '...', timestamp: new Date(Date.now() - ******** * 0.5).toISOString(), userId: 'u5c2b8d4e', channelId: 'c3m4n5o6p', topicId: 'te5f4c3b2' },
      { id: 'ms7t8u9v0', content: '...', timestamp: new Date(Date.now() - 3600000 * 6).toISOString(), userId: 'u8fbc62ae', channelId: 'c3m4n5o6p', topicId: 'te5f4c3b2' },
      { id: 'mw1x2y3z4', content: '...', timestamp: new Date(Date.now() - 3600000 * 3).toISOString(), userId: 'u5c2b8d4e', channelId: 'c3m4n5o6p', topicId: 'tf4e3d2c1' },
      { id: 'ma5b6c7d8', content: '...', timestamp: new Date(Date.now() - ******** * 0.4).toISOString(), userId: 'u8fbc62ae', channelId: 'c3m4n5o6p', topicId: 'te5f4c3b2' }
    ], new Date(Date.now() - ******** * 10).toISOString())
  }
];

const acmeSections: Section[] = [
  {
    id: 's7q8r9s0t',
    name: 'Team Workspace',
    channels: [acmeChannels.find(c => c.id === 'c7a0b3c4d')!, acmeChannels.find(c => c.id === 'c5e6f7g8h')!]
  },
  {
    id: 'su1v2w3x4',
    name: 'Project Alpha (Dev)',
    channels: [acmeChannels.find(c => c.id === 'c9i0j1k2l')!]
  },
  {
    id: 'sy5z6a7b8',
    name: 'Project Beta (Design)',
    channels: [acmeChannels.find(c => c.id === 'c3m4n5o6p')!]
  }
];

const acmeDm1Messages: Message[] = [
  {
    id: 'mp1q0r9s8',
    content: 'Hey John, do you have a minute to chat about Project X? There are some urgent decisions needed on the API spec regarding data privacy.',
    timestamp: new Date(Date.now() - 3600000 * 8).toISOString(),
    userId: 'u1d9e0f7a',
    topicId: 'td6c5b4a3'
  },
  {
    id: 'mt2u1v0w9',
    content: 'Sure Jane, what\'s up? I have about 15 minutes before my next call. Is it about the GDPR compliance concerns?',
    timestamp: new Date(Date.now() - 3600000 * 7.9).toISOString(),
    userId: 'u8fbc62ae'
  },
  {
    id: 'mx3y2z1a0',
    content: 'Exactly. I was thinking we should change the approach to the user authentication flow based on the security team\'s feedback. It might impact the timeline by a few days, but it\'s critical.',
    timestamp: new Date(Date.now() - 3600000 * 7.8).toISOString(),
    userId: 'u1d9e0f7a',
    topicId: 'td6c5b4a3'
  }
];

const acmeDm2Messages: Message[] = [
  {
    id: 'mb4c3d2e1',
    content: 'Hi John! I just sent over the design mockups for the new dashboard (v3) for review. Focused on the data visualization components this time.',
    timestamp: new Date(Date.now() - ******** / 2).toISOString(),
    userId: 'u5c2b8d4e',
    topicId: 'tc7d6a5b4'
  },
  {
    id: 'mf5g4h3i2',
    content: 'Thanks Alex! I\'ll take a look at them shortly and provide feedback by EOD. Particularly interested in how the drill-down functionality is handled.',
    timestamp: new Date(Date.now() - ******** / 2 + 300000).toISOString(),
    userId: 'u8fbc62ae',
    topicId: 'tc7d6a5b4'
  },
  {
    id: 'mj6k5l4m3',
    content: 'Also, can we discuss the new logo concepts tomorrow? I have a few variations based on the "minimalist evolution" brief I\'d like to run by you.',
    timestamp: new Date(Date.now() - ******** / 2 + 600000).toISOString(),
    userId: 'u5c2b8d4e'
  }
];

const acmeDirectMessages: DirectMessage[] = [
  {
    id: 'dmc9d8e7f6',
    name: 'Project X Sync (Jane)',
    participants: ['u8fbc62ae', 'u1d9e0f7a'],
    messages: acmeDm1Messages,
    threads: {},
    topics: [
      {
        ...(mockChannelTopics.find(t => t.id === 'td6c5b4a3')!),
        messageIds: acmeDm1Messages.filter(m => m.topicId === 'td6c5b4a3').map(m => m.id)
      }
    ],
    activeDmTopicId: 'td6c5b4a3',
    createdAt: new Date(Date.now() - ******** * 2).toISOString(),
    unreadCount: 2,
    lastMessageTimestamp: getLatestMessageTimestamp(acmeDm1Messages, new Date(Date.now() - ******** * 2).toISOString())
  },
  {
    id: 'dmb0a9f8e7',
    participants: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: acmeDm2Messages,
    threads: {},
    topics: [
      {
        ...(mockChannelTopics.find(t => t.id === 'tc7d6a5b4')!),
        messageIds: acmeDm2Messages.filter(m => m.topicId === 'tc7d6a5b4').map(m => m.id)
      }
    ],
    unreadCount: 0,
    lastMessageTimestamp: getLatestMessageTimestamp(acmeDm2Messages, acmeDm2Messages.length > 0 ? acmeDm2Messages[0].timestamp : undefined)
  }
];

// Personal Workspace: "My Retro Modding Corner"
const retroSnesModMessages: Message[] = [
  {
    id: 'mp1q2r3s4',
    content: "Just got my THS7374 amp chips for the SNES 1CHIP. Anyone tried this specific amp for an RGB mod? Heard it's cleaner than the BA6592F.",
    timestamp: new Date(Date.now() - ******** * 2).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7q8r9s0t',
    topicId: 'tb8a7f6e5'
  },
  {
    id: 'mt5u6v7w8',
    content: "Yeah, the THS7374 is great! Much cleaner and sharper image. Make sure you get good quality ceramic capacitors (C1815 or similar) to pair with it for minimal noise.",
    timestamp: new Date(Date.now() - ******** * 1.9).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7q8r9s0t',
    topicId: 'tb8a7f6e5'
  },
  {
    id: 'mx9y0z1a2',
    content: "Quick tip: Remember to lift pin 9 on the S-RGB/S-ENC chip if you're bypassing the console's onboard amp entirely. Prevents interference and potential damage.",
    timestamp: new Date(Date.now() - ******** * 1.8).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7q8r9s0t',
    topicId: 'tb8a7f6e5'
  },
  { // New message for SNES RGB Mods
    id: 'mh5i6j7k8',
    content: "I'm running into an issue with my SNES Jr RGB mod. Getting faint vertical lines on darker colors. Using THS7374, lifted pin 9. Any ideas? Could it be my SCART cable?",
    timestamp: new Date(Date.now() - ******** * 1.7).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'c7q8r9s0t',
    topicId: 'tb8a7f6e5'
  }
];

const retroHandheldScreenMessages: Message[] = [
  {
    id: 'mb3c4d5e6',
    content: "Looking for a good IPS kit for a Game Boy Color. Any recommendations? Want something with good brightness, integer scaling, and relatively easy install.",
    timestamp: new Date(Date.now() - ******** * 1).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'cu1v2w3x4',
    topicId: 'ta9b8e7f6'
  },
  {
    id: 'mf7g8h9i0',
    content: "The FunnyPlaying Q5 IPS (v2 or later) is pretty popular and relatively easy to install. Shell modification is usually needed though, unless you buy a pre-cut shell.",
    timestamp: new Date(Date.now() - ******** * 0.9).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'cu1v2w3x4',
    topicId: 'ta9b8e7f6'
  },
  {
    id: 'mj1k2l3m4',
    content: "Has anyone tried the Cloud Game Store IPS kits for GBC? They claim no-cut installs on some models. Wondering about the quality vs FunnyPlaying.",
    timestamp: new Date(Date.now() - ******** * 0.8).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'cu1v2w3x4',
    topicId: 'ta9b8e7f6'
  },
  { // New message for GBC IPS Kits
    id: 'mn5o6p7q8',
    content: "Just finished installing the FunnyPlaying Q5 IPS in my GBC! The difference is night and day. Shell trimming was a bit fiddly but worth it. Colors are amazing!",
    timestamp: new Date(Date.now() - ******** * 0.7).toISOString(),
    userId: 'u8fbc62ae',
    channelId: 'cu1v2w3x4',
    topicId: 'ta9b8e7f6'
  }
];

const personalChannels: Channel[] = [
  {
    id: 'c7q8r9s0t',
    name: 'snes-rgb-mods',
    description: 'Discussing RGB mods for SNES and Super Famicom consoles. Component sourcing, install tips, and troubleshooting.',
    isPrivate: false,
    members: ['u8fbc62ae'],
    messages: retroSnesModMessages,
    threads: {},
    createdAt: new Date(Date.now() - ******** * 20).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'tb8a7f6e5')!), messageIds: retroSnesModMessages.filter(m => m.topicId === 'tb8a7f6e5').map(m => m.id) }
    ],
    files: [mockFiles.find(f => f.id === 'f3a4b5e6f')!],
    unreadCount: 1,
    lastMessageTimestamp: getLatestMessageTimestamp(retroSnesModMessages, new Date(Date.now() - ******** * 20).toISOString())
  },
  {
    id: 'cu1v2w3x4',
    name: 'handheld-screen-upgrades',
    description: 'Swapping out old LCDs for modern IPS screens in handhelds like Game Boy, GBA, PSP. Kit comparisons and install guides.',
    isPrivate: false,
    members: ['u8fbc62ae'],
    messages: retroHandheldScreenMessages,
    threads: {},
    createdAt: new Date(Date.now() - ******** * 18).toISOString(),
    channelTopics: [
      { ...(mockChannelTopics.find(t => t.id === 'ta9b8e7f6')!), messageIds: retroHandheldScreenMessages.filter(m => m.topicId === 'ta9b8e7f6').map(m => m.id) }
    ],
    unreadCount: 0,
    lastMessageTimestamp: getLatestMessageTimestamp(retroHandheldScreenMessages, new Date(Date.now() - ******** * 18).toISOString())
  }
];

const personalSections: Section[] = [
  {
    id: 'sy5z6a7b8',
    name: 'Console Modding Projects',
    channels: [personalChannels.find(c => c.id === 'c7q8r9s0t')!, personalChannels.find(c => c.id === 'cu1v2w3x4')!]
  }
];

// Design Team workspace mock data
const designGeneralMessages: Message[] = [
  {
    id: 'mn2o1p0q9',
    content: 'Welcome to the Design Team workspace! Let\'s make awesome things and push creative boundaries.',
    timestamp: new Date(Date.now() - ******** * 3).toISOString(),
    userId: 'u5c2b8d4e'
  },
  {
    id: 'mr8s7t6u5',
    content: 'I\'ve uploaded the latest design assets (v2.1) to our shared drive. Includes new icon sets and updated brand guidelines.',
    timestamp: new Date(Date.now() - ******** * 2).toISOString(),
    userId: 'u5c2b8d4e'
  },
  {
    id: 'mv4w3x2y1',
    content: 'Looking good, Alex! I have some feedback on the color scheme for the mobile app mockups – specifically the contrast ratios for accessibility.',
    timestamp: new Date(Date.now() - ******** * 1).toISOString(),
    userId: 'u8fbc62ae'
  }
];

const designChannels: Channel[] = [
  {
    id: 'cz0a1b2c3',
    name: 'announcements-design',
    description: 'Design team updates, news, and important announcements.',
    isPrivate: false,
    members: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: designGeneralMessages,
    threads: {},
    createdAt: new Date(Date.now() - ******** * 15).toISOString(),
    unreadCount: 2,
    lastMessageTimestamp: getLatestMessageTimestamp(designGeneralMessages, new Date(Date.now() - ******** * 15).toISOString())
  },
  {
    id: 'cd4e5f6g7',
    name: 'inspiration-board',
    description: 'Design inspiration, references, cool websites, and moodboards.',
    isPrivate: false,
    members: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: [
        { id: 'mh8i9j0k1', content: 'Check out this amazing portfolio site: designhero.com - love their use of typography!', userId: 'u5c2b8d4e', timestamp: new Date(Date.now() - ******** * 5).toISOString(), channelId: 'cd4e5f6g7'}
    ],
    threads: {},
    createdAt: new Date(Date.now() - ******** * 12).toISOString(),
    unreadCount: 0,
    lastMessageTimestamp: getLatestMessageTimestamp([], new Date(Date.now() - ******** * 12).toISOString())
  },
  {
    id: 'cl2m3n4o5',
    name: 'ui-component-lib',
    description: 'Discussions and updates related to our shared UI component library (Storybook).',
    isPrivate: false,
    members: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: [
        { id: 'mp6q7r8s9', content: 'Planning to refactor the Button component next week to add more variants and improve accessibility. Any objections or specific requests?', userId: 'u8fbc62ae', timestamp: new Date(Date.now() - ******** * 3).toISOString(), channelId: 'cl2m3n4o5'}
    ],
    threads: {},
    createdAt: new Date(Date.now() - ******** * 10).toISOString(),
    unreadCount: 0,
    lastMessageTimestamp: getLatestMessageTimestamp([], new Date(Date.now() - ******** * 10).toISOString())
  }
];

const designSections: Section[] = [
  {
    id: 'st0u1v2w3',
    name: 'Design System & Process',
    channels: [designChannels.find(c=>c.id==='cz0a1b2c3')!, designChannels.find(c=>c.id==='cd4e5f6g7')!, designChannels.find(c=>c.id==='cl2m3n4o5')!]
  }
];

const designDm1Messages: Message[] = [
  {
    id: 'mx4y5z6a7',
    content: 'Hey John, what do you think about the new wireframes for the settings page? Sent them via Figma link. Focused on simplifying the information architecture.',
    timestamp: new Date(Date.now() - 3600000 * 5).toISOString(),
    userId: 'u5c2b8d4e'
  },
  {
    id: 'mb8c9d0e1',
    content: 'They look great, Alex! I especially like the navigation flow and how you grouped the settings. Much more intuitive than the current version.',
    timestamp: new Date(Date.now() - 3600000 * 4.8).toISOString(),
    userId: 'u8fbc62ae'
  }
];

const designDirectMessages: DirectMessage[] = [
  {
    id: 'dmf2g3h4i5',
    participants: ['u8fbc62ae', 'u5c2b8d4e'],
    messages: designDm1Messages,
    threads: {},
    unreadCount: 1,
    lastMessageTimestamp: getLatestMessageTimestamp(designDm1Messages, designDm1Messages.length > 0 ? designDm1Messages[0].timestamp : undefined)
  }
];

// Collection of complete workspaces
export const mockWorkspaces: Record<string, Workspace> = {
  'w1a2b3c4d': {
    id: 'w1a2b3c4d',
    name: 'Acme Inc.',
    users: acmeWorkspaceUsers,
    sections: acmeSections,
    directMessages: acmeDirectMessages,
    currentUserId: 'u8fbc62ae',
    currentSectionId: 's7q8r9s0t',
    currentChannelId: 'c7a0b3c4d',
    currentDirectMessageId: null,
    activeThreadId: null,
    settings: {
      markAsReadDelaySecondsDefault: 10,
      theme: 'royal-purple',
      allowGuestInvites: true,
      showMessageCountDefault: true,
      messageCountTypeDefault: 'total' as 'total' | 'today',
      showUnreadBadgeDefault: true,
      defaultChannelViews: ['Messages', 'Topics', 'Files', 'Members', 'Note'],
      showStatusIndicatorInMessagesDefault: true,
      suppressRealtimeConnectionToast: true // Default to true
    }
  },
  'w5e6f7g8h': {
    id: 'w5e6f7g8h',
    name: 'My Retro Modding Corner',
    users: personalWorkspaceUsers,
    sections: personalSections,
    directMessages: [],
    currentUserId: 'u8fbc62ae',
    currentSectionId: 'sy5z6a7b8',
    currentChannelId: 'c7q8r9s0t',
    currentDirectMessageId: null,
    activeThreadId: null,
    settings: {
      markAsReadDelaySecondsDefault: 5,
      showMessageCountDefault: true,
      messageCountTypeDefault: 'total' as 'total' | 'today',
      allowGuestInvites: false,
      theme: 'light',
      showUnreadBadgeDefault: true,
      defaultChannelViews: ['Messages', 'Topics', 'Files'],
      showStatusIndicatorInMessagesDefault: true,
      suppressRealtimeConnectionToast: true // Default to true
    }
  },
  'w9i0j1k2l': {
    id: 'w9i0j1k2l',
    name: 'Design Team Hub',
    users: designTeamWorkspaceUsers,
    sections: designSections,
    directMessages: designDirectMessages,
    currentUserId: 'u5c2b8d4e',
    currentSectionId: 'st0u1v2w3',
    currentChannelId: 'cz0a1b2c3',
    currentDirectMessageId: null,
    activeThreadId: null,
    settings: {
      markAsReadDelaySecondsDefault: 8,
      showMessageCountDefault: false,
      messageCountTypeDefault: 'today' as 'total' | 'today',
      allowGuestInvites: true,
      theme: 'system',
      showUnreadBadgeDefault: true,
      defaultChannelViews: ['Messages', 'Topics', 'Files', 'Members', 'Note'],
      showStatusIndicatorInMessagesDefault: true,
      suppressRealtimeConnectionToast: true // Default to true
    }
  }
};

export const mockWorkspace: Workspace = mockWorkspaces['w1a2b3c4d'];

export function findUserById(userId: string): WorkspaceDisplayUser | undefined {
  for (const wsId in mockWorkspaces) {
    const user = mockWorkspaces[wsId].users.find(u => u.id === userId);
    if (user) return user;
  }
  const globalUser = users.find(u => u.id === userId);
  if (globalUser) {
      return { ...globalUser, classification: globalUser.classification || 'standard', workspaceRole: 'member' };
  }
  return undefined;
}

export function formatTimestamp(timestamp: string): string {
  const date = new Date(timestamp);
  const now = new Date();
  const diffMs = now.getTime() - date.getTime();
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

  if (diffDays === 0) {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  } else if (diffDays === 1) {
    return `Yesterday at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else if (diffDays < 7) {
    return `${date.toLocaleDateString([], { weekday: 'long' })} at ${date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}`;
  } else {
    return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
  }
}
