import { supabase } from './supabaseClient';
import { User as AppUserProfile, Workspace, WorkspaceDisplayUser, Section, Channel, Message, DirectMessage, UserClassification } from './types'; // Import necessary types
import { UserConversationReadState } from './types';
import { generateDisplayId } from './utils'; // Import generateDisplayId

/**
 * Fetches a user profile from the 'profiles' table.
 * @param userId The ID of the user whose profile is to be fetched.
 * @returns A promise that resolves to the user profile or null if not found.
 */
export const getUserProfile = async (userId: string): Promise<AppUserProfile | null> => {
  try {
    const { data, error, status } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    // Handle Supabase client error (e.g., RLS, policy violation, but not network error which would throw)
    // Note: .single() can return an error if no rows are found (status 406) or more than one row.
    // We only want to treat actual errors (not status 406 "no row") as failure here.
    if (error && status !== 406) {
      console.error(`Supabase error in getUserProfile for user ${userId} (specific error):`, error.message);
      return null; // Return null directly as per requirement
    }

    if (data) {
      return data as AppUserProfile;
    }

    return null;
  } catch (networkOrUnexpectedError: any) { // Catch network errors or other unexpected issues
    console.error(`Network or unexpected error in getUserProfile for user ${userId}:`, networkOrUnexpectedError.message ? networkOrUnexpectedError.message : networkOrUnexpectedError);
    return null;
  }
};

/**
 * Fetches initial workspace data for a user.
 * @param userId The ID of the user.
 * @returns A promise that resolves to the user's primary workspace data or null.
 */
export const getInitialWorkspaceDataForUser = async (userId: string): Promise<Workspace | null> => {
  console.log(`Attempting to fetch initial workspace data for user: ${userId}`);

  try {
    // 1. Fetch Workspace Memberships First
    const { data: memberships, error: membershipError } = await supabase
      .from('workspace_users')
      .select('workspace_id, role')
      .eq('user_id', userId);

    if (membershipError) {
      console.error(`Supabase error fetching memberships for user ${userId}:`, membershipError.message);
      throw membershipError;
    }
    if (!memberships || memberships.length === 0) {
      console.log(`User ${userId} is not a member of any workspaces.`);
      return null;
    }

    // 2. Extract Workspace IDs
    const workspaceIds = memberships.map(mem => mem.workspace_id);

    // 3. Fetch Workspace Details Separately
    const { data: workspaceDetailsList, error: workspaceError } = await supabase
      .from('workspaces')
      .select('*')
      .in('id', workspaceIds);

    if (workspaceError) {
      console.error(`Supabase error fetching workspace details for IDs [${workspaceIds.join(', ')}]:`, workspaceError.message);
      throw workspaceError;
    }
    if (!workspaceDetailsList || workspaceDetailsList.length === 0) {
      console.log(`No workspace details found for workspace IDs [${workspaceIds.join(', ')}] for user ${userId}. This might indicate an issue if memberships existed.`);
      return null;
    }

    // Determine the primary workspace to load. Using the first membership's workspace_id,
    // similar to the original logic of taking the first result.
    const primaryMembership = memberships[0];
    const primaryWorkspaceData = workspaceDetailsList.find(w => w.id === primaryMembership.workspace_id) as any; // 'any' to match original, though specific type is better

    if (!primaryWorkspaceData || typeof primaryWorkspaceData !== 'object' || primaryWorkspaceData === null) {
        console.error("Primary workspace data (details) could not be found for the user's first membership or is not an object.");
        return null;
    }
    const currentWorkspaceId = primaryWorkspaceData.id;
    // Note: primaryMembership.role contains the user's role in this specific primaryWorkspace.
    // This information is implicitly handled later when fetching all users for the currentWorkspaceId,
    // as each user's role in that workspace is fetched.

    // 3. Fetch all users (profiles) for this workspace
    const { data: wsUsersWithProfiles, error: wspError } = await supabase
      .from('workspace_users')
      .select('role, profiles!inner(*)')
      .eq('workspace_id', currentWorkspaceId);

    if (wspError) throw wspError;
    
    // Initialize a Map to store all unique users encountered (workspace members + DM participants)
    const allKnownUsersMap = new Map<string, WorkspaceDisplayUser>();

    wsUsersWithProfiles?.forEach(wu => {
      const profileData = wu.profiles as any; 
      if (!profileData || typeof profileData !== 'object') {
        console.warn(`Profile data missing for a workspace user in workspace ${currentWorkspaceId}`);
        return; 
      }
      const baseProfile = profileData as AppUserProfile & { avatar_url?: string, full_name?: string };
      const displayUser: WorkspaceDisplayUser = {
        id: baseProfile.id,
        displayId: generateDisplayId(baseProfile.id, 'u-'),
        name: baseProfile.name || baseProfile.full_name || 'Unknown User',
        avatar: baseProfile.avatar_url || baseProfile.avatar || '',
        status: baseProfile.status,
        title: baseProfile.title,
        classification: baseProfile.classification,
        about: baseProfile.about,
        settings: baseProfile.settings,
        workspaceRole: wu.role as 'admin' | 'member',
      };
      if (!allKnownUsersMap.has(displayUser.id)) {
        allKnownUsersMap.set(displayUser.id, displayUser);
      }
    });

    // 4. Fetch sections for this workspace, and their channels with members
    const { data: sectionsData, error: sectionsError } = await supabase
      .from('sections')
      .select('*, channels!inner(*, channel_members!inner(user_id))')
      .eq('workspace_id', currentWorkspaceId)
      .order('display_order', { ascending: true })
      .order('name', { foreignTable: 'channels', ascending: true });

    if (sectionsError) throw sectionsError;

    const formattedSections: Section[] = sectionsData?.map((s: any) => ({
      id: s.id,
      displayId: generateDisplayId(s.id, 's-'),
      name: s.name,
      channels: s.channels?.map((c: any) => ({
        id: c.id,
        displayId: generateDisplayId(c.id, 'c-'),
        name: c.name,
        description: c.description,
        isPrivate: c.is_private,
        createdAt: c.created_at,
        channelNote: c.channel_note,
        settings: c.settings,
        lastMessageTimestamp: c.last_message_timestamp,
        activeChannelTopicId: c.active_channel_topic_id,
        members: c.channel_members?.map((cm: any) => cm.user_id) || [],
        messages: [], 
        threads: {},   
        channelTopics: [], 
        files: [], 
      })) || [],
    })) || [];
    
    // 5. Fetch Direct Message Sessions for the user
    let directMessages: DirectMessage[] = [];
    const { data: userDmLinks, error: userDmLinksError } = await supabase
      .from('direct_message_participants')
      .select('dm_id')
      .eq('user_id', userId);

    if (userDmLinksError) throw userDmLinksError;

    if (userDmLinks && userDmLinks.length > 0) {
      const dmIds = userDmLinks.map(link => link.dm_id);

      const { data: dmSessionsRaw, error: dmError } = await supabase
        .from('direct_message_sessions')
        .select(`
          id,
          name,
          created_at,
          last_message_timestamp,
          direct_message_participants ( 
            user_id,
            profiles!inner (id, name, avatar_url, status, title, classification, about, settings)
          )
        `)
        .in('id', dmIds);

      if (dmError) throw dmError;

      if (dmSessionsRaw) {
        directMessages = dmSessionsRaw.map((session: any) => {
          const dmParticipantProfiles: WorkspaceDisplayUser[] = session.direct_message_participants
            .map((p: any) => {
              const profile = p.profiles;
              if (!profile) return null;
              const displayUser: WorkspaceDisplayUser = { // Construct as WorkspaceDisplayUser for consistency
                id: profile.id,
                displayId: generateDisplayId(profile.id, 'u-'),
                name: profile.name || 'Unknown User',
                avatar: profile.avatar_url || '',
                status: profile.status,
                title: profile.title,
                classification: profile.classification as UserClassification | undefined,
                about: profile.about,
                settings: profile.settings,
                // workspaceRole might not be relevant for a DM participant if they aren't in the current workspace
                // but WorkspaceDisplayUser requires it. We can set a default or fetch it if necessary.
                // For now, let's assume they might be in the workspace or use a default.
                workspaceRole: allKnownUsersMap.get(profile.id)?.workspaceRole || 'member', // Get role if known, else default
              };
              // Add/update this participant in our global list of users
              if (!allKnownUsersMap.has(displayUser.id)) {
                allKnownUsersMap.set(displayUser.id, displayUser);
              } else { // If user already known (e.g. from workspace members), merge/update if necessary
                const existingUser = allKnownUsersMap.get(displayUser.id)!;
                allKnownUsersMap.set(displayUser.id, { ...existingUser, ...displayUser }); // Prioritize new data if any
              }
              return displayUser;
            })
            .filter(Boolean) as WorkspaceDisplayUser[];

          let dmDisplayName = session.name; 
          if (!dmDisplayName && dmParticipantProfiles.length === 2) {
            const otherUser = dmParticipantProfiles.find(p => p.id !== userId);
            dmDisplayName = otherUser ? otherUser.name : 'DM';
          } else if (!dmDisplayName && dmParticipantProfiles.length === 1) { 
            dmDisplayName = dmParticipantProfiles[0].name;
          }

          return {
            id: session.id,
            displayId: generateDisplayId(session.id, 'dm-'),
            name: dmDisplayName, 
            participants: dmParticipantProfiles.map(p => p.id), 
            messages: [], 
            threads: {}, 
            createdAt: session.created_at,
            lastMessageTimestamp: session.last_message_timestamp,
            unreadCount: 0, 
          };
        }).filter(Boolean) as DirectMessage[];
      }
    }

    // console.log('[supabase-data-provider] Fetched and formatted directMessages:', directMessages); // Cleaned up
    
    const finalWorkspaceUsers = Array.from(allKnownUsersMap.values());
    // console.log('[supabase-data-provider] Consolidated workspace.users:', finalWorkspaceUsers.map(u=>({id: u.id, name: u.name}))); // Cleaned up


    const constructedWorkspace: Workspace = {
      id: currentWorkspaceId,
      displayId: generateDisplayId(currentWorkspaceId, 'w-'),
      name: primaryWorkspaceData.name,
      icon: primaryWorkspaceData.icon_url, 
      owner_id: primaryWorkspaceData.owner_id, 
      settings: primaryWorkspaceData.settings,
      createdAt: primaryWorkspaceData.created_at, 
      users: finalWorkspaceUsers, // Use the consolidated list
      sections: formattedSections,
      directMessages: directMessages, 
      currentUserId: userId,
      currentSectionId: formattedSections.length > 0 ? formattedSections[0].id : null,
      currentChannelId: formattedSections.length > 0 && formattedSections[0].channels.length > 0 ? formattedSections[0].channels[0].id : null,
      currentDirectMessageId: null,
      activeThreadId: null,
    };

    return constructedWorkspace;

  } catch (error: any) {
    console.error('Error in getInitialWorkspaceDataForUser:', error.message, error.details, error.hint);
    return null;
  }
};

/**
 * Fetches messages for a given channel ID.
 * @param channelId The ID of the channel.
 * @returns A promise that resolves to an array of messages or null on error.
 */
export const getMessagesForChannel = async (channelId: string, limit: number = 50, olderThanTimestamp?: string | null, sinceTimestamp?: string | null): Promise<Message[] | null> => {
  if (!channelId) return []; // Or perhaps null, consistent with error returns
  try {
    let query = supabase
      .from('messages')
      .select(`
        id,
        content,
        timestamp,
        user_id,
        parent_message_id,
        topic_id,
        edited,
        edited_at,
        also_send_to_channel,
        profiles ( id, name, avatar_url, title, status )
      `)
      .eq('channel_id', channelId)
      .is('parent_message_id', null);

    if (sinceTimestamp) {
      query = query.gt('timestamp', sinceTimestamp).order('timestamp', { ascending: true });
    } else if (olderThanTimestamp) {
      query = query.lt('timestamp', olderThanTimestamp).order('timestamp', { ascending: false });
    } else {
      // Initial fetch
      query = query.order('timestamp', { ascending: false });
    }

    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching messages for channel:', error);
      return null;
    }
    
    return data?.map((msg: any) => ({
      id: msg.id,
      displayId: generateDisplayId(msg.id, 'm-'),
      content: msg.content,
      timestamp: msg.timestamp,
      userId: msg.user_id,
      parentMessageId: msg.parent_message_id,
      topicId: msg.topic_id,
      edited: msg.edited,
      editedAt: msg.edited_at,
      alsoSendToChannel: msg.also_send_to_channel,
    })) || []; // Keep [] for no data, null for error

  } catch (err: any) {
    console.error(`Exception in getMessagesForChannel for ${channelId}:`, err.message);
    return null;
  }
};

/**
 * Fetches messages for a given Direct Message ID.
 * @param dmId The ID of the Direct Message session.
 * @returns A promise that resolves to an array of messages or null on error.
 */
export const getMessagesForDirectMessage = async (dmId: string, limit: number = 50, olderThanTimestamp?: string | null, sinceTimestamp?: string | null): Promise<Message[] | null> => {
  if (!dmId) return []; // Or perhaps null
  try {
    let query = supabase
      .from('messages')
      .select(`
        id,
        content,
        timestamp,
        user_id,
        parent_message_id,
        topic_id,
        edited,
        edited_at,
        also_send_to_channel,
        profiles ( id, name, avatar_url, title, status )
      `)
      .eq('dm_id', dmId)
      .is('parent_message_id', null);

    if (sinceTimestamp) {
      query = query.gt('timestamp', sinceTimestamp).order('timestamp', { ascending: true });
    } else if (olderThanTimestamp) {
      query = query.lt('timestamp', olderThanTimestamp).order('timestamp', { ascending: false });
    } else {
      // Initial fetch
      query = query.order('timestamp', { ascending: false });
    }
    
    query = query.limit(limit);
    
    const { data, error } = await query;

    if (error) {
      console.error('Error fetching messages for DM:', error);
      return null;
    }
    
    return data?.map((msg: any) => ({
      id: msg.id,
      displayId: generateDisplayId(msg.id, 'm-'),
      content: msg.content,
      timestamp: msg.timestamp,
      userId: msg.user_id,
      parentMessageId: msg.parent_message_id,
      topicId: msg.topic_id,
      edited: msg.edited,
      editedAt: msg.edited_at,
      alsoSendToChannel: msg.also_send_to_channel,
      // Note: msg.profiles will be available with richer info if needed.
    })) || []; // Keep [] for no data, null for error

  } catch (err: any) {
    console.error(`Exception in getMessagesForDirectMessage for ${dmId}:`, err.message);
    return null;
  }
};

/**
 * Fetches all user conversation read states for a given user.
 * @param userId The ID of the user.
 * @returns A promise that resolves to an array of UserConversationReadState objects or null if an error occurs.
 */
export async function getUserConversationReadStates(userId: string): Promise<UserConversationReadState[] | null> {
  try {
    const { data, error } = await supabase
      .from('user_conversation_read_states')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching user conversation read states:', error);
      return null;
    }

    return data as UserConversationReadState[];
  } catch (err: any) {
    console.error('Exception in getUserConversationReadStates:', err.message);
    return null;
  }
}
