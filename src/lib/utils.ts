import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * Implements the 32-bit FNV-1a hash algorithm.
 * @param str The input string.
 * @returns An unsigned 32-bit integer hash.
 */
function fnv1a32(str: string): number {
  let hash = 0x811c9dc5; // FNV offset basis
  for (let i = 0; i < str.length; i++) {
    hash ^= str.charCodeAt(i);
    // Equivalent to hash *= 0x01000193 (FNV prime) with 32-bit overflow
    hash += (hash << 1) + (hash << 4) + (hash << 7) + (hash << 8) + (hash << 24);
  }
  return hash >>> 0; // Ensure unsigned 32-bit
}

/**
 * Generates a shorter, display-friendly ID from a UUID using FNV-1a hash.
 * @param uuid The input UUID string.
 * @param prefix A prefix for the display ID (e.g., "u-", "msg-"). Defaults to empty.
 * @param length The desired length of the hex hash part. Defaults to 8. Max is 8 for 32-bit hash.
 * @returns A string combining the prefix and the hex representation of the hash.
 */
export function generateDisplayId(uuid: string, prefix: string = '', length: number = 8): string {
  if (!uuid) return `${prefix}invalid-uuid`.substring(0, length + prefix.length);

  // Using the full UUID string for hashing to ensure different UUIDs produce different hashes
  // even if their initial segments were similar (though unlikely for v4 UUIDs).
  const hashInt = fnv1a32(uuid);
  
  // Convert the 32-bit integer hash to a hexadecimal string.
  // A 32-bit number can be represented by 8 hexadecimal characters.
  let hexHash = hashInt.toString(16).padStart(8, '0');
  
  // Truncate if the desired length is less than 8.
  if (length < 8 && length > 0) {
    hexHash = hexHash.substring(0, length);
  } else if (length > 8) {
    // If a length greater than 8 is requested for a 32-bit hash's hex,
    // it's not directly possible without more complex encoding or a larger hash.
    // For now, we'll cap at 8 or use a different strategy if needed.
    // This implementation will return max 8 hex chars from a 32-bit hash.
    hexHash = hexHash.substring(0, 8); 
  } else if (length <= 0) {
    hexHash = ''; // No hash part if length is 0 or less
  }
  
  return `${prefix}${hexHash}`;
}
