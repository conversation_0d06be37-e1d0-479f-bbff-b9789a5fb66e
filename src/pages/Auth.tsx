import React, { useState, useEffect, useCallback, useRef } from 'react'; // Added useRef
import { supabase } from '../lib/supabaseClient';
import { useNavigate, useLocation } from 'react-router-dom';

const AuthPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const lastAutoLoginEmailRef = useRef<string | null>(null); // Ref to track last auto-login attempt

  // Determine credentials, prioritizing URL in DEV, then .env
  const queryParams = new URLSearchParams(location.search);
  const urlEmail = import.meta.env.DEV ? queryParams.get('test_email') : null;
  const urlPassword = import.meta.env.DEV ? queryParams.get('test_password') : null;
  
  const envEmail = import.meta.env.VITE_TEST_USER_EMAIL || '';
  const envPassword = import.meta.env.VITE_TEST_USER_PASSWORD || '';

  const initialEffectiveEmail = urlEmail || envEmail;
  const initialEffectivePassword = urlPassword || envPassword;
  
  const [email, setEmail] = useState(initialEffectiveEmail);
  const [password, setPassword] = useState(initialEffectivePassword);
  const [isSignUp, setIsSignUp] = useState(!(initialEffectiveEmail && initialEffectivePassword)); 
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  
  // Effect to update form fields and mode if URL params or .env vars change dynamically
  useEffect(() => {
    const currentEffectiveEmail = (import.meta.env.DEV ? queryParams.get('test_email') : null) || envEmail;
    const currentEffectivePassword = (import.meta.env.DEV ? queryParams.get('test_password') : null) || envPassword;

    setEmail(currentEffectiveEmail);
    setPassword(currentEffectivePassword);
    // Only change mode if the effective presence of credentials changes
    if (!!currentEffectiveEmail !== !!initialEffectiveEmail || !!currentEffectivePassword !== !!initialEffectivePassword) {
        setIsSignUp(!(currentEffectiveEmail && currentEffectivePassword));
    }
    // If effective email changes, reset the auto-login attempt ref for the new email
    if (currentEffectiveEmail !== lastAutoLoginEmailRef.current) {
        lastAutoLoginEmailRef.current = null; 
    }

  }, [location.search, envEmail, envPassword, initialEffectiveEmail, initialEffectivePassword]);

  const performAuth = useCallback(async (authActionEmail: string, authActionPassword: string) => {
    setLoading(true);
    setError(null);

    try {
      if (isSignUp) {
        const { error: signUpError } = await supabase.auth.signUp({
          email: authActionEmail,
          password: authActionPassword,
        });
        if (signUpError) throw signUpError;
        alert('Sign up successful! Please check your email to confirm your account.');
        setIsSignUp(false);
      } else {
        const { data, error: signInError } = await supabase.auth.signInWithPassword({
          email: authActionEmail,
          password: authActionPassword,
        });
        if (signInError) throw signInError;
        if (data.user) {
          navigate('/');
        }
      }
    } catch (err: any) {
      setError(err.error_description || err.message);
    } finally {
      setLoading(false);
    }
  }, [navigate, isSignUp]); // isSignUp is a dependency

  // Effect for auto-login
  useEffect(() => {
    const currentEffectiveEmail = (import.meta.env.DEV ? queryParams.get('test_email') : null) || envEmail;
    const currentEffectivePassword = (import.meta.env.DEV ? queryParams.get('test_password') : null) || envPassword;

    const autoLoginAttemptPossible = currentEffectiveEmail && currentEffectivePassword && !isSignUp;

    if (autoLoginAttemptPossible && !loading) {
      // Check if form fields match current effective credentials AND auto-login hasn't been attempted for this email yet
      if (email === currentEffectiveEmail && password === currentEffectivePassword && lastAutoLoginEmailRef.current !== currentEffectiveEmail) {
        const source = (urlEmail && urlEmail === currentEffectiveEmail) ? 'URL' : '.env';
        console.log(`Attempting auto-login with ${source} credentials (Email: ${currentEffectiveEmail}, Password: ${currentEffectivePassword})...`); // Log password
        lastAutoLoginEmailRef.current = currentEffectiveEmail; // Mark as attempted for this email
        performAuth(currentEffectiveEmail, currentEffectivePassword);
      }
    }
  }, [envEmail, envPassword, location.search, isSignUp, loading, performAuth, email, password, urlEmail]);


  const handleFormSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    performAuth(email, password);
  };

  return (
    <div style={{ maxWidth: '400px', margin: '50px auto', padding: '20px', border: '1px solid #ccc', borderRadius: '8px' }}>
      <h2>{isSignUp ? 'Sign Up' : 'Log In'}</h2>
      <form onSubmit={handleFormSubmit}>
        <div>
          <label htmlFor="email">Email:</label>
          <input
            type="email"
            id="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            required
            style={{ width: '100%', padding: '8px', marginBottom: '10px', boxSizing: 'border-box' }}
          />
        </div>
        <div>
          <label htmlFor="password">Password:</label>
          <input
            type="password"
            id="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            required
            style={{ width: '100%', padding: '8px', marginBottom: '20px', boxSizing: 'border-box' }}
          />
        </div>
        {error && <p style={{ color: 'red' }}>{error}</p>}
        <button type="submit" disabled={loading} style={{ width: '100%', padding: '10px', backgroundColor: '#007bff', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}>
          {loading ? 'Processing...' : (isSignUp ? 'Sign Up' : 'Log In')}
        </button>
      </form>
      <button
        onClick={() => {
          setIsSignUp(!isSignUp);
          setError(null); 
        }}
        style={{ marginTop: '10px', background: 'none', border: 'none', color: '#007bff', cursor: 'pointer', textDecoration: 'underline' }}
      >
        {isSignUp ? 'Already have an account? Log In' : "Don't have an account? Sign Up"}
      </button>
    </div>
  );
};

export default AuthPage;
