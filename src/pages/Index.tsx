
import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../lib/auth-context'; // Corrected path
import { AppProvider } from '@/lib/app-context';
import { AppLayout } from '@/components/AppLayout';
import { AppLoadingScreen } from '@/components/AppLoadingScreen';
import '@/components/ui/global-styles.css';

const Index: React.FC = () => {
  const { session, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !session) {
      navigate('/auth');
    }
  }, [session, loading, navigate]);

  if (loading) {
    return <AppLoadingScreen variant="skeleton" />;
  }

  if (!session) {
    // This case should ideally be handled by the redirect,
    // but as a fallback or if navigation hasn't happened yet.
    return null;
  }

  // If session exists, render the app
  return (
    <AppProvider>
      <AppLayout />
    </AppProvider>
  );
};

export default Index;
