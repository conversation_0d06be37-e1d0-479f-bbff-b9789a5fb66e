/* Keyboard navigation styles */

/* Message selection styles */
.message:focus {
  outline: 2px solid var(--app-highlight);
  outline-offset: 2px;
}

.message[aria-selected="true"] {
  background-color: var(--app-hover-bg);
  box-shadow: 0 0 0 2px var(--app-highlight);
}

/* Add focus styles for interactive elements */
button:focus-visible,
a:focus-visible,
[tabindex]:focus-visible {
  outline: 2px solid var(--app-highlight);
  outline-offset: 2px;
}

/* Special focus styles for input elements */
input:focus-visible,
textarea:focus-visible {
  outline: 1px solid var(--app-border);
  outline-offset: -1px;
  box-shadow: 0 0 0 2px rgba(var(--app-highlight-rgb), 0.25);
}

/* Keyboard shortcut indicator for tooltips */
[data-shortcut]::after {
  content: attr(data-shortcut);
  display: inline-block;
  margin-left: 0.5rem;
  padding: 0.1rem 0.3rem;
  font-size: 0.7rem;
  background-color: var(--app-hover-bg);
  border-radius: 0.25rem;
  opacity: 0.7;
}

/* Hide shortcut indicators on mobile */
@media (max-width: 768px) {
  [data-shortcut]::after {
    display: none;
  }
}

/* Keyboard shortcut styles */
kbd {
  display: inline-block;
  padding: 0.1rem 0.4rem;
  font-size: 0.75rem;
  font-weight: 600;
  line-height: 1.5;
  color: #333;
  background-color: #f7f7f7;
  border: 1px solid #ccc;
  border-radius: 3px;
  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.2);
  white-space: nowrap;
  margin: 0 0.1rem;
}

/* Dark mode for kbd */
.dark kbd {
  color: #f7f7f7;
  background-color: #333;
  border-color: #555;
  box-shadow: 0 1px 0 rgba(255, 255, 255, 0.2);
}
