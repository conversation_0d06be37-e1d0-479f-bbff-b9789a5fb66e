export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      channel_members: {
        Row: {
          channel_id: string
          user_id: string
        }
        Insert: {
          channel_id: string
          user_id: string
        }
        Update: {
          channel_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "channel_members_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channel_members_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      channel_topics: {
        Row: {
          channel_id: string
          created_at: string
          creator_id: string | null
          id: string
          summary: string | null
          title: string
        }
        Insert: {
          channel_id: string
          created_at?: string
          creator_id?: string | null
          id?: string
          summary?: string | null
          title: string
        }
        Update: {
          channel_id?: string
          created_at?: string
          creator_id?: string | null
          id?: string
          summary?: string | null
          title?: string
        }
        Relationships: [
          {
            foreignKeyName: "channel_topics_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channel_topics_creator_id_fkey"
            columns: ["creator_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      channels: {
        Row: {
          active_channel_topic_id: string | null
          channel_note: string | null
          created_at: string
          description: string | null
          id: string
          is_private: boolean
          last_message_timestamp: string | null
          name: string
          section_id: string | null
          settings: Json | null
          updated_at: string
          workspace_id: string
        }
        Insert: {
          active_channel_topic_id?: string | null
          channel_note?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean
          last_message_timestamp?: string | null
          name: string
          section_id?: string | null
          settings?: Json | null
          updated_at?: string
          workspace_id: string
        }
        Update: {
          active_channel_topic_id?: string | null
          channel_note?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_private?: boolean
          last_message_timestamp?: string | null
          name?: string
          section_id?: string | null
          settings?: Json | null
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "channels_section_id_fkey"
            columns: ["section_id"]
            isOneToOne: false
            referencedRelation: "sections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "channels_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "fk_active_topic"
            columns: ["active_channel_topic_id"]
            isOneToOne: false
            referencedRelation: "channel_topics"
            referencedColumns: ["id"]
          },
        ]
      }
      direct_message_participants: {
        Row: {
          dm_id: string
          user_id: string
        }
        Insert: {
          dm_id: string
          user_id: string
        }
        Update: {
          dm_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "direct_message_participants_dm_id_fkey"
            columns: ["dm_id"]
            isOneToOne: false
            referencedRelation: "direct_message_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "direct_message_participants_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      direct_message_sessions: {
        Row: {
          created_at: string
          id: string
          last_message_timestamp: string | null
          name: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          last_message_timestamp?: string | null
          name?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          last_message_timestamp?: string | null
          name?: string | null
        }
        Relationships: []
      }
      files: {
        Row: {
          channel_id: string | null
          created_at: string
          id: string
          is_pinned: boolean | null
          is_starred: boolean | null
          message_id: string | null
          name: string
          size_bytes: number
          type: string
          uploaded_by_user_id: string
          url: string
        }
        Insert: {
          channel_id?: string | null
          created_at?: string
          id?: string
          is_pinned?: boolean | null
          is_starred?: boolean | null
          message_id?: string | null
          name: string
          size_bytes: number
          type: string
          uploaded_by_user_id: string
          url: string
        }
        Update: {
          channel_id?: string | null
          created_at?: string
          id?: string
          is_pinned?: boolean | null
          is_starred?: boolean | null
          message_id?: string | null
          name?: string
          size_bytes?: number
          type?: string
          uploaded_by_user_id?: string
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "files_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "files_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "files_uploaded_by_user_id_fkey"
            columns: ["uploaded_by_user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          also_send_to_channel: boolean | null
          channel_id: string | null
          content: string
          dm_id: string | null
          edited: boolean | null
          edited_at: string | null
          id: string
          parent_message_id: string | null
          timestamp: string
          topic_id: string | null
          user_id: string
        }
        Insert: {
          also_send_to_channel?: boolean | null
          channel_id?: string | null
          content: string
          dm_id?: string | null
          edited?: boolean | null
          edited_at?: string | null
          id?: string
          parent_message_id?: string | null
          timestamp?: string
          topic_id?: string | null
          user_id: string
        }
        Update: {
          also_send_to_channel?: boolean | null
          channel_id?: string | null
          content?: string
          dm_id?: string | null
          edited?: boolean | null
          edited_at?: string | null
          id?: string
          parent_message_id?: string | null
          timestamp?: string
          topic_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_channel_id_fkey"
            columns: ["channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_dm_id_fkey"
            columns: ["dm_id"]
            isOneToOne: false
            referencedRelation: "direct_message_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_parent_message_id_fkey"
            columns: ["parent_message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_topic_id_fkey"
            columns: ["topic_id"]
            isOneToOne: false
            referencedRelation: "channel_topics"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          about: string | null
          avatar_url: string | null
          classification: string | null
          created_at: string
          id: string
          name: string
          settings: Json | null
          status: string | null
          status_message: string | null
          title: string | null
          updated_at: string
        }
        Insert: {
          about?: string | null
          avatar_url?: string | null
          classification?: string | null
          created_at?: string
          id: string
          name: string
          settings?: Json | null
          status?: string | null
          status_message?: string | null
          title?: string | null
          updated_at?: string
        }
        Update: {
          about?: string | null
          avatar_url?: string | null
          classification?: string | null
          created_at?: string
          id?: string
          name?: string
          settings?: Json | null
          status?: string | null
          status_message?: string | null
          title?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      reactions: {
        Row: {
          emoji: string
          id: string
          message_id: string
          user_id: string
        }
        Insert: {
          emoji: string
          id?: string
          message_id: string
          user_id: string
        }
        Update: {
          emoji?: string
          id?: string
          message_id?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reactions_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      sections: {
        Row: {
          created_at: string
          display_order: number
          id: string
          name: string
          updated_at: string
          workspace_id: string
        }
        Insert: {
          created_at?: string
          display_order?: number
          id?: string
          name: string
          updated_at?: string
          workspace_id: string
        }
        Update: {
          created_at?: string
          display_order?: number
          id?: string
          name?: string
          updated_at?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "sections_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      user_app_state: {
        Row: {
          active_thread_id: string | null
          current_channel_id: string | null
          current_dm_id: string | null
          current_section_id: string | null
          current_workspace_id: string | null
          user_id: string
        }
        Insert: {
          active_thread_id?: string | null
          current_channel_id?: string | null
          current_dm_id?: string | null
          current_section_id?: string | null
          current_workspace_id?: string | null
          user_id: string
        }
        Update: {
          active_thread_id?: string | null
          current_channel_id?: string | null
          current_dm_id?: string | null
          current_section_id?: string | null
          current_workspace_id?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "user_app_state_active_thread_id_fkey"
            columns: ["active_thread_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_app_state_current_channel_id_fkey"
            columns: ["current_channel_id"]
            isOneToOne: false
            referencedRelation: "channels"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_app_state_current_dm_id_fkey"
            columns: ["current_dm_id"]
            isOneToOne: false
            referencedRelation: "direct_message_sessions"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_app_state_current_section_id_fkey"
            columns: ["current_section_id"]
            isOneToOne: false
            referencedRelation: "sections"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_app_state_current_workspace_id_fkey"
            columns: ["current_workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_app_state_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: true
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      workspace_users: {
        Row: {
          role: string
          user_id: string
          workspace_id: string
        }
        Insert: {
          role?: string
          user_id: string
          workspace_id: string
        }
        Update: {
          role?: string
          user_id?: string
          workspace_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspace_users_user_id_fkey"
            columns: ["user_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "workspace_users_workspace_id_fkey"
            columns: ["workspace_id"]
            isOneToOne: false
            referencedRelation: "workspaces"
            referencedColumns: ["id"]
          },
        ]
      }
      workspaces: {
        Row: {
          created_at: string
          icon_url: string | null
          id: string
          name: string
          owner_id: string
          settings: Json | null
          updated_at: string
        }
        Insert: {
          created_at?: string
          icon_url?: string | null
          id?: string
          name: string
          owner_id: string
          settings?: Json | null
          updated_at?: string
        }
        Update: {
          created_at?: string
          icon_url?: string | null
          id?: string
          name?: string
          owner_id?: string
          settings?: Json | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "workspaces_owner_id_fkey"
            columns: ["owner_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      is_allowed_to_send_to_channel: {
        Args: { p_channel_id: string; p_user_id: string }
        Returns: boolean
      }
      is_allowed_to_send_to_dm: {
        Args: { p_dm_id: string; p_user_id: string }
        Returns: boolean
      }
      is_channel_member: {
        Args: { channel_id: string }
        Returns: boolean
      }
      is_dm_participant: {
        Args: { dm_id: string }
        Returns: boolean
      }
      is_workspace_admin: {
        Args: { workspace_id: string }
        Returns: boolean
      }
      is_workspace_member: {
        Args: { workspace_id: string }
        Returns: boolean
      }
      send_message: {
        Args:
          | {
              p_content: string
              p_workspace_id: string
              p_channel_id?: string
              p_dm_id?: string
              p_parent_message_id?: string
            }
          | {
              p_content: string
              p_workspace_id: string
              p_channel_id?: string
              p_dm_id?: string
              p_parent_message_id?: string
              p_topic_id?: string
            }
        Returns: {
          also_send_to_channel: boolean | null
          channel_id: string | null
          content: string
          dm_id: string | null
          edited: boolean | null
          edited_at: string | null
          id: string
          parent_message_id: string | null
          timestamp: string
          topic_id: string | null
          user_id: string
        }
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
