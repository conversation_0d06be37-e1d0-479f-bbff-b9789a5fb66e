[{"name": "<PERSON><PERSON><PERSON><PERSON>", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "determine similarities and distance between strings"}, {"name": "pgroonga_database", "schema": null, "default_version": "3.2.5", "installed_version": null, "comment": "PGroonga database management module"}, {"name": "citext", "schema": null, "default_version": "1.6", "installed_version": null, "comment": "data type for case-insensitive character strings"}, {"name": "plpgsql", "schema": "pg_catalog", "default_version": "1.0", "installed_version": "1.0", "comment": "PL/pgSQL procedural language"}, {"name": "file_fdw", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "foreign-data wrapper for flat file access"}, {"name": "uuid-ossp", "schema": "extensions", "default_version": "1.1", "installed_version": "1.1", "comment": "generate universally unique identifiers (UUIDs)"}, {"name": "pg_buffercache", "schema": null, "default_version": "1.3", "installed_version": null, "comment": "examine the shared buffer cache"}, {"name": "postgis_topology", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "PostGIS topology spatial types and functions"}, {"name": "pg_cron", "schema": null, "default_version": "1.6", "installed_version": null, "comment": "Job scheduler for PostgreSQL"}, {"name": "tablefunc", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions that manipulate whole tables, including crosstab"}, {"name": "insert_username", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions for tracking who changed a table"}, {"name": "moddatetime", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions for tracking last modification time"}, {"name": "pgrowlocks", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "show row-level locking information"}, {"name": "timescaledb", "schema": null, "default_version": "2.16.1", "installed_version": null, "comment": "Enables scalable inserts and complex queries for time-series data (Apache 2 Edition)"}, {"name": "intarray", "schema": null, "default_version": "1.5", "installed_version": null, "comment": "functions, operators, and index support for 1-D arrays of integers"}, {"name": "wrappers", "schema": null, "default_version": "0.5.0", "installed_version": null, "comment": "Foreign data wrappers developed by Supabase"}, {"name": "address_standardizer_data_us", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "Address Standardizer US dataset example"}, {"name": "ltree", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "data type for hierarchical tree-like structures"}, {"name": "pgcrypto", "schema": "extensions", "default_version": "1.3", "installed_version": "1.3", "comment": "cryptographic functions"}, {"name": "pgjwt", "schema": null, "default_version": "0.2.0", "installed_version": null, "comment": "JSON Web Token API for Postgresql"}, {"name": "pg_trgm", "schema": null, "default_version": "1.6", "installed_version": null, "comment": "text similarity measurement and index searching based on trigrams"}, {"name": "amcheck", "schema": null, "default_version": "1.3", "installed_version": null, "comment": "functions for verifying relation integrity"}, {"name": "pg_stat_statements", "schema": "extensions", "default_version": "1.10", "installed_version": "1.10", "comment": "track planning and execution statistics of all SQL statements executed"}, {"name": "rum", "schema": null, "default_version": "1.3", "installed_version": null, "comment": "RUM index access method"}, {"name": "isn", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "data types for international product numbering standards"}, {"name": "pg_prewarm", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "prewarm relation data"}, {"name": "pgtap", "schema": null, "default_version": "1.2.0", "installed_version": null, "comment": "Unit testing for PostgreSQL"}, {"name": "old_snapshot", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "utilities in support of old_snapshot_threshold"}, {"name": "pg_repack", "schema": null, "default_version": "1.5.2", "installed_version": null, "comment": "Reorganize tables in PostgreSQL databases with minimal locks"}, {"name": "hstore", "schema": null, "default_version": "1.8", "installed_version": null, "comment": "data type for storing sets of (key, value) pairs"}, {"name": "cube", "schema": null, "default_version": "1.5", "installed_version": null, "comment": "data type for multidimensional cubes"}, {"name": "pgrouting", "schema": null, "default_version": "3.4.1", "installed_version": null, "comment": "pgRouting Extension"}, {"name": "bloom", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "bloom access method - signature file based index"}, {"name": "pgroong<PERSON>", "schema": null, "default_version": "3.2.5", "installed_version": null, "comment": "Super fast and all languages supported full text search index based on Groonga"}, {"name": "supabase_vault", "schema": "vault", "default_version": "0.3.1", "installed_version": "0.3.1", "comment": "Supabase Vault Extension"}, {"name": "pg_surgery", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "extension to perform surgery on a damaged relation"}, {"name": "btree_gist", "schema": null, "default_version": "1.7", "installed_version": null, "comment": "support for indexing common datatypes in GiST"}, {"name": "refint", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions for implementing referential integrity (obsolete)"}, {"name": "unaccent", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "text search dictionary that removes accents"}, {"name": "lo", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "Large Object maintenance"}, {"name": "xml2", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "XPath querying and XSLT"}, {"name": "tsm_system_time", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "TABLESAMPLE method which accepts time in milliseconds as a limit"}, {"name": "btree_gin", "schema": null, "default_version": "1.3", "installed_version": null, "comment": "support for indexing common datatypes in GIN"}, {"name": "postgis", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "PostGIS geometry and geography spatial types and functions"}, {"name": "pageinspect", "schema": null, "default_version": "1.11", "installed_version": null, "comment": "inspect the contents of database pages at a low level"}, {"name": "intagg", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "integer aggregator and enumerator (obsolete)"}, {"name": "pg_stat_monitor", "schema": null, "default_version": "2.1", "installed_version": null, "comment": "The pg_stat_monitor is a PostgreSQL Query Performance Monitoring tool, based on PostgreSQL contrib module pg_stat_statements. pg_stat_monitor provides aggregated statistics, client information, plan details including plan, and histogram information."}, {"name": "dblink", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "connect to other PostgreSQL databases from within a database"}, {"name": "postgis_raster", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "PostGIS raster types and functions"}, {"name": "dict_int", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "text search dictionary template for integers"}, {"name": "pgmq", "schema": null, "default_version": "1.4.4", "installed_version": null, "comment": "A lightweight message queue. Like AWS SQS and RSMQ but on Postgres."}, {"name": "plls", "schema": null, "default_version": "3.1.10", "installed_version": null, "comment": "PL/LiveScript (v8) trusted procedural language"}, {"name": "earthdistance", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "calculate great-circle distances on the surface of the Earth"}, {"name": "dict_xsyn", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "text search dictionary template for extended synonym processing"}, {"name": "address_standardizer", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "Used to parse an address into constituent elements. Generally used to support geocoding address normalization step."}, {"name": "postgres_fdw", "schema": null, "default_version": "1.1", "installed_version": null, "comment": "foreign-data wrapper for remote PostgreSQL servers"}, {"name": "seg", "schema": null, "default_version": "1.4", "installed_version": null, "comment": "data type for representing line segments or floating-point intervals"}, {"name": "vector", "schema": null, "default_version": "0.8.0", "installed_version": null, "comment": "vector data type and ivfflat and hnsw access methods"}, {"name": "adminpack", "schema": null, "default_version": "2.1", "installed_version": null, "comment": "administrative functions for PostgreSQL"}, {"name": "pg_net", "schema": null, "default_version": "0.14.0", "installed_version": null, "comment": "Async HTTP"}, {"name": "pg_jsonschema", "schema": null, "default_version": "0.3.3", "installed_version": null, "comment": "pg_jsonschema"}, {"name": "pgstattuple", "schema": null, "default_version": "1.5", "installed_version": null, "comment": "show tuple-level statistics"}, {"name": "pgaudit", "schema": null, "default_version": "1.7", "installed_version": null, "comment": "provides auditing functionality"}, {"name": "http", "schema": null, "default_version": "1.6", "installed_version": null, "comment": "HTTP client for PostgreSQL, allows web page retrieval inside the database."}, {"name": "hypopg", "schema": null, "default_version": "1.4.1", "installed_version": null, "comment": "Hypothetical indexes for PostgreSQL"}, {"name": "postgis_tiger_geocoder", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "PostGIS tiger geocoder and reverse geocoder"}, {"name": "pg_tle", "schema": null, "default_version": "1.4.0", "installed_version": null, "comment": "Trusted Language Extensions for PostgreSQL"}, {"name": "plpgsql_check", "schema": null, "default_version": "2.7", "installed_version": null, "comment": "extended check for plpgsql functions"}, {"name": "pg_graphql", "schema": "graphql", "default_version": "1.5.11", "installed_version": "1.5.11", "comment": "pg_graphql: GraphQL support"}, {"name": "pg_freespacemap", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "examine the free space map (FSM)"}, {"name": "postgis_sfcgal", "schema": null, "default_version": "3.3.7", "installed_version": null, "comment": "PostGIS SFCGAL functions"}, {"name": "pg_visibility", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "examine the visibility map (VM) and page-level visibility info"}, {"name": "index_advisor", "schema": null, "default_version": "0.2.0", "installed_version": null, "comment": "Query index advisor"}, {"name": "plv8", "schema": null, "default_version": "3.1.10", "installed_version": null, "comment": "PL/JavaScript (v8) trusted procedural language"}, {"name": "autoinc", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions for autoincrementing fields"}, {"name": "pgsodium", "schema": null, "default_version": "3.1.8", "installed_version": null, "comment": "Postgres extension for libsodium functions"}, {"name": "plcoffee", "schema": null, "default_version": "3.1.10", "installed_version": null, "comment": "PL/CoffeeScript (v8) trusted procedural language"}, {"name": "pg_hashids", "schema": null, "default_version": "1.3", "installed_version": null, "comment": "pg_hashids"}, {"name": "pg_walinspect", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "functions to inspect contents of PostgreSQL Write-Ahead Log"}, {"name": "tsm_system_rows", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "TABLESAMPLE method which accepts number of rows as a limit"}, {"name": "tcn", "schema": null, "default_version": "1.0", "installed_version": null, "comment": "Triggered change notifications"}, {"name": "sslinfo", "schema": null, "default_version": "1.2", "installed_version": null, "comment": "information about SSL certificates"}]