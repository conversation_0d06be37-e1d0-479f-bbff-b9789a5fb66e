-- Function to update last_message_timestamp on channels or direct_message_sessions
CREATE OR REPLACE FUNCTION public.update_conversation_last_message_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.channel_id IS NOT NULL THEN
        UPDATE public.channels
        SET last_message_timestamp = NEW.timestamp
        WHERE id = NEW.channel_id;
    ELSIF NEW.dm_id IS NOT NULL THEN
        UPDATE public.direct_message_sessions
        SET last_message_timestamp = NEW.timestamp
        WHERE id = NEW.dm_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to execute the function after a new message is inserted
CREATE TRIGGER trigger_update_conversation_last_message_timestamp
AFTER INSERT ON public.messages
FOR EACH ROW
EXECUTE FUNCTION public.update_conversation_last_message_timestamp();