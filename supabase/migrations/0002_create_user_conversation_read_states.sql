-- Create the user_conversation_read_states table
CREATE TABLE public.user_conversation_read_states (
    user_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
    conversation_id UUID NOT NULL,
    conversation_type TEXT NOT NULL CHECK (conversation_type IN ('channel', 'dm')),
    last_read_message_timestamp TIMESTAMPTZ,
    CONSTRAINT user_conversation_read_states_pkey PRIMARY KEY (user_id, conversation_id)
);

-- Add comments to the table and columns
COMMENT ON TABLE public.user_conversation_read_states IS 'Stores the last read message timestamp for a user in a specific conversation (channel or DM).';
COMMENT ON COLUMN public.user_conversation_read_states.user_id IS 'Foreign key referencing the user who read the message.';
COMMENT ON COLUMN public.user_conversation_read_states.conversation_id IS 'Identifier for the conversation (channel_id or dm_id).';
COMMENT ON COLUMN public.user_conversation_read_states.conversation_type IS 'Type of conversation: ''channel'' or ''dm''.';
COMMENT ON COLUMN public.user_conversation_read_states.last_read_message_timestamp IS 'Timestamp of the last message read by the user in this conversation.';

-- Create an index on user_id for faster lookups of a user''s read states
CREATE INDEX idx_user_conversation_read_states_user_id ON public.user_conversation_read_states(user_id);

-- Enable Row Level Security (RLS)
ALTER TABLE public.user_conversation_read_states ENABLE ROW LEVEL SECURITY;

-- RLS Policies

-- SELECT Policy: Users can read their own read states
CREATE POLICY "Users can read their own read states"
ON public.user_conversation_read_states
FOR SELECT
USING (auth.uid() = user_id);

-- INSERT Policy: Users can insert their own read states
CREATE POLICY "Users can insert their own read states"
ON public.user_conversation_read_states
FOR INSERT
WITH CHECK (auth.uid() = user_id);

-- UPDATE Policy: Users can update their own read states
CREATE POLICY "Users can update their own read states"
ON public.user_conversation_read_states
FOR UPDATE
USING (auth.uid() = user_id)
WITH CHECK (auth.uid() = user_id);

-- DELETE Policy: Users can delete their own read states
CREATE POLICY "Users can delete their own read states"
ON public.user_conversation_read_states
FOR DELETE
USING (auth.uid() = user_id);