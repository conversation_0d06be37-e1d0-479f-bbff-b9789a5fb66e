[{"version": "20250521060900", "name": "create_profile_on_signup"}, {"version": "20250521102543", "name": "update-channel-creation-rls-policy-v2"}, {"version": "20250521102724", "name": "qualify-rls-helper-function-calls"}, {"version": "20250521102953", "name": "refine-is-workspace-member-search-path"}, {"version": "20250521103204", "name": "inline-rls-check-for-workspaces-select"}, {"version": "20250521103511", "name": "temp_debug_rls_workspace_users_select_true"}, {"version": "20250521103556", "name": "revert_debug_rls_workspace_users_select"}, {"version": "20250521103608", "name": "temp_debug_rls_sections_select_true"}, {"version": "20250521103647", "name": "revert_debug_rls_sections_select"}, {"version": "20250521103655", "name": "inline_rls_logic_workspace_users_select"}, {"version": "20250521103717", "name": "revert_inline_rls_logic_workspace_users"}, {"version": "20250521103724", "name": "inline_rls_logic_sections_select"}, {"version": "20250521103808", "name": "fix_recursion_inline_rls_workspace_users"}, {"version": "20250521103918", "name": "simplify_rls_workspace_users_self_only"}, {"version": "20250521110404", "name": "temp_rls_workspaces_select_all_true"}, {"version": "20250521110410", "name": "temp_rls_workspace_users_select_all_true"}, {"version": "20250521110415", "name": "temp_rls_sections_select_all_true"}, {"version": "20250521110428", "name": "temp_rls_channels_select_all_true"}, {"version": "20250521110440", "name": "temp_rls_channel_members_select_all_true"}, {"version": "20250521110522", "name": "temp_rls_messages_select_all_true"}, {"version": "20250521110535", "name": "temp_rls_profiles_select_all_true"}, {"version": "20250521110621", "name": "restore_rls_workspaces_inlined"}, {"version": "20250521110639", "name": "restore_rls_workspace_users_inlined"}, {"version": "20250521110756", "name": "retest_simplify_rls_workspace_users_self_only"}, {"version": "20250521112921", "name": "restore_rls_sections_inlined"}, {"version": "20250521112952", "name": "restore_rls_channels_inlined_ws_access"}, {"version": "20250522062705", "name": "temp_rls_dm_sessions_select_all_true"}, {"version": "20250522062730", "name": "fix_rls_dm_sessions_select_inlined"}, {"version": "20250522065924", "name": "update_handle_new_user_trigger_name_key"}, {"version": "20250522071105", "name": "create_func_is_allowed_to_send_to_channel_v2"}, {"version": "20250522071115", "name": "create_func_is_allowed_to_send_to_dm"}, {"version": "20250522071850", "name": "update_messages_insert_rls_policy_v2"}, {"version": "20250522072033", "name": "create_send_message_rpc_and_update_rls"}, {"version": "20250522072113", "name": "update_send_message_rpc_for_topic_id"}, {"version": "20250522072412", "name": "update_send_message_rpc_remove_workspace_id_insert"}, {"version": "20250522080554", "name": "update_messages_select_rls_for_dms"}, {"version": "20250522123110", "name": "restore_rls_channel_members_inlined_ws_access"}, {"version": "20250522123149", "name": "restore_rls_messages_inlined_ws_access"}, {"version": "20250522123216", "name": "restore_rls_profiles_auth_only"}, {"version": "20250522123631", "name": "fix_insert_channel_rls_inlined"}, {"version": "20250522123917", "name": "fix_insert_message_rls_inlined"}, "migrations/0001_update_last_message_timestamp_trigger.sql", "migrations/0002_create_user_conversation_read_states.sql"]