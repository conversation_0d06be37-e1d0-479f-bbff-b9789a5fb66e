-- RLS Policies for public.channel_members

-- Policy: Users can view channel members
-- Roles: public
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM (channels c
--      JOIN workspace_users wu ON ((c.workspace_id = wu.workspace_id)))
--   WHERE ((c.id = channel_members.channel_id) AND (wu.user_id = auth.uid()))))
-- With Check: null
ALTER TABLE public.channel_members ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view channel members" ON public.channel_members;
CREATE POLICY "Users can view channel members"
ON public.channel_members
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM (channels c
     JOIN workspace_users wu ON ((c.workspace_id = wu.workspace_id)))
  WHERE ((c.id = channel_members.channel_id) AND (wu.user_id = auth.uid()))));

-- Policy: Workspace admins can delete channel members
-- Roles: public
-- Command: DELETE
-- Using: (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))))
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete channel members" ON public.channel_members;
CREATE POLICY "Workspace admins can delete channel members"
ON public.channel_members
FOR DELETE
TO authenticated
USING (EXISTS ( SELECT 1
   FROM channels c
  WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))));

-- Policy: Workspace admins can manage channel members
-- Roles: public
-- Command: INSERT
-- Using: null
-- With Check: (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))))
DROP POLICY IF EXISTS "Workspace admins can manage channel members" ON public.channel_members;
CREATE POLICY "Workspace admins can manage channel members"
ON public.channel_members
FOR INSERT
TO authenticated
WITH CHECK (EXISTS ( SELECT 1
   FROM channels c
  WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))));
