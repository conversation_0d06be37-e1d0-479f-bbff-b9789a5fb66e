-- RLS Policies for public.channels

-- Policy: Users can view channels they have access to
-- Roles: public
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM workspace_users wu
--   WHERE ((wu.workspace_id = channels.workspace_id) AND (wu.user_id = auth.uid()))))
-- With Check: null
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view channels they have access to" ON public.channels;
CREATE POLICY "Users can view channels they have access to"
ON public.channels
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM workspace_users wu
  WHERE ((wu.workspace_id = channels.workspace_id) AND (wu.user_id = auth.uid()))));

-- Policy: Workspace admins can delete channels
-- Roles: public
-- Command: DELETE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete channels" ON public.channels;
CREATE POLICY "Workspace admins can delete channels"
ON public.channels
FOR DELETE
TO public
USING (is_workspace_admin(workspace_id));

-- Policy: Workspace admins can update channels
-- Roles: public
-- Command: UPDATE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can update channels" ON public.channels;
CREATE POLICY "Workspace admins can update channels"
ON public.channels
FOR UPDATE
TO public
USING (is_workspace_admin(workspace_id));

-- Policy: Workspace members can create channels
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (EXISTS ( SELECT 1
--    FROM workspace_users wu
--   WHERE ((wu.workspace_id = channels.workspace_id) AND (wu.user_id = auth.uid()))))
DROP POLICY IF EXISTS "Workspace members can create channels" ON public.channels;
CREATE POLICY "Workspace members can create channels"
ON public.channels
FOR INSERT
TO authenticated
WITH CHECK (EXISTS ( SELECT 1
   FROM workspace_users wu
  WHERE ((wu.workspace_id = channels.workspace_id) AND (wu.user_id = auth.uid()))));
