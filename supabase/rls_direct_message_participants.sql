-- RLS Policies for public.direct_message_participants

-- Policy: Users can add themselves to DM sessions
-- Roles: public
-- Command: INSERT
-- Using: null
-- With Check: (user_id = auth.uid())
ALTER TABLE public.direct_message_participants ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can add themselves to DM sessions" ON public.direct_message_participants;
CREATE POLICY "Users can add themselves to DM sessions"
ON public.direct_message_participants
FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Policy: Users can view DM participants
-- Roles: public
-- Command: SELECT
-- Using: is_dm_participant(dm_id)
-- With Check: null
DROP POLICY IF EXISTS "Users can view DM participants" ON public.direct_message_participants;
CREATE POLICY "Users can view DM participants"
ON public.direct_message_participants
FOR SELECT
TO authenticated
USING (is_dm_participant(dm_id));
