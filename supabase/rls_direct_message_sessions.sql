-- RLS Policies for public.direct_message_sessions

-- Policy: Users can create DM sessions
-- Roles: public
-- Command: INSERT
-- Using: null
-- With Check: true
ALTER TABLE public.direct_message_sessions ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can create DM sessions" ON public.direct_message_sessions;
CREATE POLICY "Users can create DM sessions"
ON public.direct_message_sessions
FOR INSERT
TO authenticated
WITH CHECK (true);

-- Policy: Users can view DM sessions they participate in
-- Roles: public
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM direct_message_participants dmp
--   WHERE ((dmp.dm_id = direct_message_sessions.id) AND (dmp.user_id = auth.uid()))))
-- With Check: null
DROP POLICY IF EXISTS "Users can view DM sessions they participate in" ON public.direct_message_sessions;
CREATE POLICY "Users can view DM sessions they participate in"
ON public.direct_message_sessions
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM direct_message_participants dmp
  WHERE ((dmp.dm_id = direct_message_sessions.id) AND (dmp.user_id = auth.uid()))));
