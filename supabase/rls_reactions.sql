-- RLS Policies for public.reactions

-- Policy: Users can add reactions to messages they can see
-- Roles: public
-- Command: INSERT
-- Using: null
-- With Check: ((user_id = auth.uid()) AND (EXISTS ( SELECT 1
--    FROM messages m
--   WHERE ((m.id = reactions.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id)))))))
ALTER TABLE public.reactions ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can add reactions to messages they can see" ON public.reactions;
CREATE POLICY "Users can add reactions to messages they can see"
ON public.reactions
FOR INSERT
TO authenticated
WITH CHECK ((user_id = auth.uid()) AND (EXISTS ( SELECT 1
   FROM messages m
  WHERE ((m.id = reactions.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id)))))));

-- Policy: Users can delete their own reactions
-- Roles: public
-- Command: DELETE
-- Using: (user_id = auth.uid())
-- With Check: null
DROP POLICY IF EXISTS "Users can delete their own reactions" ON public.reactions;
CREATE POLICY "Users can delete their own reactions"
ON public.reactions
FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Policy: Users can view reactions on messages they can see
-- Roles: public
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM messages m
--   WHERE ((m.id = reactions.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id))))))
-- With Check: null
DROP POLICY IF EXISTS "Users can view reactions on messages they can see" ON public.reactions;
CREATE POLICY "Users can view reactions on messages they can see"
ON public.reactions
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM messages m
  WHERE ((m.id = reactions.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id))))));
