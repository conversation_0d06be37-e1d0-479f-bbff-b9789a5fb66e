-- <PERSON><PERSON> Policies for public.workspace_users

-- Policy: Users can view workspace members
-- Roles: authenticated
-- Command: SELECT
-- Using: EXISTS (SELECT 1 FROM public.workspace_users wu_self WHERE wu_self.workspace_id = public.workspace_users.workspace_id AND wu_self.user_id = auth.uid())
-- With Check: null
ALTER TABLE public.workspace_users ENABLE ROW LEVEL SECURITY;
-- Drop existing policy first
DROP POLICY IF EXISTS "Users can view workspace members" ON public.workspace_users;
DROP POLICY IF EXISTS "Users can view their own workspace memberships" ON public.workspace_users; -- Drop old name too if it exists

-- Recreate with the most basic, non-recursive SELECT policy
CREATE POLICY "Users can view their own workspace memberships"
ON public.workspace_users FOR SELECT TO authenticated
USING (auth.uid() = user_id);

-- Policy: Workspace admins can delete workspace members
-- Roles: public
-- Command: DELETE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete workspace members" ON public.workspace_users;
CREATE POLICY "Workspace admins can delete workspace members"
ON public.workspace_users
FOR DELETE
TO public
USING (is_workspace_admin(workspace_id));

-- Policy: Workspace admins can manage workspace members
-- Roles: public
-- Command: INSERT
-- Using: null
-- With Check: is_workspace_admin(workspace_id)
DROP POLICY IF EXISTS "Workspace admins can manage workspace members" ON public.workspace_users;
CREATE POLICY "Workspace admins can manage workspace members"
ON public.workspace_users
FOR INSERT
TO public
WITH CHECK (is_workspace_admin(workspace_id));

-- Policy: Workspace admins can update workspace members
-- Roles: public
-- Command: UPDATE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can update workspace members" ON public.workspace_users;
CREATE POLICY "Workspace admins can update workspace members"
ON public.workspace_users
FOR UPDATE
TO public
USING (is_workspace_admin(workspace_id));
