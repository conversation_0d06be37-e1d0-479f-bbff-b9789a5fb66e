-- Database Functions (RPC)

-- Function: public.is_allowed_to_send_to_channel(p_channel_id uuid, p_user_id uuid)
CREATE OR REPLACE FUNCTION public.is_allowed_to_send_to_channel(p_channel_id uuid, p_user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN (
    EXISTS (
      SELECT 1 FROM public.workspace_users wu
      JOIN public.channels ch ON wu.workspace_id = ch.workspace_id
      WHERE ch.id = p_channel_id AND wu.user_id = p_user_id
    )
    OR
    EXISTS (
      SELECT 1 FROM public.channel_members cm
      WHERE cm.channel_id = p_channel_id AND cm.user_id = p_user_id
    )
  );
END;
$function$
;

-- Function: public.handle_new_user()
CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETUR<PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  INSERT INTO public.profiles (id, name, avatar_url, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email), -- Changed full_name to name
    NEW.raw_user_meta_data->>'avatar_url',
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$function$
;

-- Function: public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid)
CREATE OR REPLACE FUNCTION public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid)
 RETURNS messages
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_sender_id UUID := auth.uid();
  v_new_message public.messages;
BEGIN
  -- Validate parameters: either channel_id or dm_id must be provided, but not both.
  IF (p_channel_id IS NULL AND p_dm_id IS NULL) OR (p_channel_id IS NOT NULL AND p_dm_id IS NOT NULL) THEN
    RAISE EXCEPTION 'Either p_channel_id or p_dm_id must be provided, but not both.';
  END IF;

  -- Perform permission checks
  IF p_channel_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_channel(p_channel_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to channel %', v_sender_id, p_channel_id;
    END IF;
  ELSIF p_dm_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_dm(p_dm_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to DM %', v_sender_id, p_dm_id;
    END IF;
  END IF;

  -- Insert the message
  INSERT INTO public.messages (user_id, content, workspace_id, channel_id, dm_id, parent_message_id)
  VALUES (v_sender_id, p_content, p_workspace_id, p_channel_id, p_dm_id, p_parent_message_id)
  RETURNING * INTO v_new_message;

  RETURN v_new_message;
END;
$function$
;

-- Function: public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid, p_topic_id uuid DEFAULT NULL::uuid)
CREATE OR REPLACE FUNCTION public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid, p_topic_id uuid DEFAULT NULL::uuid)
 RETURNS messages
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_sender_id UUID := auth.uid();
  v_new_message public.messages;
BEGIN
  IF (p_channel_id IS NULL AND p_dm_id IS NULL) OR (p_channel_id IS NOT NULL AND p_dm_id IS NOT NULL) THEN
    RAISE EXCEPTION 'Either p_channel_id or p_dm_id must be provided, but not both.';
  END IF;

  -- Permission checks (can still use p_workspace_id if needed by helper functions)
  IF p_channel_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_channel(p_channel_id, v_sender_id) THEN -- Assuming is_allowed_to_send_to_channel might internally use workspace context if needed
      RAISE EXCEPTION 'User % is not allowed to send messages to channel %', v_sender_id, p_channel_id;
    END IF;
  ELSIF p_dm_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_dm(p_dm_id, v_sender_id) THEN -- Assuming is_allowed_to_send_to_dm might internally use workspace context if needed
      RAISE EXCEPTION 'User % is not allowed to send messages to DM %', v_sender_id, p_dm_id;
    END IF;
  END IF;

  -- Insert the message without workspace_id
  INSERT INTO public.messages (user_id, content, channel_id, dm_id, parent_message_id, topic_id)
  VALUES (v_sender_id, p_content, p_channel_id, p_dm_id, p_parent_message_id, p_topic_id)
  RETURNING * INTO v_new_message;

  RETURN v_new_message;
END;
$function$
;

-- Function: public.is_workspace_member(workspace_id uuid)
CREATE OR REPLACE FUNCTION public.is_workspace_member(workspace_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.workspace_users wu
    WHERE wu.workspace_id = public.is_workspace_member.workspace_id -- Parameter qualified by function name
      AND wu.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_workspace_admin(workspace_id uuid)
CREATE OR REPLACE FUNCTION public.is_workspace_admin(workspace_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.workspace_users wu
    WHERE wu.workspace_id = $1 AND wu.user_id = auth.uid() AND wu.role = 'admin'
  );
END;
$function$
;

-- Function: public.is_dm_participant(dm_id uuid)
CREATE OR REPLACE FUNCTION public.is_dm_participant(dm_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.direct_message_participants dmp
    WHERE dmp.dm_id = $1 AND dmp.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_channel_member(channel_id uuid)
CREATE OR REPLACE FUNCTION public.is_channel_member(channel_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  _is_private BOOLEAN;
  _workspace_id UUID;
BEGIN
  -- Get channel info
  SELECT c.is_private, c.workspace_id INTO _is_private, _workspace_id
  FROM public.channels c WHERE c.id = channel_id;
  
  -- If channel is not private, check if user is workspace member
  IF NOT _is_private THEN
    RETURN public.is_workspace_member(_workspace_id);
  END IF;
  
  -- If channel is private, check if user is channel member
  RETURN EXISTS (
    SELECT 1 FROM public.channel_members cm
    WHERE cm.channel_id = $1 AND cm.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_allowed_to_send_to_dm(p_dm_id uuid, p_user_id uuid)
CREATE OR REPLACE FUNCTION public.is_allowed_to_send_to_dm(p_dm_id uuid, p_user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.direct_message_participants dmp -- schema-qualified
    WHERE dmp.dm_id = p_dm_id AND dmp.user_id = p_user_id
  );
END;
$function$
;

-- Function: public.mark_conversation_as_read(p_conversation_id uuid, p_conversation_type text, p_last_read_message_timestamp timestamptz)
CREATE OR REPLACE FUNCTION public.mark_conversation_as_read(
    p_conversation_id UUID,
    p_conversation_type TEXT,
    p_last_read_message_timestamp TIMESTAMPTZ
)
RETURNS VOID
LANGUAGE plpgsql
VOLATILE
SECURITY DEFINER
SET search_path TO 'public' -- Ensure correct schema context
AS $function$
BEGIN
    -- Optional: Add a CHECK constraint for p_conversation_type if not handled by application
    -- IF p_conversation_type NOT IN ('channel', 'dm') THEN
    --     RAISE EXCEPTION 'Invalid conversation_type: %. Must be ''channel'' or ''dm''.', p_conversation_type;
    -- END IF;

    INSERT INTO public.user_conversation_read_states (
        user_id,
        conversation_id,
        conversation_type,
        last_read_message_timestamp
    )
    VALUES (
        auth.uid(),
        p_conversation_id,
        p_conversation_type,
        p_last_read_message_timestamp
    )
    ON CONFLICT (user_id, conversation_id)
    DO UPDATE SET
        last_read_message_timestamp = EXCLUDED.last_read_message_timestamp,
        conversation_type = EXCLUDED.conversation_type;
END;
$function$
;
