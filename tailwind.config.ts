
import type { Config } from "tailwindcss";

export default {
	darkMode: ["class"],
	content: [
		"./pages/**/*.{ts,tsx}",
		"./components/**/*.{ts,tsx}",
		"./app/**/*.{ts,tsx}",
		"./src/**/*.{ts,tsx}",
	],
	prefix: "",
	theme: {
		container: {
			center: true,
			padding: '2rem',
			screens: {
				'2xl': '1400px'
			}
		},
		extend: {
			colors: {
				border: 'hsl(var(--border))',
				input: 'hsl(var(--input))',
				ring: 'hsl(var(--ring))',
				background: 'hsl(var(--background))',
				foreground: 'hsl(var(--foreground))',
				primary: {
					DEFAULT: 'hsl(var(--primary))',
					foreground: 'hsl(var(--primary-foreground))'
				},
				secondary: {
					DEFAULT: 'hsl(var(--secondary))',
					foreground: 'hsl(var(--secondary-foreground))'
				},
				destructive: {
					DEFAULT: 'hsl(var(--destructive))',
					foreground: 'hsl(var(--destructive-foreground))'
				},
				muted: {
					DEFAULT: 'hsl(var(--muted))',
					foreground: 'hsl(var(--muted-foreground))'
				},
				accent: {
					DEFAULT: 'hsl(var(--accent))',
					foreground: 'hsl(var(--accent-foreground))'
				},
				popover: {
					DEFAULT: 'hsl(var(--popover))',
					foreground: 'hsl(var(--popover-foreground))'
				},
				card: {
					DEFAULT: 'hsl(var(--card))',
					foreground: 'hsl(var(--card-foreground))'
				},
				sidebar: {
					DEFAULT: 'hsl(var(--sidebar-background))',
					foreground: 'hsl(var(--sidebar-foreground))',
					primary: 'hsl(var(--sidebar-primary))',
					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
					accent: 'hsl(var(--sidebar-accent))',
					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
					border: 'hsl(var(--sidebar-border))',
					ring: 'hsl(var(--sidebar-ring))'
				},
				app: {
					sidebar: 'var(--app-sidebar)',
					active: 'var(--app-active)',
					hover: 'var(--app-hover-bg)', // Note: global-styles.css uses --app-hover-bg
					border: 'var(--app-border)',
					text: 'var(--app-text)',
					highlight: 'var(--app-highlight)',
					'active-text': 'var(--app-active-text)', // Added for completeness
					mention: '#ECB22E', // This one seems specific, might not be themed
					'user-section': 'var(--app-user-section)', // Removed fallback, rely on :root
					'user-section-hover': 'var(--app-user-section-hover, var(--app-hover-bg))', // Fallback to hover-bg
					'selected-item': 'var(--app-selected-item)' // Removed fallback
				}
			},
			fontFamily: {
				sans: ['Inter', 'sans-serif'],
			},
			borderRadius: {
				lg: 'var(--radius)',
				md: 'calc(var(--radius) - 2px)',
				sm: 'calc(var(--radius) - 4px)'
			},
			keyframes: {
				'accordion-down': {
					from: {
						height: '0'
					},
					to: {
						height: 'var(--radix-accordion-content-height)'
					}
				},
				'accordion-up': {
					from: {
						height: 'var(--radix-accordion-content-height)'
					},
					to: {
						height: '0'
					}
				},
				'fade-in': {
					from: {
						opacity: '0'
					},
					to: {
						opacity: '1'
					}
				},
				'slide-in': {
					from: {
						transform: 'translateX(-50%)'
					},
					to: {
						transform: 'translateX(0)'
					}
				}
			},
			animation: {
				'accordion-down': 'accordion-down 0.2s ease-out',
				'accordion-up': 'accordion-up 0.2s ease-out',
				'fade-in': 'fade-in 0.3s ease-out',
				'slide-in': 'slide-in 0.3s ease-out',
			},
			backgroundImage: {
				'gradient-aqua-dream': 'linear-gradient(to bottom, #69d2e7, #a7dbd8)',
				'gradient-sea-glass': 'linear-gradient(to bottom, #69d2e7, #a7dbd8, #bce2e8)',
				'gradient-pb-and-j': 'linear-gradient(to bottom, #e5776c, #f9d2cb)',
				'gradient-sky-blue': 'linear-gradient(to bottom, #4c9fd5, #76cdea)',
				'gradient-meadow-green': 'linear-gradient(to bottom, #5d7b42, #a4bc76)',
				'gradient-lemon-lime': 'linear-gradient(to bottom, #a8c66c, #ffde17)',
				'gradient-chill-vibes': 'linear-gradient(to bottom, #0a6c7a, #3cbeb0)',
				'gradient-coral-sunrise': 'linear-gradient(to bottom, #f5b5cf, #ffaaa7)',
				'gradient-deep-ocean': 'linear-gradient(to bottom, #1d2b53, #7e9ce0)',
			}
		}
	},
	plugins: [
		require("tailwindcss-animate"),
		require("@tailwindcss/typography")
	],
} satisfies Config;
